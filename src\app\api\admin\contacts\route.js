import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Contact from '@/models/Contact';

export async function GET(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const sortField = searchParams.get('sort') || 'createdAt';
    const sortDirection = searchParams.get('direction') === 'asc' ? 1 : -1;
    const search = searchParams.get('search');
    const dateFilter = searchParams.get('dateFilter');

    // Build query
    let query = {};

    // Search by name, email, or subject if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    // Apply date filter if provided
    if (dateFilter) {
      const now = new Date();
      let dateQuery = {};

      switch (dateFilter) {
        case 'today':
          const today = new Date(now);
          today.setHours(0, 0, 0, 0);
          dateQuery = { createdAt: { $gte: today } };
          break;
        case 'week':
          const weekAgo = new Date(now);
          weekAgo.setDate(weekAgo.getDate() - 7);
          dateQuery = { createdAt: { $gte: weekAgo } };
          break;
        case 'month':
          const monthAgo = new Date(now);
          monthAgo.setDate(monthAgo.getDate() - 30);
          dateQuery = { createdAt: { $gte: monthAgo } };
          break;
        case 'year':
          const yearAgo = new Date(now);
          yearAgo.setFullYear(yearAgo.getFullYear() - 1);
          dateQuery = { createdAt: { $gte: yearAgo } };
          break;
      }

      // Merge with existing query
      query = { ...query, ...dateQuery };
    }

    // Count total documents for pagination
    const total = await Contact.countDocuments(query);

    // Build sort object
    const sort = { [sortField]: sortDirection };

    // Fetch paginated contacts
    const contacts = await Contact.find(query)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    // Calculate statistics for the dashboard
    const now = new Date();
    const today = new Date(now);
    today.setHours(0, 0, 0, 0);
    const weekAgo = new Date(now);
    weekAgo.setDate(weekAgo.getDate() - 7);
    const monthAgo = new Date(now);
    monthAgo.setDate(monthAgo.getDate() - 30);

    // Only fetch all contacts for statistics if specifically requested or if the total is small
    let allContacts = null;
    if (searchParams.get('includeStats') === 'true' || total <= 100) {
      allContacts = await Contact.find({}).sort({ createdAt: -1 });
    }

    return NextResponse.json({
      contacts,
      total,
      pages: Math.ceil(total / limit),
      allContacts
    });
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contacts' },
      { status: 500 }
    );
  }
}

// Handle message deletion (single or bulk)
export async function DELETE(request) {
  try {
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectToDB();
    const data = await request.json();

    // Handle bulk deletion
    if (data.ids && Array.isArray(data.ids)) {
      const result = await Contact.deleteMany({ _id: { $in: data.ids } });
      return NextResponse.json({
        message: `${result.deletedCount} contact messages deleted successfully`
      });
    }

    // Handle single deletion
    if (data.id) {
      const result = await Contact.findByIdAndDelete(data.id);
      if (!result) {
        return NextResponse.json(
          { error: 'Message not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ message: 'Contact message deleted successfully' });
    }

    return NextResponse.json(
      { error: 'No valid ID provided' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error deleting contact:', error);
    return NextResponse.json(
      { error: 'Failed to delete contact message' },
      { status: 500 }
    );
  }
}