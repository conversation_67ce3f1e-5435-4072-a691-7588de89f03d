'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { FiHeart, FiX, FiShoppingBag, FiSearch, FiFilter } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { useCart } from '@/context/CartContext';
import ProductsLoading from './ProductsLoading';

export default function ProductsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isInWishlist, addToWishlist, removeFromWishlist, addToCart } = useCart();

  // Get category from URL params
  const urlCategory = searchParams.get('category');

  // Update initial state to use URL category
  const [selectedCategory, setSelectedCategory] = useState('ALL');
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [addingToCart, setAddingToCart] = useState({});

  // New state for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [sortBy, setSortBy] = useState('newest');
  const [availability, setAvailability] = useState('all');

  // Effect to handle URL parameters
  useEffect(() => {
    if (urlCategory) {
      // Convert URL format (e.g., 'living-room') to display format (e.g., 'LIVING ROOM')
      const formattedCategory = urlCategory
        .split('-')
        .map(word => word.toUpperCase())
        .join(' ');
      setSelectedCategory(formattedCategory);
    }
  }, [urlCategory]);

  // Update URL when category changes and close filter panel
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    // Close the filter panel
    setShowFilters(false);

    // Update URL
    if (category === 'ALL') {
      router.push('/products');
    } else {
      const urlFormat = category.toLowerCase().replace(' ', '-');
      router.push(`/products?category=${urlFormat}`);
    }
  };

  const categories = [
    'ALL',
    'LIVING ROOM',
    'BEDROOM',
    'DINING ROOM',
    'OFFICE',
    'OUTDOOR',
    'DECOR'
  ];

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedCategory('ALL');
    setPriceRange({ min: '', max: '' });
    setSortBy('newest');
    setAvailability('all');
    setShowFilters(false);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (selectedCategory !== 'ALL') count++;
    if (priceRange.min || priceRange.max) count++;
    if (sortBy !== 'newest') count++;
    if (availability !== 'all') count++;
    return count;
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (!response.ok) throw new Error('Failed to fetch products');
      const data = await response.json();
      setProducts(data);
    } catch (error) {
      setError('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort products
  const getFilteredProducts = () => {
    let filtered = [...products];

    // Search filter with prioritization
    if (searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      const searchTerms = searchTerm.split(' ');

      filtered = filtered.filter(product => {
        const productName = product.name.toLowerCase();
        const productCategory = product.category.toLowerCase();
        const productDescription = product.description.toLowerCase();

        // Calculate relevance score for sorting
        let score = 0;

        // Exact category match (highest priority)
        if (productCategory === searchTerm) score += 100;
        if (productCategory.includes(searchTerm)) score += 50;

        // Exact product name match (high priority)
        if (productName === searchTerm) score += 80;
        if (productName.includes(searchTerm)) score += 40;

        // Individual terms matching (lower priority)
        const matchesAllTerms = searchTerms.every(term =>
          productName.includes(term) ||
          productCategory.includes(term) ||
          productDescription.includes(term)
        );

        if (matchesAllTerms) score += 20;

        // Store the score on the product object
        product._searchScore = score;

        return score > 0;
      });

      // Sort by search relevance score
      filtered.sort((a, b) => b._searchScore - a._searchScore);
    }

    // Category filter - improved to handle URL format
    if (selectedCategory !== 'ALL') {
      filtered = filtered.filter(product => {
        const productCategory = product.category.toUpperCase();
        return productCategory === selectedCategory;
      });
    }

    // Price range filter
    if (priceRange.min !== '') {
      filtered = filtered.filter(product => product.price >= Number(priceRange.min));
    }
    if (priceRange.max !== '') {
      filtered = filtered.filter(product => product.price <= Number(priceRange.max));
    }

    // Availability filter
    if (availability === 'inStock') {
      filtered = filtered.filter(product => product.stock > 0);
    } else if (availability === 'outOfStock') {
      filtered = filtered.filter(product => product.stock === 0);
    }

    // Only apply sorting if there's no search query
    if (!searchQuery.trim()) {
      switch (sortBy) {
        case 'priceLowToHigh':
          filtered.sort((a, b) => a.price - b.price);
          break;
        case 'priceHighToLow':
          filtered.sort((a, b) => b.price - a.price);
          break;
        case 'newest':
          filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          break;
        default:
          break;
      }
    }

    return filtered;
  };

  const handleToggleWishlist = async (e, product) => {
    e.stopPropagation();
    try {
      if (isInWishlist(product._id)) {
        const success = await removeFromWishlist(product._id);
        if (success) {
          toast.success('Removed from wishlist');
        } else {
          toast.error('Failed to remove from wishlist');
        }
      } else {
        const success = await addToWishlist(product);
        if (success) {
          toast.success('Added to wishlist');
        } else {
          toast.error('Failed to add to wishlist');
        }
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast.error('Failed to update wishlist');
    }
  };

  const handleAddToCart = async (e, product) => {
    e.stopPropagation();

    // Prevent adding out-of-stock items
    if (product.stock === 0) {
      toast.error('This product is out of stock');
      return;
    }

    setAddingToCart(prev => ({ ...prev, [product._id]: true }));
    try {
      const success = await addToCart(product);
      if (success) {
        toast.success('Added to cart');
      } else {
        toast.error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add to cart');
    } finally {
      setAddingToCart(prev => ({ ...prev, [product._id]: false }));
    }
  };

  if (loading) {
    return (
      <ProductsLoading />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white pt-16">
        <p className="text-center text-sm uppercase tracking-wider py-8">{error}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-0">
      {/* Header with Search and Filters */}
      <div className="sticky top-16 bg-white z-20 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          {/* Main Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 gap-4">
            <h1 className="text-xl font-light tracking-wide uppercase">
              {selectedCategory === 'ALL' ? 'All Products' : selectedCategory}
            </h1>

            {/* Search and Filter Controls */}
            <div className="flex items-center gap-4 order-first sm:order-none">
              {/* Search Bar */}
              <div className="flex-1 sm:w-[200px] relative">
                <input
                  type="text"
                  placeholder="SEARCH"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-9 pr-4 py-2 border-b border-black/10 focus:outline-none focus:border-black bg-transparent text-xs uppercase"
                />
                <FiSearch className="absolute left-0 top-1/2 transform -translate-y-1/2 text-black/60 w-4 h-4" />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 text-black/60 hover:text-black"
                  >
                    <FiX className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Filter Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-2 text-xs uppercase tracking-wider transition-colors
                  ${showFilters
                    ? 'bg-black text-white'
                    : 'hover:bg-black/5'
                  }`}
              >
                <FiFilter className="w-4 h-4" />
                <span>Filter</span>
                <span className="text-[10px] ml-1">
                  ({getActiveFiltersCount()})
                </span>
              </button>
            </div>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <div className="py-4 border-t border-black/5 animate-fadeIn">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Categories */}
                <div>
                  <p className="text-xs uppercase tracking-wider mb-3">Category</p>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <button
                        key={category}
                        onClick={() => handleCategoryChange(category)}
                        className={`block text-sm ${
                          selectedCategory === category
                            ? 'text-black font-medium'
                            : 'text-gray-500 hover:text-black'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <p className="text-xs uppercase tracking-wider mb-3">Price Range</p>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <input
                        type="number"
                        placeholder="Min"
                        value={priceRange.min}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            setShowFilters(false);
                          }
                        }}
                        className="w-full px-3 py-2 border-b border-black/10 focus:outline-none focus:border-black bg-transparent text-sm"
                      />
                      <span className="text-sm">-</span>
                      <input
                        type="number"
                        placeholder="Max"
                        value={priceRange.max}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            setShowFilters(false);
                          }
                        }}
                        className="w-full px-3 py-2 border-b border-black/10 focus:outline-none focus:border-black bg-transparent text-sm"
                      />
                    </div>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="text-xs uppercase tracking-wider hover:opacity-70 transition-opacity"
                    >
                      Apply Filter
                    </button>
                  </div>
                </div>

                {/* Sort and Availability */}
                <div className="space-y-6">
                  {/* Sort Options */}
                  <div>
                    <p className="text-xs uppercase tracking-wider mb-3">Sort By</p>
                    <select
                      value={sortBy}
                      onChange={(e) => {
                        setSortBy(e.target.value);
                        setShowFilters(false);
                      }}
                      className="w-full px-3 py-2 border-b border-black/10 focus:outline-none focus:border-black bg-transparent text-sm"
                    >
                      <option value="newest">Newest</option>
                      <option value="priceLowToHigh">Price: Low to High</option>
                      <option value="priceHighToLow">Price: High to Low</option>
                    </select>
                  </div>

                  {/* Availability */}
                  <div>
                    <p className="text-xs uppercase tracking-wider mb-3">Availability</p>
                    <select
                      value={availability}
                      onChange={(e) => {
                        setAvailability(e.target.value);
                        setShowFilters(false);
                      }}
                      className="w-full px-3 py-2 border-b border-black/10 focus:outline-none focus:border-black bg-transparent text-sm"
                    >
                      <option value="all">All Items</option>
                      <option value="inStock">In Stock</option>
                      <option value="outOfStock">Out of Stock</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Active Filters Count and Clear Button */}
              {getActiveFiltersCount() > 0 && (
                <div className="flex justify-end items-center gap-4 mt-6 pt-4 border-t border-black/5">
                  <button
                    onClick={clearAllFilters}
                    className="flex items-center gap-2 px-4 py-2 text-xs uppercase tracking-wider hover:bg-black/5 transition-colors"
                  >
                    <FiX className="w-4 h-4" />
                    Clear all filters
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Products Grid - Responsive Design */}
      <div className="max-w-screen-xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-8">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-x-2 gap-y-4 sm:gap-x-6 sm:gap-y-16">
          {getFilteredProducts().map((product) => (
            <div key={product._id} className="group">
              {/* Image Container - Compact for Mobile */}
              <div
                className="relative aspect-square mb-2 sm:mb-4 cursor-pointer overflow-hidden"
                onClick={() => router.push(`/products/${product._id}`)}
                onMouseEnter={() => setHoveredProduct(product._id)}
                onMouseLeave={() => setHoveredProduct(null)}
              >
                <Image
                  src={hoveredProduct === product._id && product.images[1]
                    ? product.images[1]
                    : product.images[0]}
                  alt={product.name}
                  fill
                  className="object-cover object-center transition-transform duration-700 group-hover:scale-105"
                  sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                  priority={true}
                />

                {/* Stock Status Tags - Top Right Corner */}
                {product.stock === 0 ? (
                  <div className="absolute top-2 right-2 bg-red-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    Out of Stock
                  </div>
                ) : product.stock <= 5 && (
                  <div className="absolute top-2 right-2 bg-amber-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    Low Stock
                  </div>
                )}

                {/* Discount Tag - Top Left Corner */}
                {product.discount > 0 && (
                  <div className="absolute top-2 left-2 bg-black text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    {product.discount}% Off
                  </div>
                )}

                {/* Quick Add Button - Hidden on Mobile */}
                <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 py-2 sm:py-3 px-2 sm:px-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 hidden sm:flex justify-between items-center">
                  <button
                    onClick={(e) => handleToggleWishlist(e, product)}
                    className="text-xs uppercase tracking-wider flex items-center gap-2 hover:opacity-70"
                  >
                    {isInWishlist(product._id) ? (
                      <>
                        <FiX className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span>Remove</span>
                      </>
                    ) : (
                      <>
                        <FiHeart className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span>Save</span>
                      </>
                    )}
                  </button>

                  <button
                    onClick={(e) => handleAddToCart(e, product)}
                    disabled={addingToCart[product._id] || product.stock === 0}
                    className="text-xs uppercase tracking-wider flex items-center gap-2 hover:opacity-70 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiShoppingBag className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>{product.stock === 0 ? 'Out of Stock' : 'Add'}</span>
                  </button>
                </div>
              </div>

              {/* Product Info - Compact for Mobile */}
              <div className="space-y-0.5 sm:space-y-1 px-0.5">
                <h3 className="text-xs sm:text-sm font-light line-clamp-1 sm:line-clamp-2">
                  {product.name}
                </h3>
                <div className="flex justify-between items-center">
                  <p className="text-[10px] sm:text-xs text-gray-500 line-clamp-1">
                    {product.category}
                  </p>
                  {product.stock === 0 ? (
                    <p className="text-[10px] sm:text-xs font-medium text-red-500 bg-red-50 px-1.5 py-0.5 rounded">
                      Out of Stock
                    </p>
                  ) : product.stock <= 5 && (
                    <p className="text-[10px] sm:text-xs font-medium text-amber-500 bg-amber-50 px-1.5 py-0.5 rounded">
                      Low Stock: {product.stock} left
                    </p>
                  )}
                </div>
                <div className="flex items-baseline gap-1.5 sm:gap-2 mt-0.5 sm:mt-1">
                  {product.discount > 0 ? (
                    <>
                      <p className="text-xs sm:text-sm font-light">
                        ₹{((product.price * (100 - product.discount)) / 100).toLocaleString('en-IN')}
                      </p>
                      <p className="text-[10px] sm:text-xs text-gray-500 line-through">
                        ₹{product.price.toLocaleString('en-IN')}
                      </p>
                    </>
                  ) : (
                    <p className="text-xs sm:text-sm font-light">
                      ₹{product.price.toLocaleString('en-IN')}
                    </p>
                  )}
                </div>
              </div>

              {/* Mobile Quick Actions */}
              <div className="flex justify-between items-center mt-2 sm:hidden border-t border-gray-100 pt-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleWishlist(e, product);
                  }}
                  className="flex items-center gap-1.5 px-3 py-2 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-colors"
                >
                  {isInWishlist(product._id) ? (
                    <>
                      <FiX className="w-4 h-4" />
                      <span className="text-[10px] uppercase tracking-wider">Remove</span>
                    </>
                  ) : (
                    <>
                      <FiHeart className="w-4 h-4" />
                      <span className="text-[10px] uppercase tracking-wider">Save</span>
                    </>
                  )}
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToCart(e, product);
                  }}
                  disabled={addingToCart[product._id] || product.stock === 0}
                  className="flex items-center gap-1.5 px-3 py-2 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiShoppingBag className="w-4 h-4" />
                  <span className="text-[10px] uppercase tracking-wider">
                    {addingToCart[product._id] ? 'Adding...' : (product.stock === 0 ? 'Out of Stock' : 'Add')}
                  </span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {getFilteredProducts().length === 0 && (
          <div className="py-16 text-center">
            <p className="text-sm uppercase tracking-wider text-gray-500">No products found</p>
            <button
              onClick={clearAllFilters}
              className="mt-4 text-xs uppercase tracking-wider underline hover:opacity-70"
            >
              Clear all filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
