'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiSearch, FiFilter, FiCheck, FiArrowDown, FiArrowUp, FiEye, FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

export default function OrdersManagement() {
  const router = useRouter();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('all');
  const [priceRangeFilter, setPriceRangeFilter] = useState({ min: '', max: '' });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [dateRange, setDateRange] = useState('all');
  const [sortConfig, setSortConfig] = useState({ field: 'createdAt', direction: 'desc' });
  const [stats, setStats] = useState({ total: 0, pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0 });

  useEffect(() => {
    fetchOrders();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [orders]);

  const calculateStats = () => {
    const newStats = orders.reduce((acc, order) => {
      acc.total++;
      acc[order.status]++;
      return acc;
    }, { total: 0, pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0 });
    setStats(newStats);
  };

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/orders');
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
      } else {
        throw new Error('Failed to fetch orders');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        toast.success('Order status updated');
        fetchOrders();
      } else {
        throw new Error('Failed to update order status');
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error(error.message);
    }
  };

  const handleBulkStatusUpdate = async (newStatus) => {
    try {
      const promises = selectedOrders.map(orderId =>
        fetch(`/api/admin/orders/${orderId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: newStatus })
        })
      );

      await Promise.all(promises);
      toast.success('Orders updated successfully');
      fetchOrders();
      setSelectedOrders([]);
    } catch (error) {
      console.error('Error in bulk update:', error);
      toast.error('Failed to update some orders');
    }
  };

  const handleSort = (field) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const filteredOrders = orders.filter(order => {
    // Search filter
    const matchesSearch =
      order.user?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order._id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (order.orderNumber && order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()));

    // Status filter
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    // Payment method filter
    const matchesPaymentMethod = paymentMethodFilter === 'all' || order.paymentMethod === paymentMethodFilter;

    // Payment status filter
    const matchesPaymentStatus = paymentStatusFilter === 'all' || order.paymentStatus === paymentStatusFilter;

    // Price range filter
    let matchesPriceRange = true;
    if (priceRangeFilter.min !== '' && !isNaN(priceRangeFilter.min)) {
      matchesPriceRange = matchesPriceRange && order.total >= Number(priceRangeFilter.min);
    }
    if (priceRangeFilter.max !== '' && !isNaN(priceRangeFilter.max)) {
      matchesPriceRange = matchesPriceRange && order.total <= Number(priceRangeFilter.max);
    }

    // Date range filter
    let matchesDate = true;
    if (dateRange !== 'all') {
      const orderDate = new Date(order.createdAt);
      const now = new Date();
      if (dateRange === 'today') {
        matchesDate = orderDate.toDateString() === now.toDateString();
      } else if (dateRange === 'week') {
        const weekAgo = new Date(now.setDate(now.getDate() - 7));
        matchesDate = orderDate >= weekAgo;
      } else if (dateRange === 'month') {
        const monthAgo = new Date(now.setMonth(now.getMonth() - 1));
        matchesDate = orderDate >= monthAgo;
      }
    }

    return matchesSearch && matchesStatus && matchesPaymentMethod &&
           matchesPaymentStatus && matchesPriceRange && matchesDate;
  }).sort((a, b) => {
    const direction = sortConfig.direction === 'asc' ? 1 : -1;
    if (sortConfig.field === 'createdAt') {
      return (new Date(a.createdAt) - new Date(b.createdAt)) * direction;
    }
    if (sortConfig.field === 'total') {
      return (a.total - b.total) * direction;
    }
    return 0;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price) => {
    return `₹${Number(price).toLocaleString('en-IN')}`;
  };

  const getStatusStyle = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-50 text-yellow-800';
      case 'processing': return 'bg-blue-50 text-blue-800';
      case 'shipped': return 'bg-purple-50 text-purple-800';
      case 'delivered': return 'bg-green-50 text-green-800';
      case 'cancelled': return 'bg-red-50 text-red-800';
      default: return 'bg-gray-50 text-gray-800';
    }
  };

  const getStatBoxStyle = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-50 border-yellow-100';
      case 'processing': return 'bg-blue-50 border-blue-100';
      case 'shipped': return 'bg-purple-50 border-purple-100';
      case 'delivered': return 'bg-green-50 border-green-100';
      case 'cancelled': return 'bg-red-50 border-red-100';
      case 'total': return 'bg-gray-100 border-gray-200';
      default: return 'bg-gray-50 border-gray-100';
    }
  };

  const getStatTextStyle = (status) => {
    switch (status) {
      case 'pending': return 'text-yellow-800';
      case 'processing': return 'text-blue-800';
      case 'shipped': return 'text-purple-800';
      case 'delivered': return 'text-green-800';
      case 'cancelled': return 'text-red-800';
      case 'total': return 'text-gray-900 font-semibold';
      default: return 'text-gray-800';
    }
  };

  const getPaymentStatusStyle = (status) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const toggleOrderSelection = (orderId) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const toggleAllOrders = () => {
    setSelectedOrders(prev =>
      prev.length === filteredOrders.length
        ? []
        : filteredOrders.map(order => order._id)
    );
  };

  const handlePrintSelectedOrders = () => {
    // Create a printable version of the selected orders
    const selectedOrdersData = orders.filter(order => selectedOrders.includes(order._id));

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    // Generate HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Orders Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .header { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .order { margin-bottom: 30px; page-break-inside: avoid; }
          .items { margin-top: 10px; }
          @media print {
            button { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Orders Report</h1>
          <div>
            <p>Date: ${new Date().toLocaleDateString()}</p>
            <p>Total Orders: ${selectedOrdersData.length}</p>
          </div>
        </div>
        <button onclick="window.print()">Print Report</button>
        <hr>
        ${selectedOrdersData.map(order => `
          <div class="order">
            <h2>Order ${order.orderNumber || order._id}</h2>
            <p><strong>Date:</strong> ${formatDate(order.createdAt)}</p>
            <p><strong>Customer:</strong> ${order.user?.name || 'N/A'} (${order.user?.email || 'N/A'})</p>
            <p><strong>Status:</strong> ${order.status}</p>
            <p><strong>Payment:</strong> ${order.paymentMethod} (${order.paymentStatus})</p>
            <p><strong>Total:</strong> ${formatPrice(order.total)}</p>

            <div class="items">
              <h3>Items:</h3>
              <table>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Price</th>
                  <th>Total</th>
                </tr>
                ${order.items.map(item => `
                  <tr>
                    <td>${item.name}</td>
                    <td>${item.quantity}</td>
                    <td>${formatPrice(item.price)}</td>
                    <td>${formatPrice(item.price * item.quantity)}</td>
                  </tr>
                `).join('')}
              </table>
            </div>
          </div>
          <hr>
        `).join('')}
      </body>
      </html>
    `;

    // Write the content to the new window and print
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
  };

  const handleExportSelectedOrders = () => {
    // Get the selected orders data
    const selectedOrdersData = orders.filter(order => selectedOrders.includes(order._id));

    // Create CSV content
    let csvContent = "Order ID,Order Number,Date,Customer Name,Customer Email,Status,Payment Method,Payment Status,Total,Items\n";

    selectedOrdersData.forEach(order => {
      const itemsSummary = order.items.map(item => `${item.name} (${item.quantity}x)`).join('; ');

      // Format the row data and handle potential commas in text fields
      const row = [
        `"${order._id}"`,
        `"${order.orderNumber || ''}"`,
        `"${formatDate(order.createdAt)}"`,
        `"${order.user?.name || 'N/A'}"`,
        `"${order.user?.email || 'N/A'}"`,
        `"${order.status}"`,
        `"${order.paymentMethod}"`,
        `"${order.paymentStatus}"`,
        `"${formatPrice(order.total)}"`,
        `"${itemsSummary}"`
      ].join(',');

      csvContent += row + "\n";
    });

    // Create a download link for the CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `orders_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header with Stats */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          {/* Back Button */}
          <div className="mb-4">
            <button
              onClick={() => router.push('/admin')}
              className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
            >
              <FiArrowLeft className="mr-1.5" />
              BACK TO ADMIN
            </button>
          </div>

          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-light tracking-tight">ORDERS</h1>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <FiFilter size={20} />
              </button>
            </div>
          </div>

          {/* Order Stats */}
          <div className="grid grid-cols-6 gap-4 mb-4">
            {Object.entries(stats).map(([status, count]) => (
              <div key={status} className={`p-4 border ${getStatBoxStyle(status)}`}>
                <p className={`text-sm capitalize ${getStatTextStyle(status)}`}>{status}</p>
                <p className={`text-2xl font-medium ${getStatTextStyle(status)}`}>{count}</p>
              </div>
            ))}
          </div>

          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search by order number, ID, or customer name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 border-none rounded-none focus:ring-0 text-sm"
            />
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="px-6 py-3 border-t border-gray-100 bg-gray-50">
            <div className="space-y-4">
              {/* Order Status Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Status:</span>
                <div className="flex flex-wrap gap-2">
                  {['all', 'pending', 'processing', 'shipped', 'delivered', 'cancelled'].map(status => (
                    <button
                      key={status}
                      onClick={() => setStatusFilter(status)}
                      className={`px-3 py-1 text-xs ${
                        statusFilter === status
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Date Range Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Date Range:</span>
                <div className="flex flex-wrap gap-2">
                  {['all', 'today', 'week', 'month'].map(range => (
                    <button
                      key={range}
                      onClick={() => setDateRange(range)}
                      className={`px-3 py-1 text-xs ${
                        dateRange === range
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {range.charAt(0).toUpperCase() + range.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Method Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Payment Method:</span>
                <div className="flex flex-wrap gap-2">
                  {['all', 'cod', 'razorpay', 'card', 'upi', 'netbanking', 'wallet', 'giftcard'].map(method => (
                    <button
                      key={method}
                      onClick={() => setPaymentMethodFilter(method)}
                      className={`px-3 py-1 text-xs ${
                        paymentMethodFilter === method
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {method === 'all' ? 'All' :
                       method === 'cod' ? 'COD' :
                       method === 'upi' ? 'UPI' :
                       method.charAt(0).toUpperCase() + method.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Status Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Payment Status:</span>
                <div className="flex flex-wrap gap-2">
                  {['all', 'pending', 'paid', 'failed'].map(status => (
                    <button
                      key={status}
                      onClick={() => setPaymentStatusFilter(status)}
                      className={`px-3 py-1 text-xs ${
                        paymentStatusFilter === status
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Price Range:</span>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    placeholder="Min"
                    value={priceRangeFilter.min}
                    onChange={(e) => setPriceRangeFilter(prev => ({ ...prev, min: e.target.value }))}
                    className="w-24 px-3 py-1 text-xs border border-gray-200 focus:outline-none focus:ring-1 focus:ring-black"
                  />
                  <span className="text-sm text-gray-500">to</span>
                  <input
                    type="number"
                    placeholder="Max"
                    value={priceRangeFilter.max}
                    onChange={(e) => setPriceRangeFilter(prev => ({ ...prev, max: e.target.value }))}
                    className="w-24 px-3 py-1 text-xs border border-gray-200 focus:outline-none focus:ring-1 focus:ring-black"
                  />
                  <button
                    onClick={() => setPriceRangeFilter({ min: '', max: '' })}
                    className="px-3 py-1 text-xs bg-white text-black border border-gray-200 hover:bg-gray-100"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Table Header */}
      <div className="grid grid-cols-6 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={selectedOrders.length === filteredOrders.length}
            onChange={toggleAllOrders}
            className="rounded border-gray-300"
          />
          <button onClick={() => handleSort('createdAt')} className="flex items-center gap-1">
            Order Info
            {sortConfig.field === 'createdAt' && (
              sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
            )}
          </button>
        </div>
        <div>Customer</div>
        <div>Items</div>
        <button onClick={() => handleSort('total')} className="flex items-center gap-1">
          Total
          {sortConfig.field === 'total' && (
            sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
          )}
        </button>
        <div>Status</div>
        <div>Date</div>
      </div>

      {/* Orders List */}
      <div className="divide-y divide-gray-100">
        {loading ? (
          <div className="p-6 text-center text-gray-500">Loading orders...</div>
        ) : filteredOrders.length === 0 ? (
          <div className="p-6 text-center text-gray-500">No orders found</div>
        ) : (
          filteredOrders.map((order) => (
            <div
              key={order._id}
              className={`group hover:bg-gray-50 ${
                selectedOrders.includes(order._id) ? 'bg-gray-50' : ''
              }`}
            >
              <div className="px-6 py-4">
                <div className="grid grid-cols-6 gap-4 items-center">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={selectedOrders.includes(order._id)}
                      onChange={() => toggleOrderSelection(order._id)}
                      className="rounded border-gray-300"
                    />
                    <div>
                      <div>
                        <Link href={`/admin/orders/${order._id}`} className="text-sm font-medium hover:underline">
                          #{order.orderNumber || order._id.slice(-6)}
                        </Link>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium">{order.user?.name || 'N/A'}</p>
                    <p className="text-xs text-gray-500">{order.user?.email || 'N/A'}</p>
                    {order.user?.phone && (
                      <p className="text-xs text-gray-500">{order.user.phone}</p>
                    )}
                    <button
                      onClick={() => window.open(`/admin/users/${order.user?._id}`, '_blank')}
                      className="text-xs text-black hover:underline mt-1"
                    >
                      View Customer
                    </button>
                  </div>

                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{order.items.length} items</span>
                      <button
                        onClick={() => {
                          const itemsList = order.items.map(item =>
                            `${item.name} (${item.quantity}x) - ₹${formatPrice(item.price * item.quantity)}`
                          ).join('\n');
                          alert(`Order Items:\n\n${itemsList}`);
                        }}
                        className="text-xs text-black hover:underline"
                      >
                        View Items
                      </button>
                    </div>
                    {order.items.length > 0 && (
                      <p className="text-xs text-gray-500 truncate max-w-[150px]">
                        {order.items[0].name}
                        {order.items.length > 1 ? ` +${order.items.length - 1} more` : ''}
                      </p>
                    )}
                  </div>

                  <div>
                    <p className="text-sm font-medium">{formatPrice(order.total)}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <span className={`px-1.5 py-0.5 text-[10px] rounded-full ${getPaymentStatusStyle(order.paymentStatus)}`}>
                        {order.paymentStatus}
                      </span>
                      <span className="text-xs text-gray-500">
                        {order.paymentMethod === 'cod' ? 'COD' :
                         order.paymentMethod === 'upi' ? 'UPI' :
                         order.paymentMethod.charAt(0).toUpperCase() + order.paymentMethod.slice(1)}
                      </span>
                    </div>
                  </div>

                  <div>
                    <select
                      value={order.status}
                      onChange={(e) => handleUpdateStatus(order._id, e.target.value)}
                      className={`text-sm border-none focus:ring-0 cursor-pointer ${getStatusStyle(order.status)}`}
                    >
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="shipped">Shipped</option>
                      <option value="delivered">Delivered</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  <div className="text-sm text-gray-500">
                    {formatDate(order.createdAt)}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Bulk Actions */}
      {selectedOrders.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
          <div className="max-w-screen-xl mx-auto flex items-center justify-between">
            <span className="text-sm">
              {selectedOrders.length} orders selected
            </span>
            <div className="flex gap-4">
              <select
                onChange={(e) => handleBulkStatusUpdate(e.target.value)}
                className="text-sm border-gray-200"
                defaultValue=""
              >
                <option value="" disabled>Update Status</option>
                <option value="processing">Mark as Processing</option>
                <option value="shipped">Mark as Shipped</option>
                <option value="delivered">Mark as Delivered</option>
                <option value="cancelled">Mark as Cancelled</option>
              </select>
              <button
                onClick={handlePrintSelectedOrders}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Print Selected
              </button>
              <button
                onClick={handleExportSelectedOrders}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Export CSV
              </button>
              <select
                onChange={(e) => {
                  if (e.target.value === 'email') {
                    alert('Email functionality will be implemented soon');
                  } else if (e.target.value === 'delete') {
                    if (confirm(`Are you sure you want to delete ${selectedOrders.length} orders? This action cannot be undone.`)) {
                      alert('Delete functionality will be implemented soon');
                    }
                  }
                  e.target.value = '';
                }}
                className="text-sm border-gray-200"
                defaultValue=""
              >
                <option value="" disabled>More Actions</option>
                <option value="email">Email Customers</option>
                <option value="delete">Delete Orders</option>
              </select>
              <button
                onClick={() => setSelectedOrders([])}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
