import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Newsletter from '@/models/Newsletter';
import { sendBulkNewsletter } from '@/lib/email';

export async function POST(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get request body
    const { subject, content, testEmail } = await request.json();

    // Validate required fields
    if (!subject || !content) {
      return NextResponse.json(
        { error: 'Subject and content are required' },
        { status: 400 }
      );
    }

    // If testEmail is provided, send only to that email
    if (testEmail) {
      const result = await sendBulkNewsletter([testEmail], subject, content);
      
      if (!result.success) {
        return NextResponse.json(
          { error: 'Failed to send test newsletter', details: result.error },
          { status: 500 }
        );
      }
      
      return NextResponse.json({
        message: 'Test newsletter sent successfully',
        recipients: 1
      });
    }

    // Get all active subscribers
    const subscribers = await Newsletter.find({ status: 'active' });
    
    if (subscribers.length === 0) {
      return NextResponse.json(
        { error: 'No active subscribers found' },
        { status: 400 }
      );
    }

    // Extract emails
    const emails = subscribers.map(subscriber => subscriber.email);

    // Send newsletter
    const result = await sendBulkNewsletter(emails, subject, content);

    if (!result.success) {
      return NextResponse.json(
        { error: 'Failed to send newsletter', details: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Newsletter sent successfully',
      recipients: emails.length
    });
  } catch (error) {
    console.error('Error sending newsletter:', error);
    return NextResponse.json(
      { error: 'Failed to send newsletter', details: error.message },
      { status: 500 }
    );
  }
}
