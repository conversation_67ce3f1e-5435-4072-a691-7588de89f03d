import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Store from '@/models/Store';
import { getSessionUser } from '@/lib/auth';

// Get all stores (admin access)
export async function GET() {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Fetch all stores
    const stores = await Store.find().sort({ createdAt: -1 });
    
    return NextResponse.json(stores);
  } catch (error) {
    console.error('Error fetching stores:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stores' },
      { status: 500 }
    );
  }
}

// Create a new store (admin access)
export async function POST(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse and validate request body
    const data = await request.json();
    const { 
      name, 
      city, 
      address, 
      fullAddress, 
      phone, 
      hours, 
      features, 
      image, 
      coordinates,
      status = 'active'
    } = data;

    // Basic validation
    if (!name || !city || !address || !fullAddress || !phone || !hours || !image) {
      return NextResponse.json(
        { error: 'All required fields must be filled' },
        { status: 400 }
      );
    }

    // Create and save store
    const store = new Store({
      name,
      city,
      address,
      fullAddress,
      phone,
      hours,
      features: features || [],
      image,
      coordinates: coordinates || { lat: null, lng: null },
      status,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await store.save();
    console.log('Store created successfully:', store._id);

    return NextResponse.json(store, { status: 201 });
  } catch (error) {
    console.error('Error creating store:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: Object.values(error.errors).map(err => err.message).join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create store' },
      { status: 500 }
    );
  }
}
