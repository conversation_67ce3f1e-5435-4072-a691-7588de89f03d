'use client';
import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Fi<PERSON>, FiCheck } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

export default function GiftCardRedemption({ onApply, onRemove, disabled, total }) {
  const [giftCardCode, setGiftCardCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [giftCard, setGiftCard] = useState(null);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setGiftCardCode(e.target.value.toUpperCase());
    setError('');
  };

  const validateGiftCard = async () => {
    if (!giftCardCode.trim()) {
      setError('Please enter a gift card code');
      return;
    }

    try {
      setIsValidating(true);
      setError('');

      const response = await fetch('/api/gift-cards/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: giftCardCode }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Invalid gift card');
      }

      const giftCardData = await response.json();

      // Check if gift card has enough balance
      if (giftCardData.balance <= 0) {
        throw new Error('Gift card has no remaining balance');
      }

      setGiftCard(giftCardData);

      // Calculate amount to apply (either full balance or total amount)
      const amountToApply = Math.min(giftCardData.balance, total);

      // Call the onApply callback with the gift card data
      onApply({
        code: giftCardData.code,
        amount: amountToApply,
      });

      toast.success('Gift card applied successfully');
    } catch (error) {
      console.error('Error validating gift card:', error);
      setError(error.message || 'Failed to validate gift card');
      setGiftCard(null);
    } finally {
      setIsValidating(false);
    }
  };

  const removeGiftCard = () => {
    setGiftCard(null);
    setGiftCardCode('');
    setError('');
    onRemove();
    toast.success('Gift card removed');
  };

  return (
    <div className="border-b border-gray-100 pb-8">
      <h2 className="text-sm uppercase tracking-wider mb-6">Gift Card</h2>

      {giftCard ? (
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50">
            <div>
              <p className="font-medium">{giftCard.code}</p>
              <p className="text-sm text-gray-500">
                Balance: ₹{giftCard.balance.toLocaleString('en-IN')}
              </p>
            </div>
            <button
              type="button"
              onClick={removeGiftCard}
              className="text-gray-500 hover:text-black"
              disabled={disabled}
            >
              <FiX size={18} />
            </button>
          </div>
          <div className="flex items-center text-green-600">
            <FiCheck className="mr-2" />
            <span>Gift card applied</span>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={giftCardCode}
              onChange={handleChange}
              placeholder="Enter gift card code"
              className="flex-1 px-0 py-2 bg-transparent border-0 border-b border-gray-300 focus:ring-0 focus:border-black"
              disabled={disabled || isValidating}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  validateGiftCard();
                }
              }}
            />
            <button
              type="button"
              onClick={validateGiftCard}
              className="bg-black text-white px-4 py-2 hover:bg-gray-800 transition-colors flex items-center justify-center"
              disabled={disabled || isValidating || !giftCardCode.trim()}
            >
              {isValidating ? (
                <FiLoader className="animate-spin" />
              ) : (
                'Apply'
              )}
            </button>
          </div>

          {error && (
            <p className="text-red-500 text-sm">{error}</p>
          )}
        </div>
      )}
    </div>
  );
}
