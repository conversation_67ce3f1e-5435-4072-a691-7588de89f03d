'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import {
  FiSearch, FiFilter, FiCheck, FiMoreVertical,
  FiTrash2, FiChevronDown, FiMail, FiArrowLeft,
  FiRefreshCw, FiEdit, FiPlus
} from 'react-icons/fi';
import Pagination from '@/components/admin/Pagination';

export default function ContactsManagement() {
  const router = useRouter();
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortConfig, setSortConfig] = useState({ field: 'createdAt', direction: 'desc' });
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    today: 0,
    week: 0,
    month: 0
  });
  const dropdownRef = useRef(null);
  const itemsPerPage = 10;

  useEffect(() => {
    fetchContacts();
  }, [currentPage, sortConfig]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setActiveDropdown(null);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const fetchContacts = async () => {
    try {
      setLoading(true);
      // Add a cache-busting parameter to ensure we get fresh data
      const timestamp = new Date().getTime();

      // Build query parameters
      let url = `/api/admin/contacts`;
      const params = new URLSearchParams();

      params.append('page', currentPage);
      params.append('limit', itemsPerPage);
      params.append('sort', sortConfig.field);
      params.append('direction', sortConfig.direction);

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      // Add timestamp to prevent caching
      params.append('_', timestamp);

      url += `?${params.toString()}`;

      const response = await fetch(url);

      if (!response.ok) throw new Error('Failed to fetch contacts');

      const data = await response.json();
      setContacts(data.contacts);
      setTotalPages(data.pages);

      // Calculate statistics
      const now = new Date();
      const today = new Date(now.setHours(0, 0, 0, 0));
      const weekAgo = new Date(now);
      weekAgo.setDate(weekAgo.getDate() - 7);
      const monthAgo = new Date(now);
      monthAgo.setDate(monthAgo.getDate() - 30);

      // Get all contacts for statistics (if not already included in the response)
      let allContacts = data.allContacts || data.contacts;

      if (!data.allContacts && data.total > data.contacts.length) {
        const allResponse = await fetch(`/api/admin/contacts?limit=${data.total}`);
        if (allResponse.ok) {
          const allData = await allResponse.json();
          allContacts = allData.contacts;
        }
      }

      // Calculate stats
      const todayContacts = allContacts.filter(c => new Date(c.createdAt) >= today).length;
      const weekContacts = allContacts.filter(c => new Date(c.createdAt) >= weekAgo).length;
      const monthContacts = allContacts.filter(c => new Date(c.createdAt) >= monthAgo).length;

      setStats({
        total: data.total || allContacts.length,
        today: todayContacts,
        week: weekContacts,
        month: monthContacts
      });
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to load contact messages');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field) => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
      const response = await fetch('/api/admin/contacts', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id })
      });

      if (!response.ok) throw new Error('Failed to delete message');

      toast.success('Message deleted successfully');
      fetchContacts(); // Refresh the list
      setShowModal(false);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to delete message');
    }
  };

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedContacts.length} selected messages?`)) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/admin/contacts', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: selectedContacts })
      });

      if (!response.ok) throw new Error('Failed to delete messages');

      const data = await response.json();
      toast.success(data.message);
      setSelectedContacts([]);
      fetchContacts(); // Refresh the list
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to delete messages');
    } finally {
      setActionLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on search
    fetchContacts();
  };

  const filteredContacts = contacts.filter(contact =>
    !searchQuery ||
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.subject?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading && contacts.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-sm tracking-[0.2em]">LOADING...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-4">
          <button
            onClick={() => router.push('/admin')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO ADMIN
          </button>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xs tracking-[0.2em] text-gray-400">CONTACTS</h1>
          <button
            onClick={() => fetchContacts()}
            className="bg-black text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
          >
            <FiRefreshCw className="mr-2" />
            REFRESH
          </button>
        </div>

        {/* Contact Statistics */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="p-4 border border-gray-200 bg-gray-50">
            <p className="text-sm">TOTAL MESSAGES</p>
            <p className="text-2xl font-medium">{stats.total}</p>
          </div>
          <div className="p-4 border border-blue-100 bg-blue-50">
            <p className="text-sm text-blue-700">TODAY</p>
            <p className="text-2xl font-medium text-blue-700">{stats.today}</p>
          </div>
          <div className="p-4 border border-green-100 bg-green-50">
            <p className="text-sm text-green-700">LAST 7 DAYS</p>
            <p className="text-2xl font-medium text-green-700">{stats.week}</p>
          </div>
          <div className="p-4 border border-purple-100 bg-purple-50">
            <p className="text-sm text-purple-700">LAST 30 DAYS</p>
            <p className="text-2xl font-medium text-purple-700">{stats.month}</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 bg-white border border-black/10">
          <div className="p-4">
            <form onSubmit={handleSearch} className="flex items-center">
              <div className="relative flex-grow">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name, email or subject..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-transparent border-none focus:ring-0 text-sm"
                />
              </div>
              <button
                type="submit"
                className="ml-2 px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
              >
                SEARCH
              </button>
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="ml-2 p-2 border border-gray-200 hover:bg-gray-50"
                title="Filter"
              >
                <FiFilter size={16} />
              </button>
            </form>

            {showFilters && (
              <div className="mt-4 p-4 border-t border-gray-100">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sort By
                    </label>
                    <select
                      value={`${sortConfig.field}-${sortConfig.direction}`}
                      onChange={(e) => {
                        const [field, direction] = e.target.value.split('-');
                        setSortConfig({ field, direction });
                      }}
                      className="w-full p-2 border border-gray-300 rounded text-sm"
                    >
                      <option value="createdAt-desc">Date (Newest First)</option>
                      <option value="createdAt-asc">Date (Oldest First)</option>
                      <option value="name-asc">Name (A-Z)</option>
                      <option value="name-desc">Name (Z-A)</option>
                      <option value="subject-asc">Subject (A-Z)</option>
                      <option value="subject-desc">Subject (Z-A)</option>
                    </select>
                  </div>
                  <div className="flex items-end">
                    <button
                      onClick={() => {
                        setShowFilters(false);
                        fetchContacts();
                      }}
                      className="px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
                    >
                      APPLY FILTERS
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Contacts Table */}
        <div className="overflow-x-auto bg-white border border-black/10">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedContacts.length === contacts.length && contacts.length > 0}
                    onChange={() => {
                      if (selectedContacts.length === contacts.length) {
                        setSelectedContacts([]);
                      } else {
                        setSelectedContacts(contacts.map(c => c._id));
                      }
                    }}
                    className="h-4 w-4 border-gray-300 rounded"
                  />
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('name')}
                >
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Email
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('subject')}
                >
                  Subject
                </th>
                <th className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Message
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('createdAt')}
                >
                  Date
                </th>
                <th className="px-6 py-3 text-right text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading contacts...
                  </td>
                </tr>
              ) : contacts.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-sm text-gray-500">
                    No contacts found
                  </td>
                </tr>
              ) : (
                contacts.map((contact) => (
                  <tr key={contact._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedContacts.includes(contact._id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          setSelectedContacts(prev =>
                            prev.includes(contact._id)
                              ? prev.filter(id => id !== contact._id)
                              : [...prev, contact._id]
                          );
                        }}
                        className="h-4 w-4 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {contact.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.subject || 'No Subject'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {contact.message.substring(0, 30)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(contact.createdAt), 'dd/MM/yyyy')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => {
                            setSelectedContact(contact);
                            setShowModal(true);
                          }}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="View Details"
                        >
                          <FiMail size={18} />
                        </button>
                        <button
                          onClick={() => handleDelete(contact._id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <FiTrash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>

      </div>

      {/* Bulk Actions */}
      {selectedContacts.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-20">
          <div className="container mx-auto flex items-center justify-between">
            <span className="text-xs tracking-[0.2em]">
              {selectedContacts.length} CONTACTS SELECTED
            </span>
            <div className="flex gap-4">
              <button
                onClick={() => setSelectedContacts([])}
                className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50"
              >
                CANCEL
              </button>
              <button
                onClick={handleBulkDelete}
                disabled={actionLoading}
                className="flex items-center px-4 py-2 text-xs tracking-[0.2em] bg-red-600 text-white hover:bg-red-700 disabled:bg-red-400 disabled:cursor-not-allowed"
              >
                <FiTrash2 className="mr-2" />
                {actionLoading ? 'PROCESSING...' : 'DELETE SELECTED'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Message Modal */}
      {showModal && selectedContact && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
          <div className="bg-white w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xs tracking-[0.2em] text-gray-400">MESSAGE DETAILS</h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-xs tracking-[0.2em] hover:opacity-50"
                >
                  CLOSE
                </button>
              </div>

              <div className="space-y-6 mb-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <p className="text-xs tracking-[0.2em] text-gray-400 mb-2">FROM</p>
                    <p className="text-sm">{selectedContact.name}</p>
                    <p className="text-sm text-gray-500 mt-1">{selectedContact.email}</p>
                    {selectedContact.phone && (
                      <p className="text-sm text-gray-500 mt-1">{selectedContact.phone}</p>
                    )}
                  </div>
                  <div>
                    <p className="text-xs tracking-[0.2em] text-gray-400 mb-2">RECEIVED</p>
                    <p className="text-sm">
                      {format(new Date(selectedContact.createdAt), 'dd/MM/yyyy HH:mm')}
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-xs tracking-[0.2em] text-gray-400 mb-2">SUBJECT</p>
                  <p className="text-sm">{selectedContact.subject || 'No Subject'}</p>
                </div>

                <div>
                  <p className="text-xs tracking-[0.2em] text-gray-400 mb-2">MESSAGE</p>
                  <div className="p-4 bg-gray-50 border border-gray-100 rounded">
                    <p className="text-sm whitespace-pre-wrap">{selectedContact.message}</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    window.location.href = `mailto:${selectedContact.email}`;
                  }}
                  className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50 flex items-center"
                >
                  <FiMail className="mr-2" /> REPLY
                </button>
                <button
                  onClick={() => handleDelete(selectedContact._id)}
                  className="px-4 py-2 text-xs tracking-[0.2em] bg-red-600 text-white hover:bg-red-700 flex items-center"
                >
                  <FiTrash2 className="mr-2" /> DELETE
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
