import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Newsletter from '@/models/Newsletter';
import { sendNewsletterWelcomeEmail } from '@/lib/email';

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email) {
    return 'Email is required';
  }

  if (!emailRegex.test(email)) {
    return 'Invalid email format';
  }

  if (email.length > 254) {
    return 'Email address is too long';
  }

  const disposableDomains = [
    'tempmail.com',
    'throwawaymail.com',
    'temp-mail.org',
    'fakeinbox.com',
    'guerrillamail.com'
  ];

  const domain = email.split('@')[1].toLowerCase();
  if (disposableDomains.includes(domain)) {
    return 'Please use a valid non-disposable email address';
  }

  return '';
};

export async function POST(request) {
  try {
    const { email } = await request.json();

    // Server-side validation
    const validationError = validateEmail(email);
    if (validationError) {
      return NextResponse.json(
        { error: validationError },
        { status: 400 }
      );
    }

    await connectToDB();

    // Check if email already exists
    const existingSubscriber = await Newsletter.findOne({ 
      email: email.toLowerCase() 
    });
    
    if (existingSubscriber) {
      if (existingSubscriber.status === 'unsubscribed') {
        // Reactivate subscription
        existingSubscriber.status = 'active';
        existingSubscriber.subscribedAt = new Date();
        await existingSubscriber.save();
        
        return NextResponse.json({
          message: 'Your subscription has been reactivated',
          subscriber: existingSubscriber
        });
      }
      
      return NextResponse.json(
        { message: 'You are already subscribed to our newsletter' },
        { status: 200 }
      );
    }

    // Create new subscriber
    const subscriber = await Newsletter.create({
      email: email.toLowerCase(),
      subscribedAt: new Date(),
      status: 'active'
    });

    // Send welcome email
    await sendNewsletterWelcomeEmail(email);

    return NextResponse.json({
      message: 'Successfully subscribed to newsletter',
      subscriber
    });
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { error: 'Failed to subscribe to newsletter' },
      { status: 500 }
    );
  }
}
