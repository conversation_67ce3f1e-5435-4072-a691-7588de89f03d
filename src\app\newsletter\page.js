'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { motion } from 'framer-motion';

export default function NewsletterPage() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const disposableDomains = [
      'tempmail.com',
      'throwawaymail.com',
      'temp-mail.org',
      'fakeinbox.com',
      'guerrillamail.com'
    ];

    if (!email) return 'Email is required';
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    if (email.length > 254) return 'Email address is too long';
    
    const domain = email.split('@')[1].toLowerCase();
    if (disposableDomains.includes(domain)) {
      return 'Please use a valid non-disposable email address';
    }

    return '';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateEmail(email);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: email.toLowerCase().trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe');
      }

      toast.success(data.message || 'Successfully subscribed to newsletter!');
      setEmail('');
    } catch (error) {
      toast.error(error.message);
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-[460px] mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="mb-12">
            <h1 className="text-xs tracking-widest mb-6">NEWSLETTER</h1>
            <p className="text-xs text-black/60 leading-relaxed">
              Subscribe to receive updates, access to exclusive deals, and more.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="space-y-6">
              <input
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setError('');
                }}
                placeholder="ENTER YOUR EMAIL ADDRESS"
                className="w-full bg-transparent border-0 border-b border-black/20 px-0 py-2 
                  text-sm placeholder:text-black/40 focus:ring-0 focus:border-black"
                required
              />
              
              {error && (
                <p className="text-xs text-red-500">{error}</p>
              )}
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <input 
                  type="checkbox" 
                  id="privacyConsent"
                  className="mt-1.5 h-3 w-3 border-gray-300 rounded-none"
                  required
                />
                <label htmlFor="privacyConsent" className="text-[10px] leading-relaxed text-black/60">
                  I agree to receive personalised marketing communications from FurnitureBazaar. 
                  I understand that I can unsubscribe at any time. View our{' '}
                  <a href="/privacy" className="underline hover:text-black">Privacy Policy</a>.
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-black text-white py-3 text-xs tracking-widest
                  hover:bg-black/90 disabled:bg-black/70 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
              </button>
            </div>
          </form>

          <div className="mt-12 space-y-8">
            <div>
              <h2 className="text-xs tracking-widest mb-4">WHAT YOU'LL RECEIVE</h2>
              <ul className="space-y-3 text-xs text-black/60">
                <li>• Early access to sales</li>
                <li>• Exclusive offers and promotions</li>
                <li>• New collection previews</li>
                <li>• Interior design inspiration</li>
                <li>• Styling tips and guides</li>
              </ul>
            </div>

            <div>
              <h2 className="text-xs tracking-widest mb-4">NEED HELP?</h2>
              <a 
                href="/contact" 
                className="text-xs text-black/60 hover:text-black hover:underline"
              >
                Contact Customer Service
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}