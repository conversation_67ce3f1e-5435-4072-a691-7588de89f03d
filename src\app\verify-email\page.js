'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'react-hot-toast';

function VerifyEmailContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [verificationStatus, setVerificationStatus] = useState('verifying');

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        const response = await fetch('/api/auth/verify-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || data.message || 'Verification failed');
        }

        setVerificationStatus('success');
        toast.success('Email verified successfully');
        // Redirect to login after 3 seconds
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
      } catch (error) {
        setVerificationStatus('error');
        toast.error(error.message);
      }
    };

    if (token) {
      verifyEmail();
    } else {
      setVerificationStatus('error');
      toast.error('Invalid verification link');
    }
  }, [token]);

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <h2 className="text-3xl font-bold mb-4">Email Verification</h2>

        {verificationStatus === 'verifying' && (
          <div>
            <p className="text-gray-600 mb-4">Verifying your email address...</p>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto"></div>
          </div>
        )}

        {verificationStatus === 'success' && (
          <div>
            <p className="text-green-600 mb-2">Email verified successfully!</p>
            <p className="text-gray-600">Redirecting to login page...</p>
          </div>
        )}

        {verificationStatus === 'error' && (
          <div>
            <p className="text-red-600 mb-4">Verification failed</p>
            <a
              href="/login"
              className="text-black hover:underline"
            >
              Return to login
            </a>
          </div>
        )}
      </div>
    </div>
  );
}

export default function VerifyEmail() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}
