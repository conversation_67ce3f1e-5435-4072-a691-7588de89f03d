import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Store from '@/models/Store';

// Get a specific store by ID (admin access)
export async function GET(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    const { id } = params;
    const store = await Store.findById(id);

    if (!store) {
      return NextResponse.json(
        { error: 'Store not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(store);
  } catch (error) {
    console.error('Error fetching store:', error);
    return NextResponse.json(
      { error: 'Failed to fetch store' },
      { status: 500 }
    );
  }
}

// Update a store (admin access)
export async function PUT(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    const { id } = params;
    const data = await request.json();
    const { 
      name, 
      city, 
      address, 
      fullAddress, 
      phone, 
      hours, 
      features, 
      image, 
      coordinates,
      status
    } = data;

    // Basic validation
    if (!name || !city || !address || !fullAddress || !phone || !hours || !image) {
      return NextResponse.json(
        { error: 'All required fields must be filled' },
        { status: 400 }
      );
    }

    // Update store
    const updatedStore = await Store.findByIdAndUpdate(
      id,
      {
        name,
        city,
        address,
        fullAddress,
        phone,
        hours,
        features: features || [],
        image,
        coordinates: coordinates || { lat: null, lng: null },
        status,
        updatedAt: new Date(),
      },
      { new: true, runValidators: true }
    );

    if (!updatedStore) {
      return NextResponse.json(
        { error: 'Store not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedStore);
  } catch (error) {
    console.error('Error updating store:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: Object.values(error.errors).map(err => err.message).join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update store' },
      { status: 500 }
    );
  }
}

// Delete a store (admin access)
export async function DELETE(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    const { id } = params;
    const store = await Store.findByIdAndDelete(id);

    if (!store) {
      return NextResponse.json(
        { error: 'Store not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Store deleted successfully' });
  } catch (error) {
    console.error('Error deleting store:', error);
    return NextResponse.json(
      { error: 'Failed to delete store' },
      { status: 500 }
    );
  }
}
