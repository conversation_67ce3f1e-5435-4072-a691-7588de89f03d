'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import { FiArrowLeft } from 'react-icons/fi';
import { use } from 'react';

export default function EditCouponPage({ params }) {
  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const couponId = resolvedParams.id;

  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [coupon, setCoupon] = useState(null);
  const [formData, setFormData] = useState({
    code: '',
    type: 'percentage',
    value: 0,
    minPurchase: 0,
    maxDiscount: '',
    startDate: '',
    expiryDate: '',
    usageLimit: '',
    description: '',
    isActive: true
  });

  useEffect(() => {
    // Check if user is admin
    if (user && user.role !== 'admin') {
      toast.error('You do not have permission to access this page');
      router.push('/');
      return;
    }

    if (couponId) {
      fetchCoupon(couponId);
    }
  }, [user, router, couponId]);

  const fetchCoupon = async (id) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/coupons/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch coupon');
      }

      const data = await response.json();
      setCoupon(data);

      // Format dates for input fields
      const startDate = data.startDate 
        ? new Date(data.startDate).toISOString().split('T')[0]
        : '';
      
      const expiryDate = data.expiryDate
        ? new Date(data.expiryDate).toISOString().split('T')[0]
        : '';

      setFormData({
        code: data.code || '',
        type: data.type || 'percentage',
        value: data.value || 0,
        minPurchase: data.minPurchase || 0,
        maxDiscount: data.maxDiscount !== null ? data.maxDiscount : '',
        startDate,
        expiryDate,
        usageLimit: data.usageLimit !== null ? data.usageLimit : '',
        description: data.description || '',
        isActive: data.isActive !== undefined ? data.isActive : true
      });
    } catch (error) {
      console.error('Error fetching coupon:', error);
      toast.error('Failed to load coupon');
      router.push('/admin/coupons');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
              (name === 'value' || name === 'minPurchase' || name === 'maxDiscount' || name === 'usageLimit') && value !== '' ? 
              Number(value) : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      // Validate form
      if (!formData.code) {
        throw new Error('Coupon code is required');
      }

      if (!formData.value || formData.value <= 0) {
        throw new Error('Coupon value must be greater than 0');
      }

      if (formData.type === 'percentage' && formData.value > 100) {
        throw new Error('Percentage discount cannot exceed 100%');
      }

      if (!formData.expiryDate) {
        throw new Error('Expiry date is required');
      }

      // Update coupon
      const response = await fetch(`/api/admin/coupons/${couponId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update coupon');
      }

      toast.success('Coupon updated successfully');
      router.push('/admin/coupons');
    } catch (error) {
      console.error('Error updating coupon:', error);
      toast.error(error.message || 'Failed to update coupon');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-gray-500">Loading coupon details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!coupon) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p>Coupon not found</p>
            <button
              onClick={() => router.push('/admin/coupons')}
              className="mt-4 px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
            >
              BACK TO COUPONS
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center">
          <button
            onClick={() => router.back()}
            className="mr-4 text-gray-500 hover:text-black"
          >
            <FiArrowLeft size={20} />
          </button>
          <h1 className="text-xs tracking-[0.2em] text-gray-400">EDIT COUPON</h1>
        </div>

        <div className="max-w-2xl mx-auto bg-white border border-black/10">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Coupon Code */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                COUPON CODE
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                required
              />
            </div>

            {/* Discount Type */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                DISCOUNT TYPE
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
              >
                <option value="percentage">Percentage Discount</option>
                <option value="fixed">Fixed Amount Discount</option>
              </select>
            </div>

            {/* Discount Value */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                {formData.type === 'percentage' ? 'DISCOUNT PERCENTAGE (%)' : 'DISCOUNT AMOUNT (₹)'}
              </label>
              <input
                type="number"
                name="value"
                value={formData.value}
                onChange={handleChange}
                min={1}
                max={formData.type === 'percentage' ? 100 : undefined}
                step={1}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                required
              />
              {formData.type === 'percentage' && (
                <p className="mt-1 text-xs text-gray-500">Maximum: 100%</p>
              )}
            </div>

            {/* Minimum Purchase */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                MINIMUM PURCHASE AMOUNT (₹)
              </label>
              <input
                type="number"
                name="minPurchase"
                value={formData.minPurchase}
                onChange={handleChange}
                min={0}
                step={100}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
              />
              <p className="mt-1 text-xs text-gray-500">Set to 0 for no minimum purchase requirement</p>
            </div>

            {/* Maximum Discount (for percentage type) */}
            {formData.type === 'percentage' && (
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  MAXIMUM DISCOUNT AMOUNT (₹)
                </label>
                <input
                  type="number"
                  name="maxDiscount"
                  value={formData.maxDiscount}
                  onChange={handleChange}
                  min={0}
                  step={100}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                />
                <p className="mt-1 text-xs text-gray-500">Leave empty for no maximum discount limit</p>
              </div>
            )}

            {/* Usage Information */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                USAGE INFORMATION
              </label>
              <div className="p-3 bg-gray-50 border border-gray-200 text-sm">
                <p>Used: <span className="font-medium">{coupon.usedCount} times</span></p>
                <p className="mt-1">Used by: <span className="font-medium">{coupon.usedBy?.length || 0} users</span></p>
              </div>
            </div>

            {/* Usage Limit */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                USAGE LIMIT
              </label>
              <input
                type="number"
                name="usageLimit"
                value={formData.usageLimit}
                onChange={handleChange}
                min={1}
                step={1}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
              />
              <p className="mt-1 text-xs text-gray-500">Leave empty for unlimited usage</p>
            </div>

            {/* Validity Period */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  START DATE
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  EXPIRY DATE
                </label>
                <input
                  type="date"
                  name="expiryDate"
                  value={formData.expiryDate}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                DESCRIPTION
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                placeholder="Optional description for this coupon"
              ></textarea>
            </div>

            {/* Active Status */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleChange}
                className="h-4 w-4 border-gray-300 focus:ring-0 focus:ring-offset-0"
              />
              <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                Coupon is active
              </label>
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-black text-white py-2 px-4 text-xs tracking-[0.2em] disabled:opacity-50"
              >
                {isSubmitting ? 'UPDATING...' : 'UPDATE COUPON'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
