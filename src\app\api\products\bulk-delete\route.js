import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Product from '@/models/Product';
import mongoose from 'mongoose';

export async function DELETE(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    await connectToDB();
    const { ids } = await request.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Product IDs array is required' },
        { status: 400 }
      );
    }

    // Start a transaction for bulk delete
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Delete all products in the transaction
      const result = await Product.deleteMany(
        { _id: { $in: ids } },
        { session }
      );

      await session.commitTransaction();
      session.endSession();

      return NextResponse.json({
        message: 'Products deleted successfully',
        deletedCount: result.deletedCount
      });
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error) {
    console.error('Error deleting products:', error);
    return NextResponse.json(
      { error: 'Failed to delete products' },
      { status: 500 }
    );
  }
}