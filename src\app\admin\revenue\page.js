'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { format } from 'date-fns';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import {
  FiTrendingUp,
  FiFilter,
  FiBarChart2,
  FiPieChart,
  FiShoppingCart,
  FiUsers,
  FiCreditCard,
  FiArrowLeft,
  FiRefreshCw,
  FiCalendar,
  FiDollarSign
} from 'react-icons/fi';
import { BiRupee } from 'react-icons/bi';

export default function RevenueManagement() {
  const router = useRouter();
  const { user, authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('30days'); // '30days', '12months'
  const [chartType, setChartType] = useState('line'); // 'line', 'bar'
  const [showFilters, setShowFilters] = useState(false);
  const [revenueData, setRevenueData] = useState({
    summary: {
      totalRevenue: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      revenueChange: 0,
    },
    dailyRevenue: [],
    topProducts: [],
    paymentMethods: [],
  });

  useEffect(() => {
    // Check if user is admin
    if (!authLoading && user) {
      if (user.role !== 'admin') {
        toast.error('You do not have permission to access this page');
        router.push('/');
      } else {
        fetchRevenueData();
      }
    } else if (!authLoading && !user) {
      toast.error('Please login to continue');
      router.push('/login');
    }
  }, [timeframe, user, authLoading, router]);

  const fetchRevenueData = async () => {
    try {
      setLoading(true);

      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/revenue?timeframe=${timeframe}&_=${timestamp}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Revenue API error:', errorData);

        if (response.status === 401) {
          toast.error('Please log in to access this page');
          router.push('/login');
          return;
        }

        if (response.status === 403) {
          toast.error('You do not have permission to access this page');
          router.push('/');
          return;
        }

        throw new Error(errorData.error || 'Failed to fetch revenue data');
      }

      const data = await response.json();

      // Validate the received data
      if (!data || !data.summary) {
        throw new Error('Invalid data received from server');
      }

      setRevenueData(data);

    } catch (error) {
      console.error('Error fetching revenue data:', error);
      toast.error(error.message || 'Failed to load revenue data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `₹${Number(amount).toLocaleString('en-IN')}`;
  };

  const formatChange = (change) => {
    const value = Number(change).toFixed(1);
    return value > 0 ? `+${value}%` : `${value}%`;
  };

  const getMethodDisplayName = (name) => {
    switch(name.toLowerCase()) {
      case 'cod': return 'Cash on Delivery';
      case 'razorpay': return 'Online Payment';
      case 'card': return 'Credit/Debit Card';
      case 'upi': return 'UPI';
      case 'netbanking': return 'Net Banking';
      case 'wallet': return 'Wallet';
      default: return name.charAt(0).toUpperCase() + name.slice(1);
    }
  };

  const getChangeColorClass = (change) => {
    const value = Number(change);
    if (value > 0) return 'text-green-700 bg-green-50 border-green-100';
    if (value < 0) return 'text-red-700 bg-red-50 border-red-100';
    return 'text-gray-700 bg-gray-50 border-gray-100';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-black/10 border-t-black rounded-full animate-spin mb-4 mx-auto"></div>
          <div className="text-xs uppercase tracking-wider text-black/60">Loading Revenue Data</div>
        </div>
      </div>
    );
  }





  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          {/* Back Button */}
          <div className="mb-4">
            <button
              onClick={() => router.push('/admin')}
              className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
            >
              <FiArrowLeft className="mr-1.5" />
              BACK TO ADMIN
            </button>
          </div>

          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
            <h1 className="text-2xl font-light tracking-tight">REVENUE</h1>
            <div className="flex items-center gap-2">
              <button
                onClick={() => fetchRevenueData()}
                className="bg-gray-800 text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
              >
                <FiRefreshCw className="mr-2" />
                REFRESH
              </button>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <FiFilter size={20} />
              </button>
            </div>
          </div>

          {/* Revenue Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className={`p-4 border ${getChangeColorClass(revenueData.summary.revenueChange)}`}>
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm">TOTAL REVENUE</p>
                <span className="text-xs flex items-center">
                  <FiTrendingUp className="mr-1" />
                  {formatChange(revenueData.summary.revenueChange)}
                </span>
              </div>
              <p className="text-2xl font-medium">{formatCurrency(revenueData.summary.totalRevenue)}</p>
            </div>
            <div className="p-4 border border-blue-100 bg-blue-50">
              <p className="text-sm text-blue-700">ORDERS</p>
              <p className="text-2xl font-medium text-blue-700">{revenueData.summary.totalOrders}</p>
            </div>
            <div className="p-4 border border-green-100 bg-green-50">
              <p className="text-sm text-green-700">AVG ORDER VALUE</p>
              <p className="text-2xl font-medium text-green-700">{formatCurrency(revenueData.summary.averageOrderValue)}</p>
            </div>
            <div className="p-4 border border-purple-100 bg-purple-50">
              <p className="text-sm text-purple-700">CONVERSION RATE</p>
              <p className="text-2xl font-medium text-purple-700">{revenueData.summary.conversionRate}%</p>
            </div>
          </div>

          {/* Time Period Indicator */}
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Showing data for: <span className="font-medium">
                {timeframe === '30days' ? 'Last 30 Days' :
                 timeframe === '7days' ? 'Last 7 Days' :
                 timeframe === '12months' ? 'Last 12 Months' :
                 'This Year'}
              </span>
            </p>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="px-6 py-3 border-t border-gray-100 bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xs uppercase tracking-wider text-black/60 mb-3">Time Period</h3>
                <div className="flex flex-wrap gap-2">
                  {['7days', '30days', '12months', 'year'].map(period => (
                    <button
                      key={period}
                      onClick={() => setTimeframe(period)}
                      className={`px-4 py-2 text-xs uppercase tracking-wider transition-colors ${
                        timeframe === period
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200 hover:bg-gray-100'
                      }`}
                    >
                      {period === '30days' ? 'Last 30 Days' :
                       period === '7days' ? 'Last 7 Days' :
                       period === '12months' ? 'Last 12 Months' :
                       'This Year'}
                    </button>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-xs uppercase tracking-wider text-black/60 mb-3">Chart Type</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => setChartType('line')}
                    className={`px-4 py-2 text-xs uppercase tracking-wider flex items-center gap-2 transition-colors ${
                      chartType === 'line'
                        ? 'bg-black text-white'
                        : 'bg-white text-black border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <FiTrendingUp size={14} /> Line Chart
                  </button>
                  <button
                    onClick={() => setChartType('bar')}
                    className={`px-4 py-2 text-xs uppercase tracking-wider flex items-center gap-2 transition-colors ${
                      chartType === 'bar'
                        ? 'bg-black text-white'
                        : 'bg-white text-black border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    <FiBarChart2 size={14} /> Bar Chart
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Revenue Chart */}
      <div className="p-6 border-b border-gray-200">
        <div className="bg-white border border-gray-200 p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
            <div>
              <h2 className="text-sm font-medium uppercase mb-1">Revenue Trend</h2>
              <p className="text-sm text-gray-500">
                {timeframe === '30days' ? 'Daily revenue for the last 30 days' :
                 timeframe === '7days' ? 'Daily revenue for the last 7 days' :
                 timeframe === '12months' ? 'Monthly revenue for the last 12 months' :
                 'Monthly revenue for this year'}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <div className="w-3 h-3 bg-black rounded-full"></div>
                <span>Revenue</span>
              </div>
            </div>
          </div>

          <div className="h-[450px]">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'line' ? (
                <LineChart data={revenueData.dailyRevenue} margin={{ top: 10, right: 30, left: 30, bottom: 30 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 11, fill: '#666' }}
                    tickFormatter={(value) => {
                      // Use displayDate if available, otherwise format the date
                      const item = revenueData.dailyRevenue.find(item => item.date === value);
                      if (item && item.displayDate) return item.displayDate;

                      const date = new Date(value);
                      if (timeframe === '7days') {
                        return format(date, 'EEE, dd MMM'); // Show day of week for 7-day view
                      } else if (timeframe === '30days') {
                        return format(date, 'dd MMM');
                      } else {
                        // For monthly data (12months or year)
                        return format(date, 'MMM yyyy');
                      }
                    }}
                    axisLine={{ stroke: '#e0e0e0' }}
                    tickLine={{ stroke: '#e0e0e0' }}
                    interval={timeframe === '30days' ? 4 : 0} // Show fewer ticks for 30 days
                    angle={timeframe === '30days' ? -45 : 0} // Angle the labels for better fit
                    textAnchor={timeframe === '30days' ? 'end' : 'middle'}
                    height={50} // Increase height for angled labels
                  />
                  <YAxis
                    tick={{ fontSize: 11, fill: '#666' }}
                    tickFormatter={(value) => {
                      // Format based on value range
                      if (value >= 100000) {
                        return `₹${(value/100000).toFixed(1)}L`; // Lakhs
                      } else if (value >= 1000) {
                        return `₹${(value/1000).toFixed(0)}K`; // Thousands
                      } else {
                        return `₹${value}`;
                      }
                    }}
                    axisLine={{ stroke: '#e0e0e0' }}
                    tickLine={{ stroke: '#e0e0e0' }}
                    width={60} // Increase width for larger labels
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #000',
                      borderRadius: '0',
                      fontSize: '12px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      padding: '10px 14px',
                    }}
                    formatter={(value) => [`₹${Number(value).toLocaleString('en-IN')}`, 'Revenue']}
                    labelFormatter={(value) => {
                      // Use displayDate if available, otherwise format the date
                      const item = revenueData.dailyRevenue.find(item => item.date === value);
                      if (item && item.displayDate) {
                        return timeframe === '12months' || timeframe === 'year'
                          ? item.displayDate // Just use the month and year
                          : `${item.displayDate}, ${new Date(value).getFullYear()}`; // Add the year for daily data
                      }

                      const date = new Date(value);
                      if (timeframe === '7days') {
                        return format(date, 'EEEE, dd MMMM yyyy'); // Full day name for 7-day view
                      } else if (timeframe === '30days') {
                        return format(date, 'dd MMMM yyyy');
                      } else {
                        return format(date, 'MMMM yyyy');
                      }
                    }}
                    cursor={{ stroke: '#ddd', strokeWidth: 1, strokeDasharray: '5 5' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#000"
                    strokeWidth={2}
                    dot={timeframe === '12months' || timeframe === 'year' ? { stroke: '#000', strokeWidth: 1, r: 4, fill: '#fff' } : { stroke: '#000', strokeWidth: 1, r: 3, fill: '#fff' }}
                    activeDot={{ stroke: '#000', strokeWidth: 2, r: 6, fill: '#fff' }}
                  />
                </LineChart>
              ) : (
                <BarChart data={revenueData.dailyRevenue} margin={{ top: 10, right: 30, left: 30, bottom: 30 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 11, fill: '#666' }}
                    tickFormatter={(value) => {
                      // Use displayDate if available, otherwise format the date
                      const item = revenueData.dailyRevenue.find(item => item.date === value);
                      if (item && item.displayDate) return item.displayDate;

                      const date = new Date(value);
                      if (timeframe === '7days') {
                        return format(date, 'EEE, dd MMM'); // Show day of week for 7-day view
                      } else if (timeframe === '30days') {
                        return format(date, 'dd MMM');
                      } else {
                        // For monthly data (12months or year)
                        return format(date, 'MMM yyyy');
                      }
                    }}
                    axisLine={{ stroke: '#e0e0e0' }}
                    tickLine={{ stroke: '#e0e0e0' }}
                    interval={timeframe === '30days' ? 4 : 0} // Show fewer ticks for 30 days
                    angle={timeframe === '30days' ? -45 : 0} // Angle the labels for better fit
                    textAnchor={timeframe === '30days' ? 'end' : 'middle'}
                    height={50} // Increase height for angled labels
                  />
                  <YAxis
                    tick={{ fontSize: 11, fill: '#666' }}
                    tickFormatter={(value) => {
                      // Format based on value range
                      if (value >= 100000) {
                        return `₹${(value/100000).toFixed(1)}L`; // Lakhs
                      } else if (value >= 1000) {
                        return `₹${(value/1000).toFixed(0)}K`; // Thousands
                      } else {
                        return `₹${value}`;
                      }
                    }}
                    axisLine={{ stroke: '#e0e0e0' }}
                    tickLine={{ stroke: '#e0e0e0' }}
                    width={60} // Increase width for larger labels
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #000',
                      borderRadius: '0',
                      fontSize: '12px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      padding: '10px 14px',
                    }}
                    formatter={(value) => [`₹${Number(value).toLocaleString('en-IN')}`, 'Revenue']}
                    labelFormatter={(value) => {
                      // Use displayDate if available, otherwise format the date
                      const item = revenueData.dailyRevenue.find(item => item.date === value);
                      if (item && item.displayDate) {
                        return timeframe === '12months' || timeframe === 'year'
                          ? item.displayDate // Just use the month and year
                          : `${item.displayDate}, ${new Date(value).getFullYear()}`; // Add the year for daily data
                      }

                      const date = new Date(value);
                      if (timeframe === '7days') {
                        return format(date, 'EEEE, dd MMMM yyyy'); // Full day name for 7-day view
                      } else if (timeframe === '30days') {
                        return format(date, 'dd MMMM yyyy');
                      } else {
                        return format(date, 'MMMM yyyy');
                      }
                    }}
                    cursor={{ fill: 'rgba(0,0,0,0.05)' }}
                  />
                  <Bar
                    dataKey="revenue"
                    fill="#000"
                    barSize={
                      timeframe === '7days' ? 40 :
                      timeframe === '30days' ? 12 :
                      timeframe === '12months' || timeframe === 'year' ? 30 : 20
                    }
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Top Products */}
      <div className="p-6 border-b border-gray-200">
        <div className="bg-white border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-sm font-medium uppercase">Top Performing Products</h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Units Sold
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Revenue
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {revenueData.topProducts.map((product, index) => {
                  // Calculate percentage of top product's revenue
                  const topRevenue = revenueData.topProducts[0]?.revenue || 1;
                  const percentage = (product.revenue / topRevenue * 100).toFixed(0);

                  return (
                    <tr key={product._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-8 h-8 bg-black/5 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {product.unitsSold}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {formatCurrency(product.revenue)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div className="bg-black h-2.5 rounded-full" style={{ width: `${percentage}%` }}></div>
                          </div>
                          <span className="ml-2 text-xs text-gray-500">{percentage}%</span>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="p-6">
        <div className="bg-white border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-sm font-medium uppercase">Payment Methods</h2>
            <FiPieChart size={18} className="text-gray-500" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Payment Methods Chart */}
            <div className="bg-gray-50 p-4 rounded flex items-center justify-center">
              <div className="w-full h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={revenueData.paymentMethods.map(method => ({
                        name: method.name,
                        value: method.total,
                        count: method.count,
                        percentage: (method.total / revenueData.summary.totalRevenue * 100).toFixed(1)
                      }))}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={100}
                      innerRadius={60}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {revenueData.paymentMethods.map((entry, index) => {
                        // Generate colors based on index
                        const colors = ['#000000', '#333333', '#666666', '#999999', '#cccccc'];
                        return <Cell key={`cell-${index}`} fill={colors[index % colors.length]} stroke="#fff" strokeWidth={1} />
                      })}
                    </Pie>
                    <Tooltip
                      formatter={(value, name, props) => [
                        `${formatCurrency(value)} (${props.payload.percentage}%)`,
                        `${getMethodDisplayName(name)}`
                      ]}
                      contentStyle={{
                        backgroundColor: '#fff',
                        border: '1px solid #e5e7eb',
                        borderRadius: '4px',
                        padding: '8px 12px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                        fontSize: '12px'
                      }}
                    />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      formatter={(value) => getMethodDisplayName(value)}
                      iconType="square"
                      iconSize={10}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Payment Methods Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Method
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transactions
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      %
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {revenueData.paymentMethods.map((method, index) => {
                    // Calculate percentage of total revenue
                    const percentage = (method.total / revenueData.summary.totalRevenue * 100).toFixed(1);
                    // Generate colors based on index
                    const colors = ['#000000', '#333333', '#666666', '#999999', '#cccccc'];
                    const color = colors[index % colors.length];

                    return (
                      <tr key={method.name} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: color }}></div>
                            <div className="text-sm font-medium text-gray-900">{getMethodDisplayName(method.name)}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {method.count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {formatCurrency(method.total)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {percentage}%
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total
                    </td>
                    <td className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {revenueData.paymentMethods.reduce((sum, method) => sum + method.count, 0)}
                    </td>
                    <td className="px-6 py-3 text-left text-xs font-medium text-gray-900 uppercase tracking-wider">
                      {formatCurrency(revenueData.summary.totalRevenue)}
                    </td>
                    <td className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      100%
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

