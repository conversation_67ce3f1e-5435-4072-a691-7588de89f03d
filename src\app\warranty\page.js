'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiCheckCircle, FiAlertTriangle, FiPackage, FiRefreshCw } from 'react-icons/fi';

export default function WarrantyPage() {
  // Warranty information cards
  const warrantyInfo = [
    {
      icon: FiCheckCircle,
      title: 'Standard Warranty',
      description: 'All furniture comes with a 1-year warranty against manufacturing defects, covering materials and workmanship.',
      color: 'bg-emerald-50 text-emerald-600',
    },
    {
      icon: FiAlertTriangle,
      title: 'Warranty Exclusions',
      description: 'Normal wear and tear, improper use, or damage caused by accidents are not covered under our warranty.',
      color: 'bg-amber-50 text-amber-600',
    },
    {
      icon: FiPackage,
      title: 'Returns & Exchanges',
      description: 'Unopened products can be returned within 7 days. Opened products may be eligible for exchange only.',
      color: 'bg-blue-50 text-blue-600',
    },
    {
      icon: FiRefreshCw,
      title: 'Refund Process',
      description: 'Refunds are processed within 7-10 business days after we receive and inspect the returned item.',
      color: 'bg-purple-50 text-purple-600',
    },
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-b from-amber-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Warranty Policy
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-600 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Our commitment to quality and customer satisfaction
            </motion.p>
          </div>
        </div>
      </section>

      {/* Warranty Cards Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {warrantyInfo.map((info, index) => (
                <motion.div
                  key={info.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100"
                >
                  <div className="p-6">
                    <div className={`${info.color} w-14 h-14 rounded-full flex items-center justify-center mb-6`}>
                      <info.icon className="h-7 w-7" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4">{info.title}</h3>
                    <p className="text-gray-600">{info.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Warranty Coverage</h2>
                <p>FurnitureBazaar stands behind the quality of our products. We offer the following warranty coverage:</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">1. Standard Warranty</h3>
                <p>All furniture purchased from FurnitureBazaar comes with a 1-year limited warranty from the date of delivery. This warranty covers manufacturing defects in materials and workmanship under normal household use.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">2. Extended Warranty</h3>
                <p>For select premium furniture collections, we offer an extended warranty of up to 5 years. Details of extended warranty coverage are provided with the product information at the time of purchase.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">3. Specific Product Warranties</h3>
                <ul className="list-disc pl-6 mb-6">
                  <li><strong>Wooden Furniture:</strong> 1-year warranty against manufacturing defects, including issues with joints, hardware, and structural integrity.</li>
                  <li><strong>Upholstered Furniture:</strong> 1-year warranty on frames and 6-month warranty on fabrics for manufacturing defects only.</li>
                  <li><strong>Mattresses:</strong> 5-year warranty against manufacturing defects, including sagging greater than 1.5 inches not associated with improper foundation support.</li>
                  <li><strong>Outdoor Furniture:</strong> 1-year warranty against manufacturing defects and premature deterioration of materials under normal weather conditions.</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Warranty Exclusions</h2>
                <p>Our warranty does not cover:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Normal wear and tear</li>
                  <li>Damage resulting from improper use, abuse, or accidents</li>
                  <li>Damage from improper cleaning, maintenance, or storage</li>
                  <li>Variations in color, grain, or texture of natural materials like wood and leather</li>
                  <li>Damage caused by pets, pests, or children</li>
                  <li>Damage from exposure to extreme temperature or humidity</li>
                  <li>Commercial use of residential furniture</li>
                  <li>Floor samples, "as-is" or clearance items</li>
                  <li>Transportation or delivery damages</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">How to Make a Warranty Claim</h2>
                <p>If you believe your furniture has a defect covered by our warranty, please follow these steps:</p>
                <ol className="list-decimal pl-6 mb-6">
                  <li>Contact our customer service <NAME_EMAIL> or call +91 80 1234 5678.</li>
                  <li>Provide your order number, detailed description of the issue, and clear photos showing the defect.</li>
                  <li>Our team will review your claim and respond within 2-3 business days.</li>
                  <li>If approved, we will arrange for repair, replacement, or other appropriate resolution.</li>
                </ol>
                <p>For warranty service, the original purchaser must present the original sales receipt or other proof of purchase.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Return Policy</h2>
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">1. Return Eligibility</h3>
                <ul className="list-disc pl-6 mb-6">
                  <li><strong>Unopened Products:</strong> May be returned within 7 days of delivery for a full refund (minus shipping charges).</li>
                  <li><strong>Opened Products:</strong> May be eligible for exchange only within 7 days if the item has a manufacturing defect.</li>
                  <li><strong>Custom-Made Furniture:</strong> Cannot be returned unless there is a manufacturing defect.</li>
                  <li><strong>Clearance or Sale Items:</strong> All sales are final unless the item has a manufacturing defect.</li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">2. Return Process</h3>
                <ol className="list-decimal pl-6 mb-6">
                  <li>Contact our customer service to initiate a return request within 7 days of delivery.</li>
                  <li>Obtain a Return Merchandise Authorization (RMA) number.</li>
                  <li>Pack the item securely in its original packaging if possible.</li>
                  <li>Schedule a pickup with our logistics team or arrange to return the item to our showroom.</li>
                </ol>

                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">3. Refund Process</h3>
                <p>Once we receive and inspect the returned item:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Approved refunds will be processed within 7-10 business days.</li>
                  <li>Refunds will be issued to the original payment method used for the purchase.</li>
                  <li>Shipping charges are non-refundable unless the return is due to our error.</li>
                  <li>A restocking fee of 15% may apply to certain returns that are not due to manufacturing defects.</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Delivery Damage</h2>
                <p>If your furniture arrives damaged:</p>
                <ol className="list-decimal pl-6 mb-6">
                  <li>Inspect all items at the time of delivery before signing for receipt.</li>
                  <li>Note any damage on the delivery receipt.</li>
                  <li>Take photos of the damage.</li>
                  <li>Contact our customer service within 24 hours of delivery.</li>
                </ol>
                <p>We will arrange for repair or replacement of items damaged during delivery at no cost to you.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Product Care</h2>
                <p>To maintain your warranty coverage and extend the life of your furniture, please follow the care instructions provided with your purchase. Proper care and maintenance are essential for preserving the quality and appearance of your furniture.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Contact Information</h2>
                <p>For warranty claims, returns, or any questions about our policies, please contact us at:</p>
                <div className="bg-gray-50 p-6 rounded-lg mt-4 mb-8">
                  <p className="mb-2"><strong>FurnitureBazaar Customer Service</strong></p>
                  <p className="mb-2">123 Furniture Street</p>
                  <p className="mb-2">Surat, Gujarat 395007</p>
                  <p className="mb-2">India</p>
                  <p className="mb-2">Email: <EMAIL></p>
                  <p>Phone: +91 80 1234 5678</p>
                </div>
              </motion.div>
            </div>

            <div className="mt-12 text-center">
              <Link 
                href="/" 
                className="inline-flex items-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-50 font-medium rounded-md transition duration-300"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 