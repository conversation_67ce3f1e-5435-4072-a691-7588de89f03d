import mongoose from 'mongoose';

const storeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Store name is required'],
    trim: true
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true
  },
  address: {
    type: String,
    required: [true, 'Address is required'],
    trim: true
  },
  fullAddress: {
    type: String,
    required: [true, 'Full address is required'],
    trim: true
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true
  },
  hours: {
    type: String,
    required: [true, 'Operating hours are required'],
    trim: true
  },
  features: {
    type: [String],
    default: []
  },
  image: {
    type: String,
    required: [true, 'Store image is required']
  },
  coordinates: {
    lat: {
      type: Number,
      default: null
    },
    lng: {
      type: Number,
      default: null
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt timestamp before saving
storeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Store = mongoose.models.Store || mongoose.model('Store', storeSchema);

export default Store;
