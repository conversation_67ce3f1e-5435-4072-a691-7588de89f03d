import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request) {
  try {
    console.log('Email check request received');
    const { email } = await request.json();

    // Validate input
    if (!email) {
      console.log('Email is required');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Check if user already exists (case insensitive)
    console.log('Checking if email exists:', email);
    const existingUser = await User.findOne({ email: email.toLowerCase() });

    console.log('Email exists:', !!existingUser);
    return NextResponse.json({
      exists: !!existingUser
    });
  } catch (error) {
    console.error('Email check error:', error);
    return NextResponse.json(
      { error: 'An error occurred while checking email' },
      { status: 500 }
    );
  }
}
