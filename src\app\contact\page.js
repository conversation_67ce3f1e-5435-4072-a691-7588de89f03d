'use client';

import { useState } from 'react';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import Image from 'next/image';
import Link from 'next/link';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    orderNumber: '',
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeAccordion, setActiveAccordion] = useState(null);

  // Contact information
  const contactInfo = [
    {
      title: 'Visit Our Showroom',
      details: ['123 Furniture Street', 'Surat, Gujarat 395007', 'India'],
    },
    {
      title: 'Call Us',
      details: ['+91 80 1234 5678', '+91 98765 43210'],
    },
    {
      title: 'Email Us',
      details: ['<EMAIL>', '<EMAIL>'],
    },
    {
      title: 'Business Hours',
      details: ['Monday - Saturday: 10:00 AM - 8:00 PM', 'Sunday: 11:00 AM - 6:00 PM'],
    },
  ];

  // FAQ items
  const faqItems = [
    {
      question: 'How can I track my order?',
      answer: 'You can track your order by logging into your account and visiting the "My Orders" section in your profile. There you\'ll find detailed information about your order status, including when it was processed, shipped, and estimated delivery date.'
    },
    {
      question: 'Do you offer free delivery?',
      answer: 'Yes, we offer free delivery for orders above ₹10,000 within city limits. For orders below this amount or for delivery outside city limits, a nominal delivery fee will be charged based on the distance.'
    },
    {
      question: 'What is your return policy?',
      answer: 'We offer a 7-day return policy for most items. If you\'re not satisfied with your purchase, you can return it within 7 days in its original condition and packaging. Custom-made furniture items cannot be returned unless they have manufacturing defects.'
    },
    {
      question: 'How long does delivery take?',
      answer: 'For in-stock items, delivery typically takes 3-5 business days. For custom orders or items that need to be manufactured, the delivery time can range from 2-4 weeks depending on the complexity of the item.'
    },
    {
      question: 'Do you offer assembly services?',
      answer: 'Yes, we provide professional assembly services for all our furniture. Our trained technicians will assemble your furniture at your home at a time convenient for you. This service is complimentary for orders above ₹20,000.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit and debit cards, UPI payments, net banking, and cash on delivery. For orders above ₹50,000, we also offer EMI options through select banks.'
    }
  ];

  // Form validation
  const validateForm = () => {
    let tempErrors = {};
    let formIsValid = true;

    if (!formData.name.trim()) {
      tempErrors.name = "Name is required";
      formIsValid = false;
    }

    if (!formData.email.trim()) {
      tempErrors.email = "Email is required";
      formIsValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      tempErrors.email = "Email is invalid";
      formIsValid = false;
    }

    if (formData.phone && !/^[0-9]{10}$/.test(formData.phone.replace(/[^0-9]/g, ''))) {
      tempErrors.phone = "Phone number is invalid";
      formIsValid = false;
    }

    if (!formData.subject.trim()) {
      tempErrors.subject = "Subject is required";
      formIsValid = false;
    }

    if (!formData.message.trim()) {
      tempErrors.message = "Message is required";
      formIsValid = false;
    } else if (formData.message.trim().length < 10) {
      tempErrors.message = "Message should be at least 10 characters";
      formIsValid = false;
    }

    setErrors(tempErrors);
    return formIsValid;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...formData, createdAt: new Date() }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Message sent successfully!');
        setFormData({ name: '', email: '', phone: '', subject: '', message: '', orderNumber: '' });
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleAccordion = (index) => {
    setActiveAccordion(activeAccordion === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="max-w-6xl mx-auto px-4 xl:px-0 py-12">
        {/* Header */}
        <div className="mb-24 text-center">
          <h1 className="text-2xl font-light mb-2">CONTACT US</h1>
          <p className="text-sm text-gray-500 max-w-xl mx-auto">
            Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
          </p>
        </div>

        {/* Contact Information */}
        <div className="mb-24 grid grid-cols-1 md:grid-cols-2 gap-16">
          <div>
            <h2 className="text-xl font-light mb-12">OUR INFORMATION</h2>
            <div className="space-y-8">
              {contactInfo.map((info, index) => (
                <div key={index} className="border-t border-gray-100 pt-6">
                  <h3 className="text-sm uppercase mb-4">{info.title}</h3>
                  <div className="space-y-1">
                    {info.details.map((detail, i) => (
                      <p key={i} className="text-sm text-gray-600">
                        {detail}
                      </p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Store Image */}
          <div className="relative aspect-[3/4] bg-gray-100">
            <Image
              src="https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg"
              alt="FurnitureBazaar Showroom"
              fill
              className="object-cover object-center"
            />
          </div>
        </div>

        {/* Contact Form */}
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">SEND US A MESSAGE</h2>
          <form onSubmit={handleSubmit} className="max-w-xl mx-auto">
            <div className="space-y-6">
              <div>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b ${errors.name ? 'border-red-500' : 'border-gray-200'} focus:ring-0 focus:border-black text-sm`}
                  placeholder="Name *"
                />
                {errors.name && <p className="mt-1 text-xs text-red-500">{errors.name}</p>}
              </div>

              <div>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b ${errors.email ? 'border-red-500' : 'border-gray-200'} focus:ring-0 focus:border-black text-sm`}
                  placeholder="Email *"
                />
                {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email}</p>}
              </div>

              <div>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b ${errors.phone ? 'border-red-500' : 'border-gray-200'} focus:ring-0 focus:border-black text-sm`}
                  placeholder="Phone (Optional)"
                />
                {errors.phone && <p className="mt-1 text-xs text-red-500">{errors.phone}</p>}
              </div>

              <div>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b ${errors.subject ? 'border-red-500' : 'border-gray-200'} focus:ring-0 focus:border-black text-sm`}
                  placeholder="Subject *"
                />
                {errors.subject && <p className="mt-1 text-xs text-red-500">{errors.subject}</p>}
              </div>

              <div>
                <input
                  type="text"
                  name="orderNumber"
                  value={formData.orderNumber}
                  onChange={handleChange}
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b border-gray-200 focus:ring-0 focus:border-black text-sm`}
                  placeholder="Order Number (if inquiry is about an order)"
                />
              </div>

              <div>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows="4"
                  className={`w-full px-0 py-2 bg-transparent border-0 border-b ${errors.message ? 'border-red-500' : 'border-gray-200'} focus:ring-0 focus:border-black text-sm resize-none`}
                  placeholder="Message *"
                ></textarea>
                {errors.message && <p className="mt-1 text-xs text-red-500">{errors.message}</p>}
              </div>

              <div className="pt-6">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200 disabled:bg-gray-400"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Store Location */}
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">FIND US</h2>
          <div className="h-96 w-full relative">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d119066.41619608562!2d72.73988409442378!3d21.159180249877504!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3be04e59411d1563%3A0xfe4558290938b042!2sSurat%2C%20Gujarat!5e0!3m2!1sen!2sin!4v1710599400000!5m2!1sen!2sin"
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen=""
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="FurnitureBazaar Surat Location"
              className="absolute inset-0"
            ></iframe>
          </div>
        </div>

        {/* FAQ */}
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">FREQUENTLY ASKED QUESTIONS</h2>
          <div className="space-y-6 max-w-3xl mx-auto">
            {faqItems.map((item, index) => (
              <div key={index} className="border-t border-gray-100 pt-4">
                <button
                  onClick={() => toggleAccordion(index)}
                  className="w-full text-left flex items-center justify-between pb-2"
                >
                  <span className="text-sm uppercase">{item.question}</span>
                  {activeAccordion === index ? (
                    <FiChevronUp size={16} />
                  ) : (
                    <FiChevronDown size={16} />
                  )}
                </button>
                {activeAccordion === index && (
                  <div className="py-4">
                    <p className="text-sm text-gray-600">{item.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}