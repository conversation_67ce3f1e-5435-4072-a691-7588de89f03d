'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { FiArrowLeft, FiSave, FiX } from 'react-icons/fi';

export default function EditStorePage({ params }) {
  const resolvedParams = use(params);
  const id = resolvedParams.id;
  const [formData, setFormData] = useState({
    name: '',
    city: '',
    address: '',
    fullAddress: '',
    phone: '',
    hours: '',
    features: [],
    image: '',
    coordinates: {
      lat: null,
      lng: null
    },
    status: 'active'
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [featureInput, setFeatureInput] = useState('');
  const router = useRouter();
  const { user } = useAuth();

  // Available features for selection
  const availableFeatures = [
    'Design Consultation',
    'Home Delivery',
    'Assembly Service',
    'Virtual Shopping',
    'Parking Available',
    'Wheelchair Accessible',
    'Click & Collect',
    'Interior Design Service'
  ];

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }
    
    fetchStoreData();
  }, [user, router, id]);

  const fetchStoreData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/stores/${id}`);
      
      if (response.ok) {
        const data = await response.json();
        setFormData({
          name: data.name || '',
          city: data.city || '',
          address: data.address || '',
          fullAddress: data.fullAddress || '',
          phone: data.phone || '',
          hours: data.hours || '',
          features: data.features || [],
          image: data.image || '',
          coordinates: data.coordinates || { lat: null, lng: null },
          status: data.status || 'active'
        });
      } else {
        setErrors({ submit: 'Failed to fetch store data' });
      }
    } catch (error) {
      console.error('Error fetching store data:', error);
      setErrors({ submit: 'An error occurred while fetching store data' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  const handleFeatureSelect = (feature) => {
    if (!formData.features.includes(feature)) {
      setFormData({
        ...formData,
        features: [...formData.features, feature]
      });
    }
  };

  const handleFeatureRemove = (feature) => {
    setFormData({
      ...formData,
      features: formData.features.filter(f => f !== feature)
    });
  };

  const handleCustomFeatureAdd = () => {
    if (featureInput.trim() && !formData.features.includes(featureInput.trim())) {
      setFormData({
        ...formData,
        features: [...formData.features, featureInput.trim()]
      });
      setFeatureInput('');
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) newErrors.name = 'Store name is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.address.trim()) newErrors.address = 'Address is required';
    if (!formData.fullAddress.trim()) newErrors.fullAddress = 'Full address is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.hours.trim()) newErrors.hours = 'Operating hours are required';
    if (!formData.image.trim()) newErrors.image = 'Store image URL is required';
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/admin/stores/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        router.push('/admin/stores');
      } else {
        const data = await response.json();
        setErrors({ submit: data.error || 'Failed to update store' });
      }
    } catch (error) {
      console.error('Error updating store:', error);
      setErrors({ submit: 'An error occurred while updating the store' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-screen-xl mx-auto px-4 md:px-6 pb-12">
        <div className="flex items-center justify-center h-64">
          <p className="text-sm tracking-[0.2em]">LOADING...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-screen-xl mx-auto px-4 md:px-6 pb-12">
      {/* Header with Back Button */}
      <div className="mb-8">
        <Link href="/admin/stores" className="inline-flex items-center text-sm hover:opacity-70 transition-opacity mb-4">
          <FiArrowLeft className="mr-2" /> BACK TO STORES
        </Link>
        <h1 className="text-xl font-light tracking-wide">Edit Store</h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="max-w-3xl">
        {errors.submit && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-600">
            {errors.submit}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Store Name */}
          <div>
            <label className="block text-sm mb-2">Store Name *</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* City */}
          <div>
            <label className="block text-sm mb-2">City *</label>
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.city ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
            />
            {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city}</p>}
          </div>

          {/* Address */}
          <div>
            <label className="block text-sm mb-2">Short Address *</label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.address ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
              placeholder="e.g. Phoenix Mall, Whitefield"
            />
            {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
          </div>

          {/* Full Address */}
          <div>
            <label className="block text-sm mb-2">Full Address *</label>
            <input
              type="text"
              name="fullAddress"
              value={formData.fullAddress}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.fullAddress ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
              placeholder="Complete address with pincode"
            />
            {errors.fullAddress && <p className="text-red-500 text-xs mt-1">{errors.fullAddress}</p>}
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm mb-2">Phone Number *</label>
            <input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.phone ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
              placeholder="+91 XXXXX XXXXX"
            />
            {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
          </div>

          {/* Hours */}
          <div>
            <label className="block text-sm mb-2">Operating Hours *</label>
            <input
              type="text"
              name="hours"
              value={formData.hours}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.hours ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
              placeholder="e.g. Monday - Sunday: 11:00 AM - 9:00 PM"
            />
            {errors.hours && <p className="text-red-500 text-xs mt-1">{errors.hours}</p>}
          </div>

          {/* Image URL */}
          <div className="md:col-span-2">
            <label className="block text-sm mb-2">Store Image URL *</label>
            <input
              type="text"
              name="image"
              value={formData.image}
              onChange={handleChange}
              className={`w-full p-2 border ${errors.image ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-black`}
              placeholder="https://example.com/image.jpg"
            />
            {errors.image && <p className="text-red-500 text-xs mt-1">{errors.image}</p>}
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm mb-2">Status</label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-black"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        {/* Features */}
        <div className="mb-6">
          <label className="block text-sm mb-2">Store Features</label>
          
          {/* Selected Features */}
          <div className="flex flex-wrap gap-2 mb-4">
            {formData.features.map((feature, index) => (
              <div key={index} className="flex items-center bg-gray-100 px-3 py-1">
                <span className="text-sm">{feature}</span>
                <button
                  type="button"
                  onClick={() => handleFeatureRemove(feature)}
                  className="ml-2 text-gray-500 hover:text-red-500"
                >
                  <FiX size={14} />
                </button>
              </div>
            ))}
          </div>
          
          {/* Available Features */}
          <div className="mb-4">
            <p className="text-xs text-gray-500 mb-2">SELECT FROM AVAILABLE FEATURES:</p>
            <div className="flex flex-wrap gap-2">
              {availableFeatures.map((feature) => (
                <button
                  key={feature}
                  type="button"
                  onClick={() => handleFeatureSelect(feature)}
                  disabled={formData.features.includes(feature)}
                  className={`text-xs px-3 py-1 border ${
                    formData.features.includes(feature)
                      ? 'border-gray-200 bg-gray-100 text-gray-400'
                      : 'border-gray-300 hover:border-black'
                  }`}
                >
                  {feature}
                </button>
              ))}
            </div>
          </div>
          
          {/* Custom Feature */}
          <div className="flex">
            <input
              type="text"
              value={featureInput}
              onChange={(e) => setFeatureInput(e.target.value)}
              placeholder="Add custom feature"
              className="flex-1 p-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-black"
            />
            <button
              type="button"
              onClick={handleCustomFeatureAdd}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 transition-colors"
            >
              Add
            </button>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end mt-8">
          <Link
            href="/admin/stores"
            className="mr-4 px-6 py-2 border border-gray-300 text-sm hover:bg-gray-100 transition-colors"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-black text-white text-sm hover:bg-black/80 transition-colors flex items-center"
          >
            <FiSave className="mr-2" />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}

