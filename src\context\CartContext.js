'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { toast } from 'react-hot-toast';

const CartContext = createContext();

export function CartProvider({ children }) {
  const [cart, setCart] = useState([]);
  const [wishlist, setWishlist] = useState([]);
  const [coupon, setCoupon] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const pathname = usePathname();

  // Load cart, wishlist, and coupon from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem('cart');
      const savedWishlist = localStorage.getItem('wishlist');
      const savedCoupon = localStorage.getItem('coupon');
      if (savedCart) setCart(JSON.parse(savedCart));
      if (savedWishlist) setWishlist(JSON.parse(savedWishlist));
      if (savedCoupon) setCoupon(JSON.parse(savedCoupon));
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('cart', JSON.stringify(cart));
    }
  }, [cart]);

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('wishlist', JSON.stringify(wishlist));
    }
  }, [wishlist]);

  // Save coupon to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (coupon) {
        localStorage.setItem('coupon', JSON.stringify(coupon));
      } else {
        localStorage.removeItem('coupon');
      }
    }
  }, [coupon]);

  // Check if user is logged in
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const response = await fetch('/api/auth/check');
        if (response.ok) {
          setIsLoggedIn(true);
        } else {
          setIsLoggedIn(false);
        }
      } catch (error) {
        console.error('Error checking login status:', error);
        setIsLoggedIn(false);
      }
    };

    checkLoginStatus();
  }, [pathname]); // Re-check when pathname changes

  // Sync cart with server when user is logged in
  useEffect(() => {
    if (isLoggedIn) {
      // Fetch user's cart from server
      const fetchUserCart = async () => {
        try {
          const response = await fetch('/api/user/cart');
          if (response.ok) {
            const data = await response.json();
            if (data.cart && data.cart.length > 0) {
              // Only update if server cart has items
              setCart(data.cart);
            } else if (cart.length > 0) {
              // If server cart is empty but local cart has items, sync to server
              await fetch('/api/user/cart', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cart }),
              });
            }
          }
        } catch (error) {
          console.error('Error fetching user cart:', error);
        }
      };

      fetchUserCart();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn]); // Only run when login status changes

  // Sync wishlist with server when user is logged in
  useEffect(() => {
    if (isLoggedIn) {
      // Fetch user's wishlist from server
      const fetchUserWishlist = async () => {
        try {
          const response = await fetch('/api/user/wishlist');
          if (response.ok) {
            const data = await response.json();
            if (data.wishlist && data.wishlist.length > 0) {
              // Only update if server wishlist has items
              setWishlist(data.wishlist);
            } else if (wishlist.length > 0) {
              // If server wishlist is empty but local wishlist has items, sync to server
              await fetch('/api/user/wishlist', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ wishlist }),
              });
            }
          }
        } catch (error) {
          console.error('Error fetching user wishlist:', error);
        }
      };

      fetchUserWishlist();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn]); // Only run when login status changes

  const addToCart = async (product, quantity = 1) => {
    try {
      // Check current stock before adding
      try {
        // Fetch the latest product data to check stock
        const response = await fetch(`/api/products/${product._id}`);
        if (response.ok) {
          const productData = await response.json();
          const existingItem = cart.find(item => item._id === product._id);
          const currentQuantity = existingItem ? existingItem.quantity : 0;

          if (productData.stock < (currentQuantity + quantity)) {
            const availableToAdd = Math.max(0, productData.stock - currentQuantity);

            if (availableToAdd <= 0) {
              toast.error(`Cannot add more. Maximum stock (${productData.stock}) already in cart`);
              return false;
            }

            toast.warning(`Only ${availableToAdd} more items available. Adjusted quantity.`);
            quantity = availableToAdd;
          }
        }
      } catch (error) {
        console.error('Error checking product stock:', error);
      }

      // Create the updated cart array first
      let updatedCart = [...cart];
      const existingItem = updatedCart.find(item => item._id === product._id);

      if (existingItem) {
        // If item exists, update its quantity
        updatedCart = updatedCart.map(item =>
          item._id === product._id
            ? { ...item, quantity: (item.quantity || 1) + quantity }
            : item
        );
      } else {
        // If item doesn't exist, add it with the specified quantity
        updatedCart.push({ ...product, quantity: quantity });
      }

      // Update state with the updated cart
      setCart(updatedCart);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/cart', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart: updatedCart }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error adding item to cart:', error);
      return false;
    }
  };

  const removeFromCart = async (productId) => {
    try {
      // Create the updated cart array first
      const updatedCart = cart.filter(item => item._id !== productId);

      // Update state with the filtered cart
      setCart(updatedCart);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/cart', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart: updatedCart }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error removing item from cart:', error);
      return false;
    }
  };

  const updateCartItemQuantity = async (productId, quantity) => {
    try {
      // First check if the requested quantity is available
      const item = cart.find(item => item._id === productId);
      if (item) {
        try {
          // Fetch the latest product data to check stock
          const response = await fetch(`/api/products/${productId}`);
          if (response.ok) {
            const productData = await response.json();

            if (productData.stock < quantity) {
              toast.error(`Only ${productData.stock} items available in stock`);
              // Update to maximum available stock instead
              quantity = productData.stock;
            }
          }
        } catch (error) {
          console.error('Error checking product stock:', error);
        }
      }

      // Create the updated cart array first
      const updatedCart = cart.map(item =>
        item._id === productId
          ? { ...item, quantity: Math.max(0, quantity) }
          : item
      );

      // Update state with the updated cart
      setCart(updatedCart);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/cart', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart: updatedCart }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error updating cart item quantity:', error);
      return false;
    }
  };

  const clearCart = async () => {
    try {
      // Update state with empty cart
      setCart([]);
      // Also clear the coupon when clearing the cart
      setCoupon(null);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/cart', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cart: [] }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error clearing cart:', error);
      return false;
    }
  };

  const addToWishlist = async (product) => {
    try {
      // Check if product is already in wishlist
      if (!wishlist.some(item => item._id === product._id)) {
        // Create the updated wishlist array first
        const updatedWishlist = [...wishlist, product];

        // Update state with the new wishlist
        setWishlist(updatedWishlist);

        // Sync with server if user is logged in
        if (isLoggedIn) {
          await fetch('/api/user/wishlist', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ wishlist: updatedWishlist }),
          });
        }
      }
      return true;
    } catch (error) {
      console.error('Error adding item to wishlist:', error);
      return false;
    }
  };

  const removeFromWishlist = async (productId) => {
    try {
      // Create the updated wishlist array first
      const updatedWishlist = wishlist.filter(item => item._id !== productId);

      // Update state with the filtered wishlist
      setWishlist(updatedWishlist);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/wishlist', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ wishlist: updatedWishlist }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error removing item from wishlist:', error);
      return false;
    }
  };

  const isInWishlist = (productId) => {
    return wishlist.some(item => item._id === productId);
  };

  const clearWishlist = async () => {
    try {
      // Update state with empty wishlist
      setWishlist([]);

      // Sync with server if user is logged in
      if (isLoggedIn) {
        await fetch('/api/user/wishlist', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ wishlist: [] }),
        });
      }

      return true;
    } catch (error) {
      console.error('Error clearing wishlist:', error);
      return false;
    }
  };

  // Function to validate cart items against current stock
  // Apply coupon code
  const applyCoupon = async (code) => {
    if (!code) {
      toast.error('Please enter a coupon code');
      return false;
    }

    if (!cart || cart.length === 0) {
      toast.error('Your cart is empty');
      return false;
    }

    try {
      // Calculate subtotal for validation
      const subtotal = cart.reduce((total, item) => {
        const price = item.discount > 0
          ? (item.price * (100 - item.discount)) / 100
          : item.price;
        return total + (price * item.quantity);
      }, 0);

      // Ensure coupon code is uppercase
      const uppercaseCode = code.toUpperCase();

      // Validate coupon with API
      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: uppercaseCode, subtotal })
      });

      if (!response.ok) {
        const errorData = await response.json();
        toast.error(errorData.error || 'Invalid coupon code');
        return false;
      }

      // Get coupon details
      const couponData = await response.json();

      // Set coupon in state
      setCoupon(couponData);

      toast.success('Coupon applied successfully');
      return true;
    } catch (error) {
      console.error('Error applying coupon:', error);
      toast.error('Failed to apply coupon');
      return false;
    }
  };

  // Remove coupon
  const removeCoupon = () => {
    setCoupon(null);
    toast.success('Coupon removed');
  };

  // Calculate discount amount from coupon
  const calculateCouponDiscount = (subtotal) => {
    if (!coupon) return 0;

    let discountAmount = 0;

    if (coupon.type === 'percentage') {
      discountAmount = (subtotal * coupon.value) / 100;

      // Apply maximum discount cap if set
      if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
        discountAmount = coupon.maxDiscount;
      }
    } else if (coupon.type === 'fixed') {
      discountAmount = coupon.value;

      // Ensure discount doesn't exceed subtotal
      if (discountAmount > subtotal) {
        discountAmount = subtotal;
      }
    }

    return discountAmount;
  };

  const validateCartStock = async () => {
    if (!cart || cart.length === 0) {
      return true; // No items to validate
    }

    let isValid = true;
    let updatedItems = false;

    try {
      // Use the bulk stock check API for better performance
      const response = await fetch('/api/products/stock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items: cart })
      });

      if (!response.ok) {
        console.error('Error validating stock:', await response.text());
        return true; // Assume valid if API fails to avoid blocking checkout
      }

      const stockData = await response.json();
      const stockErrors = [];

      // Create a new cart with validated quantities
      const validatedCart = [...cart];

      // Process the stock validation results
      stockData.items.forEach((result, index) => {
        if (!result.valid) {
          // If requested quantity exceeds available stock
          stockErrors.push({
            name: result.name,
            requested: result.requested,
            available: result.available
          });

          // Update the item quantity to match available stock
          validatedCart[index] = { ...validatedCart[index], quantity: result.available };
          updatedItems = true;

          if (result.available === 0) {
            isValid = false;
          }
        }
      });

      // Update cart if any quantities were adjusted
      if (updatedItems) {
        // Update state with the validated cart
        setCart(validatedCart);

        // Sync with server if user is logged in
        if (isLoggedIn) {
          try {
            await fetch('/api/user/cart', {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ cart: validatedCart }),
            });
          } catch (error) {
            console.error('Error syncing cart with server:', error);
          }
        }

        // Show notifications about stock issues
        stockErrors.forEach(error => {
          if (error.available === 0) {
            toast.error(`${error.name} is out of stock and has been removed from your cart`);
          } else {
            toast.warning(`Quantity for ${error.name} adjusted to ${error.available} (maximum available)`);
          }
        });
      }
    } catch (error) {
      console.error('Error in validateCartStock:', error);
      return true; // Assume valid if function fails to avoid blocking checkout
    }

    return isValid;
  };

  return (
    <CartContext.Provider
      value={{
        cart,
        addToCart,
        removeFromCart,
        updateCartItemQuantity,
        clearCart,
        wishlist,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        clearWishlist,
        validateCartStock,
        coupon,
        applyCoupon,
        removeCoupon,
        calculateCouponDiscount
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}