'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import {
  FiArrowLeft,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiAlertCircle,
  FiBell,
  FiCheck,
  FiX
} from 'react-icons/fi';

export default function EditUser({ params }) {
  const resolvedParams = use(params);
  const userId = resolvedParams.userId;
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    role: 'user',
    status: 'active',
    emailVerified: false,
    notifications: {
      orderUpdates: true,
      promotions: true,
      recommendations: true
    }
  });

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }

    fetchUserProfile();
  }, [user, userId, router]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        } else {
          throw new Error('Failed to fetch user profile');
        }
      }

      const data = await response.json();
      setUserProfile(data);
      
      // Initialize form data with user profile data
      setFormData({
        name: data.name || '',
        email: data.email || '',
        phone: data.phone || '',
        address: data.address || '',
        role: data.role || 'user',
        status: data.status || 'active',
        emailVerified: data.emailVerified || false,
        notifications: {
          orderUpdates: data.notifications?.orderUpdates ?? true,
          promotions: data.notifications?.promotions ?? true,
          recommendations: data.notifications?.recommendations ?? true
        }
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (key) => {
    setFormData(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: !prev.notifications[key]
      }
    }));
  };

  const handleToggleChange = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      
      // Prevent admin from changing their own role
      if (userId === user._id && formData.role !== userProfile.role) {
        toast.error('You cannot change your own role');
        return;
      }

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      toast.success('User updated successfully');
      router.push(`/admin/users/${userId}`);
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  if (!user || user.role !== 'admin') {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <h1 className="mt-4 text-xl font-medium text-gray-900">User Not Found</h1>
          <p className="mt-2 text-sm text-gray-500">The user you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => router.push('/admin/users')}
            className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => router.push(`/admin/users/${userId}`)}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO USER PROFILE
          </button>
        </div>

        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-light tracking-tight">Edit User</h1>
          <p className="mt-2 text-sm text-gray-500">Update user information and settings</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information */}
          <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-sm font-medium text-gray-900">Personal Information</h2>
            </div>
            <div className="p-6 space-y-6">
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <div className="flex items-center">
                  <FiUser className="text-gray-400 mr-2" />
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-black focus:border-black sm:text-sm"
                  />
                </div>
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <div className="flex items-center">
                  <FiMail className="text-gray-400 mr-2" />
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-black focus:border-black sm:text-sm"
                  />
                </div>
              </div>

              {/* Phone */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <div className="flex items-center">
                  <FiPhone className="text-gray-400 mr-2" />
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-black focus:border-black sm:text-sm"
                  />
                </div>
              </div>

              {/* Address */}
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <div className="flex items-start">
                  <FiMapPin className="text-gray-400 mr-2 mt-2" />
                  <textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    rows={3}
                    className="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-black focus:border-black sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Account Settings */}
          <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-sm font-medium text-gray-900">Account Settings</h2>
            </div>
            <div className="p-6 space-y-6">
              {/* Role */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  User Role
                </label>
                <div className="flex space-x-4">
                  {['user', 'admin'].map((role) => (
                    <label key={role} className="inline-flex items-center">
                      <input
                        type="radio"
                        name="role"
                        value={role}
                        checked={formData.role === role}
                        onChange={handleInputChange}
                        className="focus:ring-black h-4 w-4 text-black border-gray-300"
                        disabled={userId === user._id} // Prevent changing own role
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">
                        {role}
                      </span>
                    </label>
                  ))}
                </div>
                {userId === user._id && (
                  <p className="mt-1 text-xs text-red-500">
                    You cannot change your own role
                  </p>
                )}
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Status
                </label>
                <div className="flex space-x-4">
                  {['active', 'inactive'].map((status) => (
                    <label key={status} className="inline-flex items-center">
                      <input
                        type="radio"
                        name="status"
                        value={status}
                        checked={formData.status === status}
                        onChange={handleInputChange}
                        className="focus:ring-black h-4 w-4 text-black border-gray-300"
                        disabled={userId === user._id} // Prevent changing own status
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">
                        {status}
                      </span>
                    </label>
                  ))}
                </div>
                {userId === user._id && (
                  <p className="mt-1 text-xs text-red-500">
                    You cannot change your own account status
                  </p>
                )}
              </div>

              {/* Email Verification */}
              <div>
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    Email Verification Status
                  </label>
                  <button
                    type="button"
                    onClick={() => handleToggleChange('emailVerified')}
                    className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                      formData.emailVerified ? 'bg-black' : 'bg-gray-200'
                    }`}
                  >
                    <span className="sr-only">Toggle email verification</span>
                    <span
                      className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                        formData.emailVerified ? 'translate-x-5' : 'translate-x-0'
                      }`}
                    />
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {formData.emailVerified
                    ? 'Email is verified'
                    : 'Email is not verified'}
                </p>
              </div>
            </div>
          </div>

          {/* Notification Preferences */}
          <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-sm font-medium text-gray-900">Notification Preferences</h2>
            </div>
            <div className="p-6 space-y-4">
              {/* Order Updates */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiBell className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Order Updates</p>
                    <p className="text-xs text-gray-500">Receive notifications about order status changes</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleNotificationChange('orderUpdates')}
                  className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                    formData.notifications.orderUpdates ? 'bg-black' : 'bg-gray-200'
                  }`}
                >
                  <span className="sr-only">Toggle order updates</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                      formData.notifications.orderUpdates ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>

              {/* Promotions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiBell className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Promotions</p>
                    <p className="text-xs text-gray-500">Receive notifications about sales and promotions</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleNotificationChange('promotions')}
                  className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                    formData.notifications.promotions ? 'bg-black' : 'bg-gray-200'
                  }`}
                >
                  <span className="sr-only">Toggle promotions</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                      formData.notifications.promotions ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>

              {/* Recommendations */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FiBell className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Recommendations</p>
                    <p className="text-xs text-gray-500">Receive personalized product recommendations</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleNotificationChange('recommendations')}
                  className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                    formData.notifications.recommendations ? 'bg-black' : 'bg-gray-200'
                  }`}
                >
                  <span className="sr-only">Toggle recommendations</span>
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                      formData.notifications.recommendations ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={() => router.push(`/admin/users/${userId}`)}
              className="px-4 py-2 border border-gray-300 text-sm rounded-sm shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 border border-transparent text-sm rounded-sm shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {submitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
