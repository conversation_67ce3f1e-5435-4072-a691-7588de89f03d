import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request) {
  try {
    const { productId, quantity } = await request.json();
    const cookieStore = await cookies();
    const cartCookie = cookieStore.get('cart');

    let cart = cartCookie ? JSON.parse(cartCookie.value) : [];

    // Check if product already exists in cart
    const existingItemIndex = cart.findIndex(item => item.productId === productId);

    if (existingItemIndex >= 0) {
      // Update quantity if product exists
      cart[existingItemIndex].quantity += quantity;
    } else {
      // Add new item if product doesn't exist
      cart.push({ productId, quantity });
    }

    // Set cookie with updated cart
    const response = NextResponse.json({ message: 'Item added to cart', cart });
    response.cookies.set('cart', JSON.stringify(cart), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 1 week
    });

    return response;
  } catch (error) {
    console.error('Cart error:', error);
    return NextResponse.json(
      { error: 'Failed to update cart' },
      { status: 500 }
    );
  }
}