import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import User from '@/models/User';
import { cookies } from 'next/headers';
import { generateToken } from '@/lib/jwt';
import { sendVerificationEmail, sendWelcomeEmail } from '@/lib/email';

export async function POST(request) {
  try {
    const { name, email, password } = await request.json();

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email is already registered' },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await User.hashPassword(password);

    // Create user
    const user = new User({
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      role: 'user',
      emailVerified: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await user.save();

    // Generate verification token
    const verificationToken = generateToken({ userId: user._id }, '24h');

    // Send verification email
    const verificationResult = await sendVerificationEmail(email, verificationToken);
    if (!verificationResult.success) {
      console.error('Failed to send verification email:', verificationResult.error);
      // Don't return error to user, just log it
    }

    // Send welcome email
    const welcomeResult = await sendWelcomeEmail(email, name);
    if (!welcomeResult.success) {
      console.error('Failed to send welcome email:', welcomeResult.error);
      // Don't return error to user, just log it
    }

    // Generate session token
    const sessionToken = user._id.toString();

    // Set session cookie
    const cookieStore = await cookies();
    cookieStore.set('session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      // 7 days
      maxAge: 7 * 24 * 60 * 60
    });

    // Return user data (excluding password)
    const userData = {
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      createdAt: user.createdAt
    };

    return NextResponse.json({
      message: 'Registration successful. Please check your email to verify your account.',
      user: userData
    });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'An error occurred during registration' },
      { status: 500 }
    );
  }
}