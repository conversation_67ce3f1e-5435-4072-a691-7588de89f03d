import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import User from '@/models/User';

export async function PATCH(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;
    const { status } = await request.json();

    // Prevent changing own status
    if (userId === currentUser._id) {
      return NextResponse.json(
        { error: 'Cannot modify own account status' },
        { status: 400 }
      );
    }

    // Validate status
    if (!['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    console.log(`API: Updating user ${userId} status to ${status}`);

    // Update user status
    const user = await User.findByIdAndUpdate(
      userId,
      {
        $set: {
          status,
          lastStatusUpdate: new Date(),
          statusUpdatedBy: currentUser._id
        }
      },
      { new: true }
    ).select('-password');

    console.log('API: Updated user:', user);

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error updating user status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}