'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

export default function SustainableLiving() {
  const sections = [
    {
      type: 'hero',
      image: '/images/editorial/sustainable-hero.jpg',
      title: 'SUSTAINABLE LIVING',
      subtitle: 'EDITORIAL 2024'
    },
    {
      type: 'text',
      content: 'Discover our commitment to sustainability through thoughtfully crafted furniture that respects both nature and design. Each piece tells a story of conscious manufacturing and environmental responsibility.'
    },
    {
      type: 'full-width-image',
      image: '/images/editorial/sustainable-process.jpg',
      caption: 'Our sustainable manufacturing process in action'
    },
    {
      type: 'image-text',
      image: '/images/editorial/sustainable-materials.jpg',
      title: 'NATURAL MATERIALS',
      description: "We carefully select sustainable materials like FSC-certified wood, recycled metals, and organic textiles to create furniture that's both beautiful and environmentally responsible.",
      products: [
        { name: 'RECLAIMED WOOD DINING TABLE', price: '₹75,999', link: '/products/reclaimed-table' },
        { name: 'ORGANIC COTTON SOFA', price: '₹89,999', link: '/products/organic-sofa' }
      ]
    },
    {
      type: 'split-text',
      title: 'OUR COMMITMENT',
      columns: [
        'Every piece in our sustainable collection is crafted with a commitment to reducing environmental impact. From sourcing to production, we prioritize eco-friendly practices.',
        'We partner with certified suppliers who share our vision for a more sustainable future in furniture manufacturing.'
      ]
    },
    {
      type: 'image-grid',
      images: [
        {
          src: '/images/editorial/sustainable-detail1.jpg',
          caption: 'Handcrafted details'
        },
        {
          src: '/images/editorial/sustainable-detail2.jpg',
          caption: 'Natural finishes'
        },
        {
          src: '/images/editorial/sustainable-detail3.jpg',
          caption: 'Sustainable packaging'
        }
      ]
    },
    {
      type: 'image-text',
      image: '/images/editorial/sustainable-living-room.jpg',
      title: 'ECO-CONSCIOUS LIVING',
      description: "Transform your living space with furniture that makes a positive impact. Our sustainable collection proves that style and environmental responsibility can go hand in hand.",
      products: [
        { name: 'BAMBOO COFFEE TABLE', price: '₹32,999', link: '/products/bamboo-table' },
        { name: 'RECYCLED METAL SHELF', price: '₹28,999', link: '/products/eco-shelf' }
      ]
    }
  ];

  return (
    <div className="bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md">
        <div className="container mx-auto px-4">
          <div className="h-16 flex items-center justify-between">
            <Link
              href="/editorial"
              className="flex items-center text-sm tracking-wider hover:opacity-70"
            >
              <FiArrowLeft className="mr-2" />
              BACK TO EDITORIALS
            </Link>
            <Link
              href="/"
              className="text-sm tracking-wider hover:opacity-70"
            >
              FURNITUREBAAZAR
            </Link>
            <div className="w-[100px]" />
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {sections.map((section, index) => {
          switch (section.type) {
            case 'hero':
              return (
                <section key={index} className="relative h-screen">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-sm tracking-[0.2em] mb-4"
                    >
                      {section.subtitle}
                    </motion.p>
                    <motion.h1
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-4xl md:text-6xl tracking-[0.1em] text-center font-light px-4"
                    >
                      {section.title}
                    </motion.h1>
                  </div>
                </section>
              );

            case 'text':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <motion.p
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="max-w-3xl mx-auto text-center text-lg text-gray-600 leading-relaxed"
                    >
                      {section.content}
                    </motion.p>
                  </div>
                </section>
              );

            case 'split-text':
              return (
                <section key={index} className="py-24 bg-gray-50">
                  <div className="container mx-auto px-4">
                    <motion.h2
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="text-2xl tracking-[0.2em] text-center mb-16"
                    >
                      {section.title}
                    </motion.h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
                      {section.columns.map((text, idx) => (
                        <motion.p
                          key={idx}
                          initial={{ opacity: 0, y: 20 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          viewport={{ once: true }}
                          transition={{ delay: idx * 0.2 }}
                          className="text-gray-600 leading-relaxed"
                        >
                          {text}
                        </motion.p>
                      ))}
                    </div>
                  </div>
                </section>
              );

            case 'image-grid':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      {section.images.map((image, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: true }}
                          transition={{ delay: idx * 0.2 }}
                          className="space-y-4"
                        >
                          <div className="relative aspect-square">
                            <Image
                              src={image.src}
                              alt={image.caption}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <p className="text-sm text-gray-500 text-center">
                            {image.caption}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </section>
              );

            case 'image-text':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        className="relative aspect-[4/5]"
                      >
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                        />
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        className="space-y-8"
                      >
                        <h2 className="text-2xl tracking-[0.2em]">{section.title}</h2>
                        <p className="text-gray-600">{section.description}</p>
                        <div className="space-y-4">
                          {section.products.map((product, idx) => (
                            <Link
                              key={idx}
                              href={product.link}
                              className="group flex items-center justify-between border-b border-gray-200 pb-4"
                            >
                              <span className="text-sm tracking-wider">{product.name}</span>
                              <div className="flex items-center">
                                <span className="text-sm mr-4">{product.price}</span>
                                <FiArrowRight className="transform group-hover:translate-x-2 transition-transform" />
                              </div>
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </section>
              );

            case 'full-width-image':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <motion.div
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="relative aspect-[16/9]"
                    >
                      <Image
                        src={section.image}
                        alt={section.caption}
                        fill
                        className="object-cover"
                      />
                    </motion.div>
                    {section.caption && (
                      <p className="text-center text-sm text-gray-500 mt-4">
                        {section.caption}
                      </p>
                    )}
                  </div>
                </section>
              );

            default:
              return null;
          }
        })}
      </main>

      {/* Next Editorial Navigation */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <Link
            href="/editorial/urban-retreat"
            className="group block relative aspect-[16/9] overflow-hidden"
          >
            <Image
              src="/images/editorial/urban-retreat-hero.jpg"
              alt="Next Editorial"
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors" />
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
              <p className="text-sm tracking-[0.2em] mb-4">NEXT EDITORIAL</p>
              <h2 className="text-4xl tracking-[0.1em] font-light mb-8">URBAN RETREAT</h2>
              <FiArrowRight className="text-2xl" />
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}
