'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiTruck, FiClock, FiMapPin, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';

export default function ShippingInfoPage() {

  // Shipping zones
  const shippingZones = [
    {
      zone: "Zone 1 - Metro Cities",
      areas: "Delhi NCR, Mumbai, Bangalore, Chennai, Hyderabad, Kolkata, Pune, Ahmedabad",
      deliveryTime: "3-5 business days",
      freeShippingThreshold: "₹10,000",
      standardShipping: "₹499"
    },
    {
      zone: "Zone 2 - Tier 1 Cities",
      areas: "Jaipur, Lucknow, Kanpur, Nagpur, Indore, Thane, Bhopal, Visakhapatnam, Surat, Vadodara",
      deliveryTime: "5-7 business days",
      freeShippingThreshold: "₹15,000",
      standardShipping: "₹699"
    },
    {
      zone: "Zone 3 - Tier 2 Cities & Others",
      areas: "All other serviceable pin codes across India",
      deliveryTime: "7-10 business days",
      freeShippingThreshold: "₹20,000",
      standardShipping: "₹999"
    }
  ];

  return (
    <div className="bg-white min-h-screen pt-10 md:pt-16">
      {/* Hero Section - Zara Style */}
      <section className="relative py-8 md:py-12 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p
              className="text-xs uppercase tracking-wider text-gray-500 mb-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              Delivery Services
            </motion.p>
            <motion.h1
              className="text-2xl md:text-3xl font-light text-black mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              Shipping Information
            </motion.h1>
            <motion.p
              className="text-sm md:text-base text-gray-600 max-w-2xl"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              Everything you need to know about our delivery process, shipping zones, and assembly services for your furniture orders.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            {/* Shipping Process - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Our Shipping Process</h2>

              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiTruck className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Order Processing</h3>
                      <p className="text-sm text-gray-600">
                        Once your order is confirmed, it typically takes 1-2 business days to process before shipping.
                        You will receive an email confirmation when your order has been processed and is ready for shipping.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiClock className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Delivery Time</h3>
                      <p className="text-sm text-gray-600">
                        Delivery times vary based on your location and product availability. Standard delivery typically takes 3-10 business days depending on your shipping zone. Express delivery options may be available at checkout for select products and locations.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiMapPin className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Order Tracking</h3>
                      <p className="text-sm text-gray-600">
                        You'll receive a tracking number via email once your order ships, allowing you to monitor its progress.
                        You can also track your order by logging into your account and visiting the "My Orders" section.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Shipping Zones - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Shipping Zones & Rates</h2>

              <div className="space-y-8">
                {shippingZones.map((zone, index) => (
                  <div key={index} className="border-t border-black/5 pt-6">
                    <h3 className="text-base font-light mb-3">{zone.zone}</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Areas Covered</p>
                          <p className="text-sm">{zone.areas}</p>
                        </div>
                        <div>
                          <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Delivery Time</p>
                          <p className="text-sm">{zone.deliveryTime}</p>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Free Shipping</p>
                          <p className="text-sm">Orders above {zone.freeShippingThreshold}</p>
                        </div>
                        <div>
                          <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Standard Shipping</p>
                          <p className="text-sm">{zone.standardShipping}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <p className="mt-8 text-xs text-gray-500">
                * Delivery times are estimates and may vary based on product availability and external factors.
              </p>
            </motion.div>

            {/* Special Items - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Special Items & Custom Orders</h2>

              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiAlertCircle className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Custom & Made-to-Order Furniture</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Custom and made-to-order furniture items typically require 2-4 weeks for manufacturing before shipping.
                        The exact timeline will be communicated to you at the time of purchase.
                      </p>
                      <p className="text-sm text-gray-600">
                        These items may have different shipping rates and delivery timeframes than our standard products.
                        Our customer service team will provide you with specific details for your custom order.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiAlertCircle className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Oversized & Bulky Items</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Large furniture pieces like sofas, beds, dining tables, and wardrobes may require special handling
                        and delivery arrangements. These items are delivered by our specialized furniture delivery team.
                      </p>
                      <p className="text-sm text-gray-600">
                        Our team will contact you to schedule a delivery appointment for these items. Please ensure
                        there is adequate access to your home for large furniture delivery.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Assembly Services - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Assembly Services</h2>

              <div className="border-t border-black/5 pt-6">
                <div className="flex items-start">
                  <div className="mr-6">
                    <FiCheckCircle className="w-5 h-5 text-black" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-6">
                      We provide professional assembly services for all our furniture. Our trained technicians will
                      assemble your furniture at your home at a time convenient for you.
                    </p>

                    <div className="space-y-4 mb-6">
                      <div>
                        <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Complimentary Assembly</p>
                        <p className="text-sm">For orders above ₹20,000</p>
                      </div>

                      <div>
                        <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Standard Assembly Fee</p>
                        <p className="text-sm">Starting from ₹499, depending on the complexity of the furniture</p>
                      </div>

                      <div>
                        <p className="text-xs uppercase tracking-wider text-gray-500 mb-1">Scheduling</p>
                        <p className="text-sm">Our assembly team will contact you to schedule an appointment after your furniture is delivered</p>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600">
                      Please note that some small items like side tables, stools, and simple shelves may come fully assembled or require minimal assembly that you can easily do yourself.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Shipping Policies - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Additional Shipping Policies</h2>

              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Order Tracking</h3>
                  <p className="text-sm text-gray-600">
                    Once your order ships, you'll receive a tracking number via email. You can also track your order by logging into your account and visiting the "My Orders" section.
                  </p>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Delivery Appointments</h3>
                  <p className="text-sm text-gray-600">
                    For large furniture items, our delivery team will contact you to schedule a delivery appointment. Please ensure someone is available at the delivery address during the scheduled time to receive and inspect the items.
                  </p>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Shipping Restrictions</h3>
                  <p className="text-sm text-gray-600">
                    We currently ship to most locations across India. However, there may be some remote areas where our delivery services are limited. You can check if we deliver to your area by entering your pincode during checkout.
                  </p>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">International Shipping</h3>
                  <p className="text-sm text-gray-600">
                    We currently do not offer international shipping. Our services are limited to deliveries within India only.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Contact for Shipping Questions - Zara Style */}
            <motion.div
              className="mt-16 border-t border-black/5 pt-12"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-light mb-4">Have questions about shipping?</h3>
              <p className="text-sm text-gray-600 mb-8 max-w-lg">
                Our customer support team is here to help with any shipping-related inquiries. Feel free to reach out to us for assistance.
              </p>
              <div className="space-x-6">
                <Link
                  href="/contact"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                >
                  Contact Us
                </Link>
                <Link
                  href="/"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black/50 pb-1 hover:opacity-70 transition-opacity"
                >
                  Return to Home
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}