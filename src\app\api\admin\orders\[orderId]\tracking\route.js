import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';
import User from '@/models/User';
import OrderHistory from '@/models/OrderHistory';
import { sendOrderTrackingUpdateEmail } from '@/lib/email';
import mongoose from 'mongoose';

export async function PATCH(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;
    const { trackingNumber, trackingCompany, trackingUrl } = await request.json();

    // Validate tracking number
    if (!trackingNumber) {
      return NextResponse.json(
        { error: 'Tracking number is required' },
        { status: 400 }
      );
    }

    // Find order
    const order = await Order.findById(orderId);
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Start a transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Store previous status if we're going to change it
      const previousStatus = order.status;
      let statusChanged = false;

      // Update tracking information
      order.trackingNumber = trackingNumber;
      order.trackingCompany = trackingCompany || null;
      order.trackingUrl = trackingUrl || null;

      // If the order status is 'processing' and we're adding tracking for the first time,
      // update the status to 'shipped' and set the shippedAt date
      if (order.status === 'processing' && !order.shippedAt) {
        order.status = 'shipped';
        order.shippedAt = new Date();
        statusChanged = true;
      }

      // If this is the first time tracking is being added, set the shippedAt date
      if (!order.shippedAt && trackingNumber) {
        order.shippedAt = new Date();
      }

      // Save the order
      await order.save({ session });

      // Create history entry for tracking update
      const trackingHistory = new OrderHistory({
        order: order._id,
        status: order.status,
        previousStatus: statusChanged ? previousStatus : order.status,
        updatedBy: user._id,
        updatedAt: new Date(),
        note: `Tracking information updated: ${trackingNumber} (${trackingCompany || 'No carrier specified'})`
      });
      await trackingHistory.save({ session });

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();
    } catch (error) {
      // Abort the transaction if there's an error
      await session.abortTransaction();
      session.endSession();
      throw error;
    }

    // Send email notification to customer
    try {
      const customer = await User.findById(order.user);
      if (customer && customer.email) {
        // This function would need to be implemented in your email library
        await sendOrderTrackingUpdateEmail(
          customer.email,
          customer.name || 'Valued Customer',
          order
        );
      }
    } catch (emailError) {
      console.error('Failed to send tracking update email:', emailError);
      // Don't fail the API call if email sending fails
    }

    return NextResponse.json({
      message: 'Tracking information updated successfully',
      order: {
        _id: order._id,
        trackingNumber: order.trackingNumber,
        trackingCompany: order.trackingCompany,
        trackingUrl: order.trackingUrl,
        status: order.status,
        shippedAt: order.shippedAt
      }
    });
  } catch (error) {
    console.error('Error updating tracking information:', error);
    return NextResponse.json(
      { error: 'Failed to update tracking information' },
      { status: 500 }
    );
  }
}
