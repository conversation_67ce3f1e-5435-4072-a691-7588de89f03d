'use client';

import Image from 'next/image';
import Link from 'next/link';

export default function AboutPage() {
  // Company values
  const values = [
    {
      title: 'Quality Craftsmanship',
      description: 'We partner with skilled artisans who take pride in their work, ensuring every piece meets our high standards.',
    },
    {
      title: 'Customer Satisfaction',
      description: 'Your happiness is our priority. We go above and beyond to ensure a seamless shopping experience.',
    },
    {
      title: 'Sustainable Practices',
      description: 'We\'re committed to eco-friendly materials and manufacturing processes to protect our planet.',
    },
    {
      title: 'Innovation',
      description: 'We constantly explore new designs and technologies to bring you the most current furniture trends.',
    },
    {
      title: 'Integrity',
      description: 'Honesty and transparency guide every aspect of our business, from pricing to product descriptions.',
    },
  ];

  // Team members
  // const teamMembers = [
  //   {
  //     name: '<PERSON><PERSON>',
  //     position: 'Founder & CEO',
  //     image: '/images/team/rajiv-sharma.jpg',
  //     bio: 'With over 20 years in the furniture industry, <PERSON><PERSON> founded FurnitureBazaar with a vision to bring premium quality furniture to Indian homes at accessible prices.',
  //   },
  //   {
  //     name: '<PERSON><PERSON>',
  //     position: 'Head of Design',
  //     image: '/images/team/priya-patel.jpg',
  //     bio: 'A graduate from NID Ahmedabad, <PERSON><PERSON> brings her creative expertise to curate collections that blend traditional craftsmanship with contemporary aesthetics.',
  //   },
  //   {
  //     name: 'Arjun Mehta',
  //     position: 'Operations Director',
  //     image: '/images/team/arjun-mehta.jpg',
  //     bio: 'Arjun ensures our supply chain runs smoothly, from sourcing quality materials to delivering the final product to your doorstep.',
  //   },
  //   {
  //     name: 'Neha Gupta',
  //     position: 'Customer Experience Manager',
  //     image: '/images/team/neha-gupta.jpg',
  //     bio: 'Neha leads our customer service team with a passion for creating delightful experiences for every FurnitureBazaar customer.',
  //   },
  // ];

  // History timeline
  const historyTimeline = [
    {
      year: '2010',
      title: 'The Beginning',
      description: 'FurnitureBazaar was founded in Bangalore with a small workshop and showroom',
    },
    {
      year: '2013',
      title: 'Expanding Horizons',
      description: 'Opened our second showroom in Mumbai and launched our first e-commerce website',
    },
    {
      year: '2015',
      title: 'Product Innovation',
      description: 'Introduced our signature modular furniture collection, designed for urban Indian homes',
    },
    {
      year: '2018',
      title: 'Sustainability Initiative',
      description: 'Committed to using responsibly sourced wood and eco-friendly manufacturing processes',
    },
    {
      year: '2020',
      title: 'Digital Transformation',
      description: 'Enhanced online shopping experience with 3D visualization and virtual room planning',
    },
    {
      year: '2025',
      title: 'National Presence',
      description: 'Expanded to 25 cities across India with a network of showrooms and delivery hubs',
    },
  ];

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="max-w-6xl mx-auto px-4 xl:px-0 py-12">
        {/* Header */}
        <div className="mb-24 text-center">
          <h1 className="text-2xl font-light mb-2">ABOUT US</h1>
          <p className="text-sm text-gray-500 max-w-xl mx-auto">
            FurnitureBazaar transforms houses into homes with thoughtfully crafted furniture since 2010
          </p>
        </div>

        {/* Company Story */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-24">
          <div>
            <div className="relative aspect-[4/5] bg-gray-100">
              <Image
                src="https://cdn.cosmos.so/2e2aabaa-bdd7-4731-bae6-9df58941079d?format=jpeg"
                alt="Our workshop"
                fill
                className="object-cover object-center"
              />
            </div>
          </div>
          <div className="flex flex-col justify-center">
            <h2 className="text-xl font-light mb-8">OUR STORY</h2>
            <div className="space-y-4 text-sm text-gray-700">
              <p>FurnitureBazaar began with a simple idea: to create beautiful, well-crafted furniture that brings joy to Indian homes. What started as a small workshop in Bangalore has grown into a beloved furniture brand trusted by customers across the country.</p>
              <p>Our founder, Rajiv Sharma, comes from a family of traditional woodworkers. Combining his heritage of craftsmanship with modern design sensibilities, he established FurnitureBazaar to bridge the gap between mass-produced furniture and expensive boutique pieces.</p>
              <p>Today, we continue to honor those roots while embracing innovation. Every piece in our collection is thoughtfully designed, expertly crafted, and rigorously tested to ensure it meets our standards of quality, comfort, and durability.</p>
            </div>
          </div>
        </div>

        {/* Values */}
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">OUR VALUES</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {values.map((value, index) => (
              <div key={index} className="border-t border-gray-100 pt-6">
                <h3 className="text-sm uppercase mb-4">{value.title}</h3>
                <p className="text-sm text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="border-t border-b border-gray-100 py-16 mb-24">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-xl font-light mb-8">OUR MISSION</h2>
              <p className="text-sm text-gray-700">
                To create beautiful, functional, and accessible furniture that enhances the quality of life for our customers while supporting sustainable practices and traditional craftsmanship. We aim to blend timeless techniques with modern innovation, creating pieces that are not only aesthetically pleasing but also durable, practical, and reflective of Indian heritage.
              </p>
            </div>
            <div>
              <h2 className="text-xl font-light mb-8">OUR VISION</h2>
              <p className="text-sm text-gray-700">
                To be India's most trusted furniture brand, known for exceptional quality, innovative design, and customer-centric service. We envision a future where every Indian home has access to well-designed furniture that brings joy and comfort. We aspire to lead the industry in sustainable practices, setting new standards for environmental responsibility and ethical business operations.
              </p>
            </div>
          </div>
        </div>

        {/* Team
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">OUR TEAM</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-12">
            {teamMembers.map((member, index) => (
              <div key={index} className="group">
                <div className="relative aspect-[3/4] bg-gray-100 mb-4">
                  <Image
                    src={member.image}
                    alt={member.name}
                    fill
                    className="object-cover object-center"
                  />
                </div>
                <h3 className="text-sm uppercase mb-1">{member.name}</h3>
                <p className="text-xs text-gray-500 mb-3">{member.position}</p>
                <p className="text-xs text-gray-600 line-clamp-3 group-hover:line-clamp-none transition-all duration-300">
                  {member.bio}
                </p>
              </div>
            ))}
          </div>
        </div>  */}

        {/* Timeline */}
        <div className="mb-24">
          <h2 className="text-xl font-light mb-12 text-center">OUR JOURNEY</h2>
          <div className="space-y-8">
            {historyTimeline.map((item, index) => (
              <div key={index} className="grid grid-cols-12 gap-4">
                <div className="col-span-2 md:col-span-1 text-sm font-medium">
                  {item.year}
                </div>
                <div className="col-span-10 md:col-span-11 border-t border-gray-100 pt-4">
                  <h3 className="text-sm uppercase mb-2">{item.title}</h3>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mb-24">
          <h2 className="text-xl font-light mb-8">DISCOVER OUR COLLECTIONS</h2>
          <Link 
            href="/products" 
            className="inline-block px-8 py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200"
          >
            Shop Now
          </Link>
        </div>
      </div>
    </div>
  );
}
