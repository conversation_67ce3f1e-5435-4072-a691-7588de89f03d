import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Product from '@/models/Product';
import User from '@/models/User';
import { cookies } from 'next/headers';

// Get all products
export async function GET() {
  try {
    await connectToDB();
    // Return all products, including those with zero stock
    // Client-side filtering will handle display based on stock availability
    const products = await Product.find().sort({ createdAt: -1 });
    return NextResponse.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// Create a new product
export async function POST(request) {
  try {
    // Check admin authentication
    const cookieStore = await cookies();
    const sessionId = cookieStore.get('session')?.value;
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    await connectToDB();

    // Verify admin role
    const user = await User.findById(sessionId);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const data = await request.json();
    const { name, description, price, category, stock, images } = data;

    // Basic validation
    if (!name || !description || !price || !category || !stock || !images?.length) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Create and save product
    const product = new Product({
      name,
      description,
      price: Number(price),
      category,
      stock: Number(stock),
      images,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await product.save();
    console.log('Product created successfully:', product._id);

    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error creating product:', error);

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { error: Object.values(error.errors).map(err => err.message).join(', ') },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}

// Delete a product
export async function DELETE(request) {
  try {
    // Check admin authentication
    const cookieStore = await cookies();
    const sessionId = cookieStore.get('session')?.value;
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    await connectToDB();

    // Verify admin role
    const user = await User.findById(sessionId);
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    const { id } = await request.json();

    const product = await Product.findById(id);
    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Delete product from database
    await Product.findByIdAndDelete(id);

    return NextResponse.json({
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    );
  }
}
