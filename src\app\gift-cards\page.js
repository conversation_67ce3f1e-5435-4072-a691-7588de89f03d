'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import GiftCardForm from './GiftCardForm';
import { FiArrowLeft } from 'react-icons/fi';

export default function GiftCardsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Redirect if user is not logged in
    if (mounted && !authLoading && !user) {
      toast.error('Please login to purchase gift cards');
      router.push('/login');
    }
  }, [mounted, user, authLoading, router]);

  const handlePurchase = async (formData) => {
    try {
      setIsProcessing(true);

      // Create Razorpay order
      const orderResponse = await fetch('/api/payment/razorpay', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: formData.amount,
          currency: 'INR',
          receipt: `giftcard_${Date.now()}`,
          notes: {
            type: 'gift_card',
            recipientEmail: formData.recipientEmail,
            recipientName: formData.recipientName,
            senderName: formData.senderName,
            message: formData.message,
          },
        }),
      });

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json();
        throw new Error(errorData.error || 'Failed to create payment order');
      }

      const orderData = await orderResponse.json();

      // Initialize Razorpay
      const options = {
        key: orderData.key,
        amount: orderData.amount,
        currency: orderData.currency,
        name: 'FURNITUREBAZAAR',
        description: 'Gift Card Purchase',
        order_id: orderData.id,
        handler: async function (response) {
          try {
            // Verify payment signature
            const verifyResponse = await fetch('/api/payment/razorpay/', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature
              }),
            });

            if (!verifyResponse.ok) {
              const errorData = await verifyResponse.json();
              throw new Error(errorData.error || 'Payment verification failed');
            }

            // Create gift card
            const giftCardResponse = await fetch('/api/gift-cards', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                amount: formData.amount,
                recipientEmail: formData.recipientEmail,
                recipientName: formData.recipientName,
                senderName: formData.senderName,
                message: formData.message,
                paymentId: response.razorpay_payment_id,
              }),
            });

            if (!giftCardResponse.ok) {
              const errorData = await giftCardResponse.json();
              throw new Error(errorData.error || 'Failed to create gift card');
            }

            const giftCardData = await giftCardResponse.json();

            toast.success('Gift card purchased successfully!');
            router.push('/profile?section=gift-cards');
          } catch (error) {
            console.error('Error processing gift card:', error);
            toast.error(error.message || 'Failed to process gift card');
          }
        },
        prefill: {
          name: user?.name || '',
          email: user?.email || '',
        },
        theme: {
          color: '#000000',
        },
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error('Error purchasing gift card:', error);
      toast.error(error.message || 'Failed to purchase gift card');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!mounted || !user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8 py-4 md:py-6">
          <button
            onClick={() => router.back()}
            className="flex items-center text-sm hover:opacity-70 transition-opacity"
          >
            <FiArrowLeft className="mr-2" />
            Back
          </button>
          <h1 className="text-2xl md:text-3xl font-light mt-4">Gift Cards</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8 py-8 md:py-12">
        <div className="max-w-2xl mx-auto">
          <div className="mb-8 md:mb-12">
            <h2 className="text-xl font-light mb-4">Purchase a Gift Card</h2>
            <p className="text-gray-600">
              Send a FurnitureBazaar gift card to someone special. The recipient will receive an email with the gift card code and instructions on how to redeem it.
            </p>
          </div>

          <GiftCardForm onSubmit={handlePurchase} isProcessing={isProcessing} />
        </div>
      </div>
    </div>
  );
}
