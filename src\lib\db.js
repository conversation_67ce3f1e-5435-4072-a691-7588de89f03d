import mongoose from 'mongoose';

if (!process.env.MONGODB_URI) {
  throw new Error('Please add your Mongo URI to .env.local');
}

const uri = process.env.MONGODB_URI;
const options = {};

let clientPromise;

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoosePromise) {
    global._mongoosePromise = mongoose.connect(uri, options);
  }
  clientPromise = global._mongoosePromise;
} else {
  clientPromise = mongoose.connect(uri, options);
}

export async function connectToDatabase() {
  try {
    await clientPromise;
    return { db: mongoose.connection.db, client: mongoose.connection };
  } catch (error) {
    console.error('Error connecting to the database:', error);
    throw new Error('Failed to connect to the database');
  }
}

export default clientPromise;