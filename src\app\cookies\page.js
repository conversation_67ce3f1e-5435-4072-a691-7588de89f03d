'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON><PERSON>heck, FiInfo } from 'react-icons/fi';

export default function CookiesPage() {
  const [cookiePreferences, setCookiePreferences] = useState({
    necessary: true,
    functional: false,
    analytics: false,
    marketing: false
  });

  // <PERSON><PERSON> types data
  const cookieTypes = [
    {
      id: 'necessary',
      name: 'Necessary Cookies',
      description: 'These cookies are essential for the website to function properly. They enable basic functions like page navigation, secure areas access, and shopping cart functionality. The website cannot function properly without these cookies.',
      required: true,
      examples: ['Session cookies', 'Security cookies', 'Load balancing cookies']
    },
    {
      id: 'functional',
      name: 'Functional Cookies',
      description: 'These cookies enable the website to provide enhanced functionality and personalization. They may be set by us or by third-party providers whose services we have added to our pages.',
      required: false,
      examples: ['Language preference cookies', 'Region/location cookies', 'User interface customization cookies']
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously. They help us improve the way our website works.',
      required: false,
      examples: ['Google Analytics cookies', 'Performance measurement cookies', 'Visitor behavior cookies']
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'These cookies are used to track visitors across websites. The intention is to display ads that are relevant and engaging for the individual user and thereby more valuable for publishers and third-party advertisers.',
      required: false,
      examples: ['Advertising cookies', 'Social media cookies', 'Retargeting cookies']
    }
  ];

  // Third-party cookies data
  const thirdPartyCookies = [
    {
      provider: 'Google Analytics',
      purpose: 'Web analytics',
      cookieNames: ['_ga', '_gid', '_gat'],
      privacyPolicy: 'https://policies.google.com/privacy'
    },
    {
      provider: 'Facebook',
      purpose: 'Social media integration and advertising',
      cookieNames: ['_fbp', 'fr'],
      privacyPolicy: 'https://www.facebook.com/policy.php'
    },
    {
      provider: 'Hotjar',
      purpose: 'User behavior analysis',
      cookieNames: ['_hjid', '_hjSessionUser', '_hjFirstSeen'],
      privacyPolicy: 'https://www.hotjar.com/legal/policies/privacy/'
    }
  ];

  const handleCookiePreferenceChange = (id) => {
    if (id === 'necessary') return; // Cannot toggle necessary cookies
    setCookiePreferences({
      ...cookiePreferences,
      [id]: !cookiePreferences[id]
    });
  };

  const handleSavePreferences = () => {
    // In a real implementation, this would save the preferences to cookies/localStorage
    // and update the consent management platform
    alert('Your cookie preferences have been saved.');
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section - Zara Style */}
      <section className="relative py-24 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p 
              className="text-xs tracking-[0.3em] mb-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              PRIVACY
            </motion.p>
            <motion.h1 
              className="text-2xl md:text-3xl font-light uppercase tracking-wide mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Cookie Policy
            </motion.h1>
            <motion.p 
              className="text-sm text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Last Updated: April 15, 2024
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <div className="max-w-none">
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                {/* Introduction */}
                <p className="text-sm text-gray-600 mb-12">
                  At FurnitureBazaar, we use cookies and similar technologies to enhance your browsing experience, 
                  personalize content, analyze our traffic, and provide social media features. This Cookie Policy 
                  explains how we use cookies, your choices regarding cookies, and more information about them.
                </p>

                {/* What Are Cookies */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">What Are Cookies</h2>
                <p className="text-sm text-gray-600 mb-4">
                  Cookies are small text files that are stored on your device (computer, tablet, or mobile) when you 
                  visit websites. They are widely used to make websites work more efficiently, provide a better user 
                  experience, and give information to the website owners.
                </p>
                <p className="text-sm text-gray-600 mb-12">
                  Cookies are not harmful and do not contain viruses. They cannot access other information on your 
                  computer or extract personal information. Most browsers allow you to control cookies through their 
                  settings preferences.
                </p>

                {/* Types of Cookies We Use */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Types of Cookies We Use</h2>
                <p className="text-sm text-gray-600 mb-8">
                  We use different types of cookies for various purposes. Some cookies are essential for the functioning 
                  of our website, while others help us improve your experience.
                </p>

                <div className="space-y-8 mb-12">
                  {cookieTypes.map((cookieType) => (
                    <div key={cookieType.id} className="border-t border-black/5 pt-6">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-base font-light">{cookieType.name}</h3>
                        <div className="flex items-center">
                          {cookieType.required ? (
                            <span className="text-xs text-gray-500">Required</span>
                          ) : (
                            <button
                              className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 focus:outline-none ${
                                cookiePreferences[cookieType.id] ? 'bg-black' : 'bg-gray-300'
                              }`}
                              onClick={() => handleCookiePreferenceChange(cookieType.id)}
                              disabled={cookieType.required}
                            >
                              <span
                                className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
                                  cookiePreferences[cookieType.id] ? 'translate-x-6' : 'translate-x-1'
                                }`}
                              />
                            </button>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{cookieType.description}</p>
                      <div className="mt-3">
                        <p className="text-xs uppercase tracking-wider text-gray-500 mb-2">Examples:</p>
                        <ul className="text-sm space-y-1">
                          {cookieType.examples.map((example, index) => (
                            <li key={index} className="text-gray-600">{example}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                {/* How We Use Cookies */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">How We Use Cookies</h2>
                <p className="text-sm text-gray-600 mb-4">
                  We use cookies for various purposes, including:
                </p>
                <ul className="space-y-3 mb-12 text-sm text-gray-600">
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Remembering your preferences and settings</p>
                  </li>
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Keeping you signed in to your account</p>
                  </li>
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Understanding how you use our website</p>
                  </li>
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Improving our website based on analytics data</p>
                  </li>
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Providing personalized content and recommendations</p>
                  </li>
                  <li className="flex items-start">
                    <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                    <p>Measuring the effectiveness of our marketing campaigns</p>
                  </li>
                </ul>

                {/* Managing Your Cookie Preferences */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Managing Your Cookie Preferences</h2>
                <p className="text-sm text-gray-600 mb-4">
                  You can manage your cookie preferences in several ways:
                </p>
                <div className="space-y-6 mb-12">
                  <div className="border-t border-black/5 pt-4">
                    <h3 className="text-base font-light mb-2">Browser Settings</h3>
                    <p className="text-sm text-gray-600">
                      Most web browsers allow you to control cookies through their settings. You can usually find these 
                      settings in the "Options" or "Preferences" menu of your browser. For more information about how to 
                      manage cookies in your browser, you can visit:
                    </p>
                    <ul className="mt-3 space-y-1 text-sm">
                      <li><a href="https://support.google.com/chrome/answer/95647" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Google Chrome</a></li>
                      <li><a href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Mozilla Firefox</a></li>
                      <li><a href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Safari</a></li>
                      <li><a href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Microsoft Edge</a></li>
                    </ul>
                  </div>
                  <div className="border-t border-black/5 pt-4">
                    <h3 className="text-base font-light mb-2">Our Cookie Preferences Tool</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      You can also manage your preferences for non-essential cookies using our cookie preferences tool below:
                    </p>
                    <div className="border border-black/10 p-6">
                      <div className="space-y-4 mb-6">
                        {cookieTypes.map((cookieType) => (
                          <div key={cookieType.id} className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium">{cookieType.name}</p>
                              <p className="text-xs text-gray-500">{cookieType.required ? 'Required' : 'Optional'}</p>
                            </div>
                            <div>
                              <button
                                className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 focus:outline-none ${
                                  cookiePreferences[cookieType.id] ? 'bg-black' : 'bg-gray-300'
                                }`}
                                onClick={() => handleCookiePreferenceChange(cookieType.id)}
                                disabled={cookieType.required}
                              >
                                <span
                                  className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
                                    cookiePreferences[cookieType.id] ? 'translate-x-6' : 'translate-x-1'
                                  }`}
                                />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                      <button
                        onClick={handleSavePreferences}
                        className="w-full py-2 bg-black text-white text-sm hover:bg-gray-900 transition-colors"
                      >
                        Save Preferences
                      </button>
                    </div>
                  </div>
                  <div className="border-t border-black/5 pt-4">
                    <h3 className="text-base font-light mb-2">Third-Party Opt-Out Tools</h3>
                    <p className="text-sm text-gray-600">
                      For cookies used for advertising purposes, you can also opt out via:
                    </p>
                    <ul className="mt-3 space-y-1 text-sm">
                      <li><a href="https://www.youronlinechoices.eu/" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">European Interactive Digital Advertising Alliance (EDAA)</a></li>
                      <li><a href="https://optout.networkadvertising.org/" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Network Advertising Initiative (NAI)</a></li>
                      <li><a href="https://optout.aboutads.info/" target="_blank" rel="noopener noreferrer" className="underline hover:opacity-70 transition-opacity">Digital Advertising Alliance (DAA)</a></li>
                    </ul>
                  </div>
                </div>

                {/* Third-Party Cookies */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Third-Party Cookies</h2>
                <p className="text-sm text-gray-600 mb-6">
                  Some cookies are placed by third parties on our website. These third parties may collect information 
                  about your online activities across different websites. We do not control these third-party cookies, 
                  and they are subject to the third parties' privacy policies.
                </p>
                <div className="overflow-x-auto mb-12">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="border-b border-black/10">
                        <th className="py-3 text-left text-xs uppercase tracking-wider text-gray-500">Provider</th>
                        <th className="py-3 text-left text-xs uppercase tracking-wider text-gray-500">Purpose</th>
                        <th className="py-3 text-left text-xs uppercase tracking-wider text-gray-500">Cookie Names</th>
                        <th className="py-3 text-left text-xs uppercase tracking-wider text-gray-500">Privacy Policy</th>
                      </tr>
                    </thead>
                    <tbody>
                      {thirdPartyCookies.map((cookie, index) => (
                        <tr key={index} className="border-b border-black/5">
                          <td className="py-3 text-sm">{cookie.provider}</td>
                          <td className="py-3 text-sm">{cookie.purpose}</td>
                          <td className="py-3 text-sm">{cookie.cookieNames.join(', ')}</td>
                          <td className="py-3 text-sm">
                            <a 
                              href={cookie.privacyPolicy} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="underline hover:opacity-70 transition-opacity"
                            >
                              View
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Cookie Policy Updates */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Cookie Policy Updates</h2>
                <p className="text-sm text-gray-600 mb-4">
                  We may update this Cookie Policy from time to time to reflect changes in technology, regulation, or our 
                  business practices. Any changes will be posted on this page with an updated revision date. If we make 
                  significant changes, we will notify you through a prominent notice on our website.
                </p>
                <div className="flex items-start bg-gray-50 p-4 mb-12">
                  <FiInfo className="text-black mt-1 mr-3 flex-shrink-0" />
                  <p className="text-xs text-gray-600">
                    This Cookie Policy was last updated on April 15, 2024. Please check back periodically to stay informed 
                    about how we use cookies and related technologies.
                  </p>
                </div>

                {/* Contact Information */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Contact Us</h2>
                <p className="text-sm text-gray-600 mb-6">
                  If you have any questions about our use of cookies or this Cookie Policy, please contact us at:
                </p>
                <div className="border border-black/10 p-6 mb-12">
                  <p className="mb-2"><strong>FurnitureBazaar</strong></p>
                  <p className="mb-2">123 Furniture Street</p>
                  <p className="mb-2">Surat, Gujarat 395007</p>
                  <p className="mb-2">India</p>
                  <p className="mb-2">Email: <EMAIL></p>
                  <p>Phone: +91 80 1234 5678</p>
                </div>
              </motion.div>
            </div>

            {/* Related Links - Zara Style */}
            <div className="mt-16 pt-12 border-t border-black/10">
              <h3 className="text-xs tracking-[0.3em] mb-8">RELATED INFORMATION</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link 
                  href="/privacy" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  PRIVACY POLICY
                </Link>
                <Link 
                  href="/terms" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  TERMS OF USE
                </Link>
                <Link 
                  href="/contact" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  CONTACT US
                </Link>
              </div>
              <div className="mt-12">
                <Link 
                  href="/" 
                  className="text-xs tracking-[0.2em] hover:opacity-70 transition-opacity border-b border-black pb-1"
                >
                  BACK TO HOME
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
