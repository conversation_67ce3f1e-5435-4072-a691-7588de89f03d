import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import User from '@/models/User';
import Newsletter from '@/models/Newsletter';

export async function GET(request) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const sortField = searchParams.get('sort') || 'createdAt';
    const sortDirection = searchParams.get('direction') === 'asc' ? 1 : -1;

    // Build sort object
    const sort = { [sortField]: sortDirection };

    // Fetch all users
    const users = await User.find({ _id: { $ne: currentUser._id } })
      .select('-password')
      .sort(sort);

    // Get all newsletter subscribers
    const newsletterSubscribers = await Newsletter.find({
      status: 'active',
      email: {
        $in: users.map(user => user.email)
      }
    });

    // Create a set of subscribed emails for faster lookup
    const subscribedEmails = new Set(newsletterSubscribers.map(sub => sub.email));

    // Add newsletter subscription status to each user
    const usersWithNewsletterStatus = users.map(user => {
      const userObj = user.toObject();
      userObj.isNewsletterSubscriber = subscribedEmails.has(user.email);
      return userObj;
    });

    return NextResponse.json({
      users: usersWithNewsletterStatus
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { userIds, action } = await request.json();

    // Only handle delete action
    if (action === 'delete') {
      await User.deleteMany({ _id: { $in: userIds } });
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing bulk action:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}