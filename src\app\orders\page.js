'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiChevronRight,
  FiFilter,
  FiX,
  FiClock,
  FiPackage,
  FiTruck,
  FiCheckCircle,
  FiAlertCircle,
  FiSearch
} from 'react-icons/fi';

export default function OrdersPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'all',
    search: ''
  });

  useEffect(() => {
    // Only redirect if authentication check is complete and user is not logged in
    if (!authLoading && !user) {
      router.push('/login');
      return;
    }

    // Only fetch orders if user is available
    if (user) {
      fetchOrders();
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    applyFilters();
  }, [orders, filters]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/orders/user');

      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }

      const data = await response.json();
      setOrders(data);
      setFilteredOrders(data);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let result = [...orders];

    // Filter by status
    if (filters.status !== 'all') {
      result = result.filter(order => order.status === filters.status);
    }

    // Filter by date range
    const now = new Date();
    if (filters.dateRange === 'last30') {
      const thirtyDaysAgo = new Date(now.setDate(now.getDate() - 30));
      result = result.filter(order => new Date(order.createdAt) >= thirtyDaysAgo);
    } else if (filters.dateRange === 'last90') {
      const ninetyDaysAgo = new Date(now.setDate(now.getDate() - 90));
      result = result.filter(order => new Date(order.createdAt) >= ninetyDaysAgo);
    } else if (filters.dateRange === 'last365') {
      const yearAgo = new Date(now.setDate(now.getDate() - 365));
      result = result.filter(order => new Date(order.createdAt) >= yearAgo);
    }

    // Filter by search term
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(order =>
        order._id.toLowerCase().includes(searchTerm) ||
        order.items.some(item => item.name.toLowerCase().includes(searchTerm))
      );
    }

    setFilteredOrders(result);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    setFilters({
      status: 'all',
      dateRange: 'all',
      search: ''
    });
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  const formatPrice = (price) => {
    return price.toLocaleString('en-IN');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <FiClock className="text-yellow-500" />;
      case 'processing':
        return <FiPackage className="text-blue-500" />;
      case 'shipped':
        return <FiTruck className="text-indigo-500" />;
      case 'delivered':
        return <FiCheckCircle className="text-green-500" />;
      case 'cancelled':
        return <FiAlertCircle className="text-red-500" />;
      default:
        return <FiClock className="text-gray-500" />;
    }
  };

  const getStatusStyle = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'processing':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'shipped':
        return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'delivered':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-sm tracking-[0.2em]">LOADING...</div>
      </div>
    );
  }

  // Don't render anything if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
      <div className="max-w-screen-xl mx-auto py-8 md:py-12">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 md:mb-12">
          <h1 className="text-xl md:text-2xl font-light tracking-wide mb-4 md:mb-0">MY ORDERS</h1>

          {/* Search and Filter */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-grow max-w-xs">
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search orders..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 text-sm focus:outline-none focus:ring-1 focus:ring-black"
              />
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>

            <button
              onClick={() => setFilterOpen(!filterOpen)}
              className="flex items-center text-[10px] md:text-xs tracking-[0.2em] border border-black px-4 py-2 hover:bg-black hover:text-white transition-colors"
            >
              <FiFilter className="mr-2" />
              FILTER
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        <AnimatePresence>
          {filterOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8 overflow-hidden"
            >
              <div className="border border-gray-200 p-4 md:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-sm uppercase tracking-wider">Filter Orders</h2>
                  <button
                    onClick={() => setFilterOpen(false)}
                    className="text-gray-500 hover:text-black"
                  >
                    <FiX />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Status Filter */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-2">Order Status</label>
                    <select
                      value={filters.status}
                      onChange={(e) => handleFilterChange('status', e.target.value)}
                      className="w-full border border-gray-200 p-2 text-sm focus:outline-none focus:ring-1 focus:ring-black"
                    >
                      <option value="all">All Statuses</option>
                      <option value="pending">Pending</option>
                      <option value="processing">Processing</option>
                      <option value="shipped">Shipped</option>
                      <option value="delivered">Delivered</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>

                  {/* Date Range Filter */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-2">Date Range</label>
                    <select
                      value={filters.dateRange}
                      onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                      className="w-full border border-gray-200 p-2 text-sm focus:outline-none focus:ring-1 focus:ring-black"
                    >
                      <option value="all">All Time</option>
                      <option value="last30">Last 30 Days</option>
                      <option value="last90">Last 90 Days</option>
                      <option value="last365">Last Year</option>
                    </select>
                  </div>

                  {/* Reset Button */}
                  <div className="flex items-end">
                    <button
                      onClick={resetFilters}
                      className="text-xs tracking-wider border border-gray-200 px-4 py-2 hover:bg-gray-50 transition-colors"
                    >
                      RESET FILTERS
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Orders List */}
        {loading ? (
          <div className="space-y-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="border border-gray-100 p-6 animate-pulse">
                <div className="h-6 bg-gray-100 w-1/4 mb-4"></div>
                <div className="h-4 bg-gray-100 w-1/3 mb-6"></div>
                <div className="h-20 bg-gray-100"></div>
              </div>
            ))}
          </div>
        ) : filteredOrders.length === 0 ? (
          <div className="text-center py-16 border border-gray-100">
            {filters.status !== 'all' || filters.dateRange !== 'all' || filters.search ? (
              <>
                <p className="text-sm md:text-base font-light mb-6">No orders match your filters</p>
                <button
                  onClick={resetFilters}
                  className="text-[10px] md:text-xs tracking-[0.2em] border border-black px-4 py-2 hover:bg-black hover:text-white transition-colors"
                >
                  CLEAR FILTERS
                </button>
              </>
            ) : (
              <>
                <p className="text-sm md:text-base font-light mb-6">You haven't placed any orders yet</p>
                <Link
                  href="/products"
                  className="inline-block text-[10px] md:text-xs tracking-[0.2em] border border-black px-4 py-2 hover:bg-black hover:text-white transition-colors"
                >
                  START SHOPPING
                </Link>
              </>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {filteredOrders.map(order => (
              <div key={order._id} className="border border-gray-100 hover:border-gray-200 transition-colors">
                <div className="p-4 md:p-6 border-b border-gray-100">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <p className="text-[10px] md:text-xs tracking-[0.2em] text-gray-500 mb-1">
                        ORDER #{order._id.slice(-6)}
                      </p>
                      <p className="text-sm font-light">
                        Placed on {formatDate(order.createdAt)}
                      </p>
                    </div>

                    <div className="flex items-center mt-3 md:mt-0">
                      <div className={`flex items-center text-xs px-3 py-1 rounded-sm border ${getStatusStyle(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-2 uppercase">{order.status}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 md:p-6">
                  {/* Order Items Preview */}
                  <div className="mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {order.items.slice(0, 2).map((item, index) => (
                        <div key={index} className="flex items-center space-x-4">
                          <div className="w-16 h-16 relative flex-shrink-0 bg-gray-50">
                            {item.image && (
                              <Image
                                src={item.image}
                                alt={item.name}
                                fill
                                className="object-cover object-center"
                              />
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium line-clamp-1">{item.name}</p>
                            <p className="text-xs text-gray-500">Qty: {item.quantity}</p>
                            <p className="text-xs">₹{formatPrice(item.price)}</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {order.items.length > 2 && (
                      <p className="text-xs text-gray-500 mt-3">
                        +{order.items.length - 2} more item(s)
                      </p>
                    )}
                  </div>

                  {/* Order Summary and Action */}
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between pt-4 border-t border-gray-100">
                    <div>
                      <p className="text-sm font-medium">Total: ₹{formatPrice(order.total)}</p>
                      <div className="flex items-center gap-2">
                        <p className="text-xs text-gray-500">{order.items.length} item(s)</p>
                        {order.paymentMethod !== 'cod' && (
                          <span className={`text-xs px-2 py-0.5 rounded-full ${order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : order.paymentStatus === 'failed' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`}>
                            {order.paymentStatus === 'paid' ? 'Paid' : order.paymentStatus === 'failed' ? 'Payment Failed' : 'Payment Pending'}
                          </span>
                        )}
                      </div>
                    </div>

                    <Link
                      href={`/orders/${order._id}`}
                      className="flex items-center justify-center md:justify-start text-[10px] md:text-xs tracking-[0.2em] mt-4 md:mt-0 border border-black px-4 py-2 hover:bg-black hover:text-white transition-colors"
                    >
                      VIEW DETAILS
                      <FiChevronRight className="ml-1" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
