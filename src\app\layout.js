import { Inter } from 'next/font/google';
import './globals.css';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Providers from '@/components/Providers';

// Modify Inter font configuration
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  preload: true,
  // Specify exact weights needed instead of variable range
  weight: ['400', '500', '600', '700'],
  // Add fallback
  fallback: ['system-ui', '-apple-system', 'Segoe UI', 'Roboto', 'Arial', 'sans-serif']
});

export const metadata = {
  title: 'FurnitureBazaar - Premium Furniture Store',
  description: 'Your one-stop destination for premium furniture and home decor.',
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {/* Add Razorpay script directly in the head */}
        <script src="https://checkout.razorpay.com/v1/checkout.js" defer></script>
      </head>
      <body className={inter.className}>
        <Providers>
          <Navbar />
          <main className="pt-16">
            {children}
          </main>
          <Footer />
        </Providers>
      </body>
    </html>
  );
}
