import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Coupon from '@/models/Coupon';
import { getSessionUser } from '@/lib/auth';
import mongoose from 'mongoose';

// Get all coupons (admin only)
export async function GET(request) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const includeStats = searchParams.get('includeStats') === 'true';
    const sortField = searchParams.get('sort') || 'createdAt';
    const sortDirection = searchParams.get('direction') === 'asc' ? 1 : -1;

    // Build query
    let query = {};

    if (search) {
      // Search by code or description
      query.$or = [
        { code: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    if (status === 'active') {
      query.isActive = true;
    } else if (status === 'inactive') {
      query.isActive = false;
    } else if (status === 'expiring') {
      // Coupons expiring in the next 7 days
      const now = new Date();
      const sevenDaysLater = new Date(now);
      sevenDaysLater.setDate(sevenDaysLater.getDate() + 7);

      query.expiryDate = {
        $gte: now,
        $lte: sevenDaysLater
      };
      query.isActive = true;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Check if Coupon model exists
    if (!mongoose.models.Coupon) {
      return NextResponse.json({
        coupons: [],
        pagination: {
          total: 0,
          page,
          limit,
          pages: 0
        }
      });
    }

    // Build sort object
    const sort = { [sortField]: sortDirection };

    // Get coupons with pagination
    const coupons = await Coupon.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const totalCoupons = await Coupon.countDocuments(query);

    // Calculate statistics if requested
    let stats = null;
    if (includeStats) {
      const now = new Date();
      const sevenDaysLater = new Date(now);
      sevenDaysLater.setDate(sevenDaysLater.getDate() + 7);

      // Count active coupons
      const activeCoupons = await Coupon.countDocuments({ isActive: true });

      // Count inactive coupons
      const inactiveCoupons = await Coupon.countDocuments({ isActive: false });

      // Count coupons expiring soon (next 7 days)
      const expiringSoon = await Coupon.countDocuments({
        expiryDate: { $gte: now, $lte: sevenDaysLater },
        isActive: true
      });

      // Count expired coupons
      const expiredCoupons = await Coupon.countDocuments({
        expiryDate: { $lt: now }
      });

      stats = {
        total: totalCoupons,
        active: activeCoupons,
        inactive: inactiveCoupons,
        expiringSoon: expiringSoon,
        expired: expiredCoupons
      };
    }

    return NextResponse.json({
      coupons,
      pagination: {
        total: totalCoupons,
        page,
        limit,
        pages: Math.ceil(totalCoupons / limit)
      },
      stats
    });
  } catch (error) {
    console.error('Error getting coupons:', error);
    return NextResponse.json(
      { error: 'Failed to get coupons' },
      { status: 500 }
    );
  }
}

// Create a new coupon (admin only)
export async function POST(request) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const {
      code,
      type,
      value,
      minPurchase = 0,
      maxDiscount = null,
      startDate = new Date(),
      expiryDate,
      usageLimit = null,
      description = '',
      isActive = true
    } = body;

    // Validate required fields
    if (!code) {
      return NextResponse.json(
        { error: 'Coupon code is required' },
        { status: 400 }
      );
    }

    if (!type || !['percentage', 'fixed'].includes(type)) {
      return NextResponse.json(
        { error: 'Valid coupon type is required (percentage or fixed)' },
        { status: 400 }
      );
    }

    if (!value || value <= 0) {
      return NextResponse.json(
        { error: 'Valid discount value is required' },
        { status: 400 }
      );
    }

    if (type === 'percentage' && value > 100) {
      return NextResponse.json(
        { error: 'Percentage discount cannot exceed 100%' },
        { status: 400 }
      );
    }

    if (!expiryDate) {
      return NextResponse.json(
        { error: 'Expiry date is required' },
        { status: 400 }
      );
    }

    // Check if coupon code already exists
    const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
    if (existingCoupon) {
      return NextResponse.json(
        { error: 'Coupon code already exists' },
        { status: 400 }
      );
    }

    // Create coupon
    const coupon = new Coupon({
      code: code.toUpperCase(),
      type,
      value,
      minPurchase,
      maxDiscount,
      startDate,
      expiryDate,
      usageLimit,
      description,
      isActive,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Save coupon
    await coupon.save();

    return NextResponse.json({
      message: 'Coupon created successfully',
      coupon
    });
  } catch (error) {
    console.error('Error creating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to create coupon' },
      { status: 500 }
    );
  }
}
