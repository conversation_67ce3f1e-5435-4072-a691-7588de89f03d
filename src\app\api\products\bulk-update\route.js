import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Product from '@/models/Product';
import mongoose from 'mongoose';

export async function PATCH(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized. Admin access required.' },
        { status: 403 }
      );
    }

    await connectToDB();
    const { ids, update } = await request.json();

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Product IDs array is required' },
        { status: 400 }
      );
    }

    if (!update || typeof update !== 'object') {
      return NextResponse.json(
        { error: 'Update object is required' },
        { status: 400 }
      );
    }

    // Validate update fields
    const allowedFields = ['stock', 'price', 'discount'];
    const updateFields = Object.keys(update);
    const invalidFields = updateFields.filter(field => !allowedFields.includes(field));

    if (invalidFields.length > 0) {
      return NextResponse.json(
        { error: `Invalid update fields: ${invalidFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Start a transaction for bulk update
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Update all products in the transaction
      const result = await Product.updateMany(
        { _id: { $in: ids } },
        {
          $set: {
            ...update,
            updatedAt: new Date()
          }
        },
        { session }
      );

      await session.commitTransaction();
      session.endSession();

      return NextResponse.json({
        message: 'Products updated successfully',
        modifiedCount: result.modifiedCount
      });
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error) {
    console.error('Error updating products:', error);
    return NextResponse.json(
      { error: 'Failed to update products' },
      { status: 500 }
    );
  }
}