import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';

export async function GET(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;

    // Count orders for this user
    const count = await Order.countDocuments({ user: userId });

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Error counting user orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
