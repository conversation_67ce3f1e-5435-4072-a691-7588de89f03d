// This script generates order numbers for existing orders that don't have one
// Run with: node src/scripts/generateOrderNumbers.js

import { connectToDB } from '../lib/mongodb';
import Order from '../models/Order';
import { format } from 'date-fns';

// Function to generate a unique order number
const generateOrderNumber = (date, counter) => {
  const formattedDate = format(date, 'yyyyMMdd');
  const paddedCounter = String(counter).padStart(4, '0');
  return `FB-${formattedDate}-${paddedCounter}`;
};

async function generateOrderNumbers() {
  try {
    console.log('Connecting to database...');
    await connectToDB();
    console.log('Connected to database');

    // Find all orders without an order number
    const orders = await Order.find({ orderNumber: { $exists: false } }).sort({ createdAt: 1 });
    console.log(`Found ${orders.length} orders without order numbers`);

    if (orders.length === 0) {
      console.log('No orders need updating');
      process.exit(0);
    }

    // Group orders by date
    const ordersByDate = {};
    orders.forEach(order => {
      const date = new Date(order.createdAt);
      const dateKey = format(date, 'yyyy-MM-dd');
      
      if (!ordersByDate[dateKey]) {
        ordersByDate[dateKey] = [];
      }
      
      ordersByDate[dateKey].push(order);
    });

    // Process each date group
    for (const [dateKey, dateOrders] of Object.entries(ordersByDate)) {
      console.log(`Processing ${dateOrders.length} orders for date ${dateKey}`);
      
      // Find the highest counter for this date in existing order numbers
      const datePrefix = dateKey.replace(/-/g, '');
      const regex = new RegExp(`^FB-${datePrefix}-\\d{4}$`);
      const existingOrder = await Order.findOne({ orderNumber: regex }).sort({ orderNumber: -1 });
      
      let counter = 1;
      if (existingOrder && existingOrder.orderNumber) {
        const counterStr = existingOrder.orderNumber.split('-')[2];
        counter = parseInt(counterStr, 10) + 1;
      }
      
      // Update each order with a new order number
      for (const order of dateOrders) {
        const orderNumber = generateOrderNumber(new Date(order.createdAt), counter);
        console.log(`Assigning order number ${orderNumber} to order ${order._id}`);
        
        await Order.updateOne(
          { _id: order._id },
          { $set: { orderNumber } }
        );
        
        counter++;
      }
    }

    console.log('Order number generation complete');
    process.exit(0);
  } catch (error) {
    console.error('Error generating order numbers:', error);
    process.exit(1);
  }
}

// Run the migration
generateOrderNumbers();
