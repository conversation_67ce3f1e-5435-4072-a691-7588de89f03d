'use client';
import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import { FiArrowLeft, FiLoader, FiSend } from 'react-icons/fi';

export default function EditGiftCardPage({ params }) {
  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const giftCardId = resolvedParams.id;

  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [giftCard, setGiftCard] = useState(null);
  const [formData, setFormData] = useState({
    status: '',
    balance: 0,
    expiresAt: ''
  });

  useEffect(() => {
    if (giftCardId) {
      fetchGiftCard(giftCardId);
    }
  }, [giftCardId]);

  const fetchGiftCard = async (id) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/gift-cards/${id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch gift card');
      }

      const data = await response.json();
      setGiftCard(data);

      // Format date for input field
      const expiresAt = new Date(data.expiresAt).toISOString().split('T')[0];

      setFormData({
        status: data.status,
        balance: data.balance,
        expiresAt
      });
    } catch (error) {
      console.error('Error fetching gift card:', error);
      toast.error('Failed to load gift card');
      router.push('/admin/gift-cards');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'balance' ? Number(value) : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      // Validate form
      if (formData.balance < 0) {
        throw new Error('Balance cannot be negative');
      }

      // Update gift card
      const response = await fetch(`/api/admin/gift-cards/${giftCardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update gift card');
      }

      toast.success('Gift card updated successfully');
      router.push('/admin/gift-cards');
    } catch (error) {
      console.error('Error updating gift card:', error);
      toast.error(error.message || 'Failed to update gift card');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSendEmail = async () => {
    try {
      setIsSendingEmail(true);

      const response = await fetch(`/api/admin/gift-cards/${giftCardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          sendEmail: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send email');
      }

      toast.success('Gift card email sent successfully');
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error(error.message || 'Failed to send email');
    } finally {
      setIsSendingEmail(false);
    }
  };

  const formatCurrency = (amount) => {
    return `₹${Number(amount).toLocaleString('en-IN')}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8 flex justify-center items-center">
          <FiLoader className="animate-spin mr-2" />
          <span>Loading gift card...</span>
        </div>
      </div>
    );
  }

  if (!giftCard) {
    return (
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p>Gift card not found</p>
            <button
              onClick={() => router.push('/admin/gift-cards')}
              className="mt-4 px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
            >
              BACK TO GIFT CARDS
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center">
          <button
            onClick={() => router.back()}
            className="mr-4 text-gray-500 hover:text-black"
          >
            <FiArrowLeft size={20} />
          </button>
          <h1 className="text-xs tracking-[0.2em] text-gray-400">EDIT GIFT CARD</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Gift Card Details */}
          <div className="md:col-span-1 bg-white border border-black/10 p-6">
            <h2 className="text-xs tracking-[0.2em] mb-4">GIFT CARD DETAILS</h2>

            <div className="space-y-4">
              <div>
                <p className="text-xs text-gray-500">Code</p>
                <p className="font-medium">{giftCard.code}</p>
              </div>

              <div>
                <p className="text-xs text-gray-500">Amount</p>
                <p>{formatCurrency(giftCard.amount)}</p>
              </div>

              <div>
                <p className="text-xs text-gray-500">Created</p>
                <p>{formatDate(giftCard.createdAt)}</p>
              </div>

              <div>
                <p className="text-xs text-gray-500">Recipient</p>
                <p>{giftCard.recipientName}</p>
                <p className="text-sm text-gray-500">{giftCard.recipientEmail}</p>
              </div>

              <div>
                <p className="text-xs text-gray-500">Sender</p>
                <p>{giftCard.senderName}</p>
                <p className="text-sm text-gray-500">{giftCard.senderEmail}</p>
              </div>

              {giftCard.message && (
                <div>
                  <p className="text-xs text-gray-500">Message</p>
                  <p className="italic">"{giftCard.message}"</p>
                </div>
              )}

              <div className="pt-4">
                <button
                  type="button"
                  onClick={handleSendEmail}
                  className="w-full bg-indigo-600 text-white py-2 px-4 hover:bg-indigo-700 transition-colors flex items-center justify-center text-xs tracking-[0.2em]"
                  disabled={isSendingEmail || formData.status !== 'active'}
                >
                  {isSendingEmail ? (
                    <>
                      <FiLoader className="animate-spin mr-2" />
                      SENDING...
                    </>
                  ) : (
                    <>
                      <FiSend className="mr-2" />
                      RESEND EMAIL
                    </>
                  )}
                </button>
                {formData.status !== 'active' && (
                  <p className="mt-1 text-xs text-red-500">Gift card must be active to send email</p>
                )}
              </div>
            </div>
          </div>

          {/* Edit Form */}
          <div className="md:col-span-2 bg-white border border-black/10">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Status */}
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  STATUS
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                >
                  <option value="active">Active</option>
                  <option value="redeemed">Redeemed</option>
                  <option value="expired">Expired</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              {/* Balance */}
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  BALANCE (₹)
                </label>
                <input
                  type="number"
                  name="balance"
                  value={formData.balance}
                  onChange={handleChange}
                  min="0"
                  max={giftCard.amount}
                  step="1"
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
                <p className="mt-1 text-xs text-gray-500">
                  Original amount: {formatCurrency(giftCard.amount)}
                </p>
              </div>

              {/* Expiration Date */}
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  EXPIRATION DATE
                </label>
                <input
                  type="date"
                  name="expiresAt"
                  value={formData.expiresAt}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  className="w-full bg-black text-white py-3 px-6 hover:bg-gray-800 transition-colors flex items-center justify-center text-xs tracking-[0.2em]"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <FiLoader className="animate-spin mr-2" />
                      UPDATING...
                    </>
                  ) : (
                    'UPDATE GIFT CARD'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
