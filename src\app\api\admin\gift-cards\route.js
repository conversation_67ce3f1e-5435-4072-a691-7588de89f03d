import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';

// Get all gift cards (admin only)
export async function GET(request) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const sortField = searchParams.get('sort') || 'createdAt';
    const sortDirection = searchParams.get('direction') === 'asc' ? 1 : -1;
    const includeStats = searchParams.get('includeStats') === 'true';

    // Build query
    let query = {};

    if (status && status !== 'all') {
      query.status = status;
    }

    if (search) {
      query = {
        ...query,
        $or: [
          { code: { $regex: search, $options: 'i' } },
          { recipientEmail: { $regex: search, $options: 'i' } },
          { recipientName: { $regex: search, $options: 'i' } },
          { senderName: { $regex: search, $options: 'i' } }
        ]
      };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort = { [sortField]: sortDirection };

    // Get total count for pagination
    const totalGiftCards = await GiftCard.countDocuments(query);

    // Get gift cards with pagination
    const giftCards = await GiftCard.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('purchasedBy', 'name email')
      .populate('redeemedBy', 'name email');

    // Calculate statistics if requested
    let stats = null;
    if (includeStats) {
      // Count gift cards by status
      const activeCount = await GiftCard.countDocuments({ status: 'active' });
      const redeemedCount = await GiftCard.countDocuments({ status: 'redeemed' });
      const expiredCount = await GiftCard.countDocuments({ status: 'expired' });
      const cancelledCount = await GiftCard.countDocuments({ status: 'cancelled' });

      // Calculate total value of active gift cards
      const activeGiftCards = await GiftCard.find({ status: 'active' });
      const totalActiveValue = activeGiftCards.reduce((sum, card) => sum + card.balance, 0);

      // Calculate total value of all gift cards
      const allGiftCards = await GiftCard.find({});
      const totalValue = allGiftCards.reduce((sum, card) => sum + card.amount, 0);

      stats = {
        total: totalGiftCards,
        active: activeCount,
        redeemed: redeemedCount,
        expired: expiredCount,
        cancelled: cancelledCount,
        totalActiveValue,
        totalValue
      };
    }

    return NextResponse.json({
      giftCards,
      pagination: {
        total: totalGiftCards,
        page,
        limit,
        pages: Math.ceil(totalGiftCards / limit)
      },
      stats
    });
  } catch (error) {
    console.error('Error fetching gift cards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gift cards' },
      { status: 500 }
    );
  }
}

// Create a new gift card (admin only)
export async function POST(request) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const {
      amount,
      recipientEmail,
      recipientName,
      senderName,
      senderEmail,
      message = '',
      expiresAt = null
    } = body;

    // Validate required fields
    if (!amount || amount < 100) {
      return NextResponse.json(
        { error: 'Gift card amount must be at least ₹100' },
        { status: 400 }
      );
    }

    if (!recipientEmail) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    if (!recipientName) {
      return NextResponse.json(
        { error: 'Recipient name is required' },
        { status: 400 }
      );
    }

    if (!senderName) {
      return NextResponse.json(
        { error: 'Sender name is required' },
        { status: 400 }
      );
    }

    if (!senderEmail) {
      return NextResponse.json(
        { error: 'Sender email is required' },
        { status: 400 }
      );
    }

    // Set default expiration date (1 year from now) if not provided
    let expiration = expiresAt ? new Date(expiresAt) : new Date();
    if (!expiresAt) {
      expiration.setFullYear(expiration.getFullYear() + 1);
    }

    // Create gift card
    const giftCard = new GiftCard({
      amount,
      balance: amount,
      recipientEmail,
      recipientName,
      senderName,
      senderEmail,
      message,
      purchasedBy: user._id,
      expiresAt: expiration,
      paymentStatus: 'paid' // Admin-created cards are automatically marked as paid
    });

    // Save gift card
    const savedGiftCard = await giftCard.save();

    return NextResponse.json(savedGiftCard, { status: 201 });
  } catch (error) {
    console.error('Error creating gift card:', error);
    return NextResponse.json(
      { error: 'Failed to create gift card' },
      { status: 500 }
    );
  }
}
