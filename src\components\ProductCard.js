'use client';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FiHeart, FiX, FiChevronRight, FiShoppingBag } from 'react-icons/fi';
import { useCart } from '@/context/CartContext';
import { toast } from 'react-hot-toast';

const ProductCard = ({ product, viewMode = 'grid', minimal = false }) => {
  const router = useRouter();
  const [addingToCart, setAddingToCart] = useState(false);
  const [isImageHovered, setIsImageHovered] = useState(false);
  const { addToCart, addToWishlist, removeFromWishlist, isInWishlist } = useCart();
  const productInWishlist = isInWishlist(product._id);

  const handleClick = () => {
    router.push(`/products/${product._id}`);
  };

  const handleAddToCart = async (e) => {
    e.stopPropagation();

    // Prevent adding out-of-stock items
    if (product.stock === 0) {
      toast.error('This product is out of stock');
      return;
    }

    setAddingToCart(true);
    try {
      const success = await addToCart(product);
      if (success) {
        toast.success('Added to cart');
      } else {
        toast.error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error(error.message || 'Failed to add to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  const handleToggleWishlist = async (e) => {
    e.stopPropagation();
    try {
      if (productInWishlist) {
        const success = await removeFromWishlist(product._id);
        if (success) {
          toast.success('Removed from wishlist');
        } else {
          toast.error('Failed to remove from wishlist');
        }
      } else {
        const success = await addToWishlist(product);
        if (success) {
          toast.success('Added to wishlist');
        } else {
          toast.error('Failed to add to wishlist');
        }
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast.error('Failed to update wishlist');
    }
  };

  // Default grid view (IKEA style)
  return (
    <div className="bg-white group cursor-pointer" onClick={handleClick}>
      {/* Image Container */}
      <div className="relative aspect-square bg-[#f5f5f5]">
        <Image
          src={product.images[0]}
          alt={product.name}
          fill
          className="object-cover object-center transition-opacity duration-300"
          sizes="(max-width: 768px) 50vw, 33vw"
        />

        {/* Wishlist Button */}
        <button
          onClick={handleToggleWishlist}
          className="absolute top-4 right-4 z-10 p-2 bg-white rounded-full shadow-md hover:scale-110 transition-transform"
          aria-label={productInWishlist ? "Remove from wishlist" : "Add to wishlist"}
        >
          {productInWishlist ? (
            <FiX className="w-5 h-5" />
          ) : (
            <FiHeart className="w-5 h-5" />
          )}
        </button>

        {/* Discount Tag */}
        {product.discount > 0 && (
          <div className="absolute top-4 left-4 bg-[#cc0008] text-white px-2 py-1 text-sm font-medium">
            {product.discount}% off
          </div>
        )}

        {/* Stock Status Tags */}
        {product.stock === 0 ? (
          <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 text-xs font-medium">
            Out of Stock
          </div>
        ) : product.stock <= 5 && (
          <div className="absolute top-4 right-4 bg-amber-500 text-white px-2 py-1 text-xs font-medium">
            Low Stock
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-2">
        {/* Title and Category */}
        <div className="space-y-1">
          <h3 className="text-base font-medium text-[#111111] line-clamp-2">
            {product.name}
          </h3>
          <div className="flex justify-between items-center">
            <p className="text-sm text-[#484848]">{product.category}</p>
            {product.stock === 0 ? (
              <p className="text-xs font-medium text-red-500 bg-red-50 px-1.5 py-0.5 rounded">
                Out of Stock
              </p>
            ) : product.stock <= 5 && (
              <p className="text-xs font-medium text-amber-500 bg-amber-50 px-1.5 py-0.5 rounded">
                Low Stock: {product.stock}
              </p>
            )}
          </div>
        </div>

        {/* Price */}
        <div className="space-y-1">
          <div className="flex items-baseline gap-2">
            <span className="text-lg font-semibold text-[#111111]">
              ₹{((product.price * (100 - product.discount)) / 100).toLocaleString('en-IN')}
            </span>
            {product.discount > 0 && (
              <span className="text-sm text-[#484848] line-through">
                ₹{product.price.toLocaleString('en-IN')}
              </span>
            )}
          </div>
          {product.unit && (
            <p className="text-sm text-[#484848]">{product.unit}</p>
          )}
        </div>

        {/* Rating (if available) */}
        {product.rating && (
          <div className="flex items-center gap-1 text-sm text-[#484848]">
            <span>★</span>
            <span>{product.rating}</span>
            {product.reviews && (
              <span>({product.reviews})</span>
            )}
          </div>
        )}

        {/* Add to Cart Button */}
        <button
          onClick={(e) => handleAddToCart(e)}
          disabled={addingToCart || product.stock === 0}
          className="w-full mt-4 bg-[#0058a3] text-white py-3 px-4 flex items-center justify-center gap-2 hover:bg-[#004f93] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FiShoppingBag className="w-5 h-5" />
          <span>{addingToCart ? 'Adding...' : (product.stock === 0 ? 'Out of Stock' : 'Add to cart')}</span>
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
