'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiMapPin, FiPhone, FiClock, FiArrowRight, FiMap, FiChevronDown, FiX } from 'react-icons/fi';

export default function StoresPage() {
  const [selectedCity, setSelectedCity] = useState('all');
  const [hoveredStore, setHoveredStore] = useState(null);
  const [stores, setStores] = useState([]);
  const [cities, setCities] = useState([{ id: 'all', name: 'ALL LOCATIONS' }]);
  const [loading, setLoading] = useState(true);
  const [showFilter, setShowFilter] = useState(false);

  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stores');
      if (response.ok) {
        const data = await response.json();
        setStores(data);

        // Extract unique cities from store data
        const uniqueCities = [...new Set(data.map(store => store.city.toLowerCase()))];
        const cityOptions = [
          { id: 'all', name: 'ALL LOCATIONS' },
          ...uniqueCities.map(city => ({
            id: city,
            name: city.toUpperCase()
          }))
        ];
        setCities(cityOptions);
      }
    } catch (error) {
      console.error('Error fetching stores:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter stores based on selected city
  const filteredStores = selectedCity === 'all'
    ? stores
    : stores.filter(store => store.city.toLowerCase() === selectedCity);

  // Handle city selection
  const handleCitySelect = (cityId) => {
    setSelectedCity(cityId);
    setShowFilter(false); // Close filter on selection
  };

  // Store services
  const storeServices = [
    {
      title: 'Design Consultation',
      description: 'Book a free consultation with our interior design experts who will help you create your dream space.'
    },
    {
      title: 'Home Delivery',
      description: 'We offer delivery services to your doorstep with careful handling of all furniture items.'
    },
    {
      title: 'Assembly Service',
      description: 'Our professional team will assemble your furniture at your home, ensuring perfect installation.'
    },
    {
      title: 'Virtual Shopping',
      description: 'Shop from the comfort of your home with our virtual shopping service via video call.'
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section - Zara Style */}
      <section className="relative py-16 md:py-24 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p
              className="text-xs tracking-[0.3em] mb-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              VISIT US
            </motion.p>
            <motion.h1
              className="text-2xl md:text-3xl font-light uppercase tracking-wide mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Our Stores
            </motion.h1>
            <motion.p
              className="text-sm text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Experience our furniture collections in person at our showrooms across India.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            {/* City Filter */}
            <div className="mb-12 border-b border-black/5 pb-6">
              <div className="flex flex-wrap gap-6">
                {cities.map((city) => (
                  <button
                    key={city.id}
                    className={`text-xs tracking-wider ${
                      selectedCity === city.id
                        ? 'text-black border-b border-black pb-1'
                        : 'text-gray-500 hover:text-black transition-colors'
                    }`}
                    onClick={() => handleCitySelect(city.id)}
                  >
                    {city.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Store Listings */}
            {loading ? (
              <div className="flex justify-center items-center py-20">
                <p className="text-sm tracking-[0.2em]">LOADING STORES...</p>
              </div>
            ) : filteredStores.length === 0 ? (
              <div className="py-20 text-center">
                <p className="text-sm text-gray-500">No stores found in this location.</p>
              </div>
            ) : (
              <div className="space-y-16 mb-16">
                {filteredStores.map((store) => (
                  <motion.div
                    key={store._id}
                    className="border-t border-black/5 pt-8"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                    onMouseEnter={() => setHoveredStore(store._id)}
                    onMouseLeave={() => setHoveredStore(null)}
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <div className="relative aspect-square mb-4 overflow-hidden">
                          <Image
                            src={store.image}
                            alt={store.name}
                            fill
                            className={`object-cover transition-transform duration-700 ${
                              hoveredStore === store._id ? 'scale-105' : ''
                            }`}
                          />
                        </div>
                      </div>
                      <div>
                        <h2 className="text-xl font-light mb-4">{store.name}</h2>
                        <div className="space-y-4 text-sm">
                          <div className="flex items-start">
                            <FiMapPin className="mt-1 mr-3 flex-shrink-0" />
                            <div>
                              <p className="mb-1">{store.fullAddress}</p>
                              <a
                                href={`https://maps.google.com/?q=${encodeURIComponent(store.fullAddress)}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs underline hover:opacity-70 transition-opacity"
                              >
                                VIEW ON MAP
                              </a>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <FiPhone className="mt-1 mr-3 flex-shrink-0" />
                            <p>{store.phone}</p>
                          </div>
                          <div className="flex items-start">
                            <FiClock className="mt-1 mr-3 flex-shrink-0" />
                            <p>{store.hours}</p>
                          </div>
                        </div>
                        <div className="mt-6">
                          <p className="text-xs uppercase tracking-wider text-gray-500 mb-2">Available Services</p>
                          <div className="flex flex-wrap gap-2">
                            {store.features.map((feature, index) => (
                              <span
                                key={index}
                                className="text-xs px-3 py-1 border border-black/10 rounded-sm"
                              >
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Store Services */}
            <div className="border-t border-black/10 pt-12 mb-16">
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Store Services</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {storeServices.map((service, index) => (
                  <div key={index} className="border border-black/10 p-6">
                    <h3 className="text-base font-light mb-3">{service.title}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Store Policies */}
            <div className="border-t border-black/10 pt-12 mb-16">
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Store Policies</h2>
              <div className="space-y-6">
                <div className="border-t border-black/5 pt-4">
                  <h3 className="text-base font-light mb-2">Returns & Exchanges</h3>
                  <p className="text-sm text-gray-600">
                    Items purchased in-store can be returned or exchanged within 14 days of purchase with the original receipt.
                    Items must be in their original condition and packaging.
                  </p>
                </div>
                <div className="border-t border-black/5 pt-4">
                  <h3 className="text-base font-light mb-2">Payment Methods</h3>
                  <p className="text-sm text-gray-600">
                    We accept all major credit and debit cards, UPI payments, and cash. EMI options are available on select banks.
                  </p>
                </div>
                <div className="border-t border-black/5 pt-4">
                  <h3 className="text-base font-light mb-2">COVID-19 Safety Measures</h3>
                  <p className="text-sm text-gray-600">
                    We follow all local health guidelines to ensure a safe shopping experience. Masks may be required depending on local regulations.
                    Our stores are regularly sanitized, and hand sanitizer is available throughout.
                  </p>
                </div>
              </div>
            </div>

            {/* Book an Appointment */}
            <div className="border-t border-black/10 pt-12 mb-12">
              <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Book an Appointment</h2>
              <p className="text-sm text-gray-600 mb-6">
                Schedule a personalized shopping experience with our design consultants.
                Our experts will help you find the perfect furniture pieces for your space.
              </p>
              <Link
                href="/contact"
                className="inline-flex items-center text-sm hover:opacity-70 transition-opacity"
              >
                BOOK NOW <FiArrowRight className="ml-2" />
              </Link>
            </div>

            {/* Related Links - Zara Style */}
            <div className="mt-16 pt-12 border-t border-black/10">
              <h3 className="text-xs tracking-[0.3em] mb-8">RELATED INFORMATION</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link
                  href="/contact"
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  CONTACT US
                </Link>
                <Link
                  href="/shipping"
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  SHIPPING INFORMATION
                </Link>
                <Link
                  href="/returns"
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  RETURNS & EXCHANGES
                </Link>
              </div>
              <div className="mt-12">
                <Link
                  href="/"
                  className="text-xs tracking-[0.2em] hover:opacity-70 transition-opacity border-b border-black pb-1"
                >
                  BACK TO HOME
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
