'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { motion } from 'framer-motion';
import {
  FiArrowLeft,
  FiPackage,
  FiTruck,
  FiCheckCircle,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';

export default function OrderDetailPage({ params }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { user } = useAuth();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    const fetchOrder = async () => {
      try {
        const response = await fetch(`/api/orders/${resolvedParams.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Order not found');
          } else if (response.status === 403) {
            throw new Error('You do not have permission to view this order');
          } else {
            throw new Error('Failed to fetch order');
          }
        }

        const data = await response.json();
        setOrder(data);
      } catch (error) {
        console.error('Error fetching order:', error);
        setError(error.message);
        toast.error(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [user, resolvedParams.id, router]);

  const formatPrice = (price) => {
    return price.toLocaleString('en-IN');
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-IN', options);
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return {
          icon: <FiClock className="text-yellow-500" size={24} />,
          label: 'Order Pending',
          description: 'Your order has been received and is being reviewed.',
          color: 'bg-yellow-50 border-yellow-200',
          textColor: 'text-yellow-700',
          progress: 20
        };
      case 'processing':
        return {
          icon: <FiPackage className="text-blue-500" size={24} />,
          label: 'Processing',
          description: 'Your order is being processed and prepared for shipping.',
          color: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-700',
          progress: 40
        };
      case 'shipped':
        return {
          icon: <FiTruck className="text-indigo-500" size={24} />,
          label: 'Shipped',
          description: 'Your order has been shipped and is on its way to you.',
          color: 'bg-indigo-50 border-indigo-200',
          textColor: 'text-indigo-700',
          progress: 70
        };
      case 'delivered':
        return {
          icon: <FiCheckCircle className="text-green-500" size={24} />,
          label: 'Delivered',
          description: 'Your order has been delivered successfully.',
          color: 'bg-green-50 border-green-200',
          textColor: 'text-green-700',
          progress: 100
        };
      case 'cancelled':
        return {
          icon: <FiAlertCircle className="text-red-500" size={24} />,
          label: 'Cancelled',
          description: 'This order has been cancelled.',
          color: 'bg-red-50 border-red-200',
          textColor: 'text-red-700',
          progress: 0
        };
      default:
        return {
          icon: <FiClock className="text-gray-500" size={24} />,
          label: 'Processing',
          description: 'Your order is being processed.',
          color: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-700',
          progress: 20
        };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
        <div className="max-w-screen-xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-100 w-1/4 mb-6"></div>
            <div className="h-40 bg-gray-100 mb-6"></div>
            <div className="h-60 bg-gray-100"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl mb-4">{error}</h2>
          <Link
            href="/profile"
            className="text-sm underline"
          >
            Back to Profile
          </Link>
        </div>
      </div>
    );
  }

  if (!order) return null;

  const statusInfo = getStatusInfo(order.status);

  return (
    <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
      <div className="max-w-screen-xl mx-auto">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/profile')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO PROFILE
          </button>
        </div>

        {/* Order Header */}
        <div className="mb-8 md:mb-12">
          <h1 className="text-xl md:text-2xl font-light tracking-wide mb-2">ORDER DETAILS</h1>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm text-black/70">
            <p>Order #{order._id.slice(-6)}</p>
            <p>Placed on {formatDate(order.createdAt)}</p>
          </div>
        </div>

        {/* Order Status */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className={`border ${statusInfo.color} p-4 md:p-6 mb-8 md:mb-12 rounded-sm`}
        >
          <div className="flex items-start md:items-center gap-4">
            {statusInfo.icon}
            <div>
              <h3 className={`font-medium ${statusInfo.textColor}`}>{statusInfo.label}</h3>
              <p className="text-sm text-black/70">{statusInfo.description}</p>
            </div>
          </div>

          {/* Progress Bar (not shown for cancelled orders) */}
          {order.status !== 'cancelled' && (
            <div className="mt-4 md:mt-6">
              <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                <div
                  className="h-full bg-black transition-all duration-500 ease-out"
                  style={{ width: `${statusInfo.progress}%` }}
                ></div>
              </div>
              <div className="flex justify-between mt-2 text-[10px] text-black/50 uppercase tracking-wider">
                <span>Order Placed</span>
                <span>Processing</span>
                <span>Shipped</span>
                <span>Delivered</span>
              </div>
            </div>
          )}
        </motion.div>

        {/* Order Items */}
        <div className="mb-8 md:mb-12">
          <h2 className="text-sm uppercase tracking-wider mb-4 md:mb-6">Items in Your Order</h2>
          <div className="space-y-4 md:space-y-6">
            {order.items.map((item, index) => (
              <div key={index} className="flex gap-4 border-b border-black/5 pb-4">
                <div className="w-20 aspect-square relative flex-shrink-0 bg-gray-50">
                  {item.image && (
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      className="object-cover object-center"
                    />
                  )}
                </div>
                <div className="flex-grow">
                  <h3 className="text-sm font-medium">{item.name}</h3>
                  <p className="text-xs text-black/70 mt-1">Qty: {item.quantity}</p>
                  {item.discount > 0 ? (
                    <div className="mt-1">
                      <p className="text-xs">₹{formatPrice(item.price)}</p>
                      <p className="text-xs text-gray-500 line-through">₹{formatPrice(item.originalPrice)}</p>
                      <p className="text-xs text-green-600">{item.discount}% OFF</p>
                    </div>
                  ) : (
                    <p className="text-xs mt-1">₹{formatPrice(item.price)}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Summary */}
        <div className="mb-8 md:mb-12">
          <h2 className="text-sm uppercase tracking-wider mb-4 md:mb-6">Order Summary</h2>
          <div className="border-t border-b border-black/5 py-4 md:py-6">
            <div className="space-y-2 md:space-y-3">
              {/* Check if any item has a discount */}
              {order.items.some(item => item.discount > 0) && (
                <>
                  {/* Original Price Total */}
                  <div className="flex justify-between text-sm">
                    <span className="text-black/70">Original Price</span>
                    <span>₹{formatPrice(order.items.reduce((total, item) => {
                      return total + (item.originalPrice || item.price) * item.quantity;
                    }, 0))}</span>
                  </div>

                  {/* Discount */}
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-₹{formatPrice(order.items.reduce((total, item) => {
                      if (!item.discount || item.discount <= 0) return total;
                      const originalPrice = item.originalPrice || item.price;
                      return total + ((originalPrice - item.price) * item.quantity);
                    }, 0))}</span>
                  </div>
                </>
              )}

              {/* Subtotal after discount */}
              <div className="flex justify-between text-sm">
                <span className="text-black/70">Subtotal</span>
                <span>₹{formatPrice(order.total)}</span>
              </div>

              {/* Shipping */}
              <div className="flex justify-between text-sm">
                <span className="text-black/70">Shipping</span>
                <span>Free</span>
              </div>

              {/* Coupon Discount */}
              {order.couponCode && order.couponDiscount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Coupon Discount ({order.couponCode})</span>
                  <span>-₹{formatPrice(order.couponDiscount)}</span>
                </div>
              )}

              {/* Gift Card */}
              {order.giftCardCode && order.giftCardAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Gift Card Applied</span>
                  <span>-₹{formatPrice(order.giftCardAmount)}</span>
                </div>
              )}

              {/* Total */}
              <div className="flex justify-between text-sm font-medium pt-2 border-t border-black/5">
                <span>Total</span>
                <span>₹{formatPrice(order.total)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Shipping Information */}
        <div className="mb-8 md:mb-12">
          <h2 className="text-sm uppercase tracking-wider mb-4 md:mb-6">Shipping Information</h2>
          <div className="border-t border-b border-black/5 py-4 md:py-6">
            <p className="text-sm whitespace-pre-line">{order.shippingAddress}</p>
          </div>
        </div>

        {/* Payment Information */}
        <div className="mb-8 md:mb-12">
          <h2 className="text-sm uppercase tracking-wider mb-4 md:mb-6">Payment Information</h2>
          <div className="border-t border-b border-black/5 py-4 md:py-6">
            <div className="space-y-3">
              {/* Payment Method */}
              <div className="flex justify-between text-sm">
                <span className="text-black/70">Payment Method</span>
                <span className="capitalize">
                  {order.paymentMethod === 'cod' ? 'Cash on Delivery' :
                   order.paymentMethod === 'razorpay' ? 'Online Payment (Razorpay)' :
                   order.paymentMethod}
                </span>
              </div>

              {/* Payment Status */}
              <div className="flex justify-between text-sm">
                <span className="text-black/70">Payment Status</span>
                <span className={`${order.paymentStatus === 'paid' ? 'text-green-600' :
                                   order.paymentStatus === 'failed' ? 'text-red-600' :
                                   'text-yellow-600'}`}>
                  {order.paymentStatus === 'paid' ? 'Paid' :
                   order.paymentStatus === 'failed' ? 'Failed' :
                   'Pending'}
                </span>
              </div>

              {/* Payment ID (if available) */}
              {order.paymentId && (
                <div className="flex justify-between text-sm">
                  <span className="text-black/70">Payment ID</span>
                  <span className="font-mono text-xs">{order.paymentId}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Need Help Section */}
        <div className="mb-12 md:mb-16">
          <h2 className="text-sm uppercase tracking-wider mb-4 md:mb-6">Need Help?</h2>
          <div className="border-t border-b border-black/5 py-4 md:py-6">
            <p className="text-sm mb-4">If you have any questions about your order, please contact our customer service.</p>
            <Link
              href="/contact"
              className="inline-block text-[10px] md:text-xs tracking-[0.2em] border border-black px-4 py-2 hover:bg-black hover:text-white transition-colors"
            >
              CONTACT US
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
