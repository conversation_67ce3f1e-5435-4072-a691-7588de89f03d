'use client';

import { useState } from 'react';
import Link from 'next/link';
import { FiMenu, FiSearch, FiShoppingBag, FiX, FiUser, FiHeart } from 'react-icons/fi';
import { useAuth } from '@/context/AuthContext';
import { useCart } from '@/context/CartContext';
import Logo from './Logo';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user } = useAuth();
  const { cart, wishlist } = useCart();

  const cartItemCount = cart.reduce((total, item) => total + (Number(item.quantity) || 0), 0);
  const wishlistCount = wishlist.length;

  const navigationLinks = [
    { href: '/products', label: 'SHOP' },
    { href: '/categories', label: 'CATEGORIES' },
    { href: '/about', label: 'ABOUT' },
    { href: '/contact', label: 'CONTACT' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white">
      <div className="max-w-screen-xl mx-auto border-b border-black/5">
        <div className="h-16 px-4 md:px-8 flex items-center justify-between">
          {/* Left Side - Logo */}
          <Link href="/" className="hover:opacity-70 transition-opacity">
            <Logo />
          </Link>

          {/* Center - Navigation Links */}
          <div className="hidden lg:flex items-center justify-center space-x-8">
            {navigationLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-[10px] tracking-[0.2em] hover:opacity-50 transition-opacity"
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Right Side - Icons */}
          <div className="flex items-center space-x-4 md:space-x-5">
            <Link href="/products" className="hover:opacity-50 transition-opacity">
              <FiSearch size={16} />
            </Link>
            <Link href="/wishlist" className="hover:opacity-50 transition-opacity relative">
              <FiHeart size={16} />
              {wishlistCount > 0 && (
                <span className="absolute -top-1.5 -right-1.5 bg-black text-white text-[9px] w-3.5 h-3.5 flex items-center justify-center rounded-full">
                  {wishlistCount > 99 ? '99+' : wishlistCount}
                </span>
              )}
            </Link>
            <Link href="/cart" className="hover:opacity-50 transition-opacity relative">
              <FiShoppingBag size={16} />
              {cartItemCount > 0 && (
                <span className="absolute -top-1.5 -right-1.5 bg-black text-white text-[9px] w-3.5 h-3.5 flex items-center justify-center rounded-full">
                  {cartItemCount > 99 ? '99+' : cartItemCount}
                </span>
              )}
            </Link>
            <Link
              href={user ? '/profile' : '/login'}
              className="hover:opacity-50 transition-opacity"
            >
              <FiUser size={16} />
            </Link>

            <button
              onClick={() => setIsMenuOpen(true)}
              className="lg:hidden hover:opacity-50 transition-opacity"
              aria-label="Menu"
            >
              <FiMenu size={18} />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 bg-white lg:hidden">
          <div className="h-16 px-4 md:px-8 flex items-center justify-between border-b border-black/5">
            <Link href="/" className="hover:opacity-70 transition-opacity">
              <Logo />
            </Link>
            <button
              onClick={() => setIsMenuOpen(false)}
              className="hover:opacity-50 transition-opacity"
              aria-label="Close menu"
            >
              <FiX size={18} />
            </button>
          </div>

          <div className="px-4 md:px-8 py-8">
            <div className="space-y-6">
              {/* Main Navigation Links */}
              <div className="space-y-4">
                {navigationLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
              </div>

              {/* Utility Links */}
              <div className="pt-6 space-y-4 border-t border-black/5">
                <Link
                  href="/wishlist"
                  className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                  onClick={() => setIsMenuOpen(false)}
                >
                  WISHLIST {wishlistCount > 0 && `(${wishlistCount})`}
                </Link>

                <Link
                  href="/cart"
                  className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                  onClick={() => setIsMenuOpen(false)}
                >
                  CART {cartItemCount > 0 && `(${cartItemCount})`}
                </Link>

                <Link
                  href="/products"
                  className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                  onClick={() => setIsMenuOpen(false)}
                >
                  SEARCH
                </Link>

                <Link
                  href={user ? '/profile' : '/login'}
                  className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {user ? 'PROFILE' : 'LOGIN'}
                </Link>

                {user && (
                  <Link
                    href="/orders"
                    className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    MY ORDERS
                  </Link>
                )}
              </div>

              {/* Admin Link */}
              {user?.role === 'admin' && (
                <div className="pt-6 border-t border-black/5">
                  <Link
                    href="/admin"
                    className="block text-sm tracking-[0.2em] text-red-600 hover:opacity-50 transition-opacity"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    ADMIN DASHBOARD
                  </Link>
                </div>
              )}
            </div>

            {/* Footer Info */}
            <div className="absolute bottom-8 left-4 right-4 md:left-8 md:right-8">
              <p className="text-[9px] text-gray-400 tracking-wider">
                © {new Date().getFullYear()} FURNITUREBAZAAR
              </p>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
