'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { FiArrowRight } from 'react-icons/fi';

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    revenue: { value: '₹0', change: 0 },
    orders: { value: 0, change: 0 },
    users: { value: 0, change: 0 },
    products: { value: 0, change: 0 },
    newsletter: { value: 0, change: 0 },
    giftCards: { value: 0, change: 0 },
    coupons: { value: 0, change: 0 },
    stores: { value: 0, change: 0 }
  });
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }
    fetchStats();
  }, [user, router]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const adminSections = [
    {
      title: 'PRODUCTS',
      stats: `${stats.products.value} items`,
      link: '/admin/products',
      description: 'Manage products, inventory, and categories'
    },
    {
      title: 'ORDERS',
      stats: `${stats.orders.value} orders`,
      link: '/admin/orders',
      description: 'Process and manage customer orders'
    },
    {
      title: 'USERS',
      stats: `${stats.users.value} users`,
      link: '/admin/users',
      description: 'Manage user accounts and permissions'
    },
    {
      title: 'REVENUE',
      stats: stats.revenue.value,
      link: '/admin/revenue',
      description: 'View financial reports and analytics'
    },
    {
      title: 'STORES',
      stats: `${stats.stores?.value || 0} locations`,
      link: '/admin/stores',
      description: 'Manage store locations and details'
    },
    {
      title: 'GIFT CARDS',
      stats: `${stats.giftCards?.value || 0} cards`,
      link: '/admin/gift-cards',
      description: 'Manage gift cards and redemptions'
    },
    {
      title: 'COUPONS',
      stats: `${stats.coupons?.value || 0} coupons`,
      link: '/admin/coupons',
      description: 'Manage discount coupons and promotions'
    },
    {
      title: 'CONTACTS',
      stats: 'Messages',
      link: '/admin/contacts',
      description: 'Customer inquiries and support'
    },
    {
      title: 'NEWSLETTER',
      stats: `${stats.newsletter.value} subscribers`,
      link: '/admin/newsletter',
      description: 'Manage newsletter subscribers'
    }
  ];

  return (
    <div className="max-w-screen-xl mx-auto">
      <div className="mb-12 px-6">
        <h1 className="text-xs tracking-[0.2em] text-gray-400">ADMIN DASHBOARD</h1>
      </div>

      <div className="grid grid-cols-1 border-t border-black/10">
        {adminSections.map((section, index) => (
          <Link
            key={section.title}
            href={section.link}
            className="group border-b border-black/10 hover:bg-black/5 transition-colors"
          >
            <div className="px-6 py-8">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-sm tracking-[0.2em]">{section.title}</h2>
                <div className="flex items-center">
                  <span className="text-sm mr-4">{section.stats}</span>
                  <FiArrowRight
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    size={16}
                  />
                </div>
              </div>
              <p className="text-sm text-gray-500">{section.description}</p>
            </div>
          </Link>
        ))}
      </div>

      <div className="mt-12 px-6">
        <div className="text-xs text-gray-400 tracking-[0.2em]">
          LAST UPDATED: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}
