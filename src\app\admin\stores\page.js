'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import Image from 'next/image';
import { 
  FiPlus, 
  FiEdit2, 
  FiTrash2, 
  FiMapPin, 
  FiPhone, 
  FiClock, 
  FiArrowLeft,
  FiCheck,
  FiX,
  FiSearch
} from 'react-icons/fi';

export default function AdminStoresPage() {
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [storeToDelete, setStoreToDelete] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  });
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }
    fetchStores();
  }, [user, router]);

  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/stores');
      if (response.ok) {
        const data = await response.json();
        setStores(data);
        calculateStats(data);
      }
    } catch (error) {
      console.error('Error fetching stores:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (storeData) => {
    const total = storeData.length;
    const active = storeData.filter(store => store.status === 'active').length;
    const inactive = storeData.filter(store => store.status === 'inactive').length;
    
    setStats({ total, active, inactive });
  };

  const handleDeleteClick = (store) => {
    setStoreToDelete(store);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!storeToDelete) return;
    
    try {
      const response = await fetch(`/api/admin/stores/${storeToDelete._id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setStores(stores.filter(store => store._id !== storeToDelete._id));
        calculateStats(stores.filter(store => store._id !== storeToDelete._id));
        setShowDeleteModal(false);
        setStoreToDelete(null);
      } else {
        console.error('Failed to delete store');
      }
    } catch (error) {
      console.error('Error deleting store:', error);
    }
  };

  const filteredStores = stores.filter(store => 
    store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    store.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="max-w-screen-xl mx-auto px-4 md:px-6 pb-12">
      {/* Header with Back Button */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <Link href="/admin" className="inline-flex items-center text-sm hover:opacity-70 transition-opacity mb-4">
            <FiArrowLeft className="mr-2" /> BACK TO DASHBOARD
          </Link>
          <h1 className="text-xl font-light tracking-wide">Store Locations</h1>
        </div>
        <Link
          href="/admin/stores/add"
          className="inline-flex items-center bg-black text-white px-4 py-2 text-sm hover:bg-black/80 transition-colors"
        >
          <FiPlus className="mr-2" /> ADD STORE
        </Link>
      </div>

      {/* Store Statistics */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="p-4 border border-gray-200 bg-gray-50">
          <p className="text-sm">TOTAL STORES</p>
          <p className="text-2xl font-medium">{stats.total}</p>
        </div>
        <div className="p-4 border border-green-100 bg-green-50">
          <p className="text-sm text-green-700">ACTIVE STORES</p>
          <p className="text-2xl font-medium text-green-700">{stats.active}</p>
        </div>
        <div className="p-4 border border-red-100 bg-red-50">
          <p className="text-sm text-red-700">INACTIVE STORES</p>
          <p className="text-2xl font-medium text-red-700">{stats.inactive}</p>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6 relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FiSearch className="text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search stores by name, city or address..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-black"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Store Listings */}
      {loading ? (
        <div className="text-center py-12">
          <p className="text-sm tracking-[0.2em]">LOADING...</p>
        </div>
      ) : filteredStores.length === 0 ? (
        <div className="text-center py-12 border border-dashed border-gray-300">
          <p className="text-gray-500">No stores found. Add your first store location.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStores.map((store) => (
            <div key={store._id} className="border border-gray-200 group">
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                  src={store.image}
                  alt={store.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-500"
                />
                {store.status === 'inactive' && (
                  <div className="absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1">
                    INACTIVE
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h2 className="text-lg font-light">{store.name}</h2>
                  <div className="flex space-x-2">
                    <Link href={`/admin/stores/${store._id}/edit`}>
                      <FiEdit2 className="text-gray-500 hover:text-black transition-colors" />
                    </Link>
                    <button onClick={() => handleDeleteClick(store)}>
                      <FiTrash2 className="text-gray-500 hover:text-red-500 transition-colors" />
                    </button>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start">
                    <FiMapPin className="mt-1 mr-2 flex-shrink-0 text-gray-400" />
                    <p className="text-gray-600">{store.address}, {store.city}</p>
                  </div>
                  <div className="flex items-start">
                    <FiPhone className="mt-1 mr-2 flex-shrink-0 text-gray-400" />
                    <p className="text-gray-600">{store.phone}</p>
                  </div>
                  <div className="flex items-start">
                    <FiClock className="mt-1 mr-2 flex-shrink-0 text-gray-400" />
                    <p className="text-gray-600">{store.hours}</p>
                  </div>
                </div>
                <div className="mt-4 flex flex-wrap gap-1">
                  {store.features.map((feature, index) => (
                    <span key={index} className="text-xs px-2 py-1 bg-gray-100 text-gray-600">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Confirm Deletion</h3>
            <p className="mb-6">
              Are you sure you want to delete the store "{storeToDelete?.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 border border-gray-300 text-sm hover:bg-gray-100 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
