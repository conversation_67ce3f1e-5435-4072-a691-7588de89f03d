@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@layer utilities {
  .aspect-4\/5 {
    aspect-ratio: 4/5;
  }

  .aspect-1\/1 {
    aspect-ratio: 1/1;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }
}

:root {
  --foreground: #000000;
  --background: #ffffff;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Prevent scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Minimal button styles */
.btn-minimal {
  @apply text-sm tracking-[0.2em] hover:opacity-60 transition-opacity;
}

/* Clean input styles */
.input-minimal {
  @apply border-b border-black text-sm tracking-[0.1em] pb-2 focus:outline-none;
}

/* Image transitions */
.image-fade {
  @apply transition-opacity duration-700;
}

/* Hover effects */
.hover-fade {
  @apply hover:opacity-60 transition-opacity;
}

/* Custom scrollbar */
@media (min-width: 1024px) {
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
