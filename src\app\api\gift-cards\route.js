import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';
import { sendGiftCardEmail } from '@/lib/email';

// Create a new gift card
export async function POST(request) {
  try {
    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { 
      amount, 
      recipientEmail, 
      recipientName, 
      senderName, 
      message = '',
      paymentId = null
    } = body;

    // Validate required fields
    if (!amount || amount < 100) {
      return NextResponse.json(
        { error: 'Gift card amount must be at least ₹100' },
        { status: 400 }
      );
    }

    if (!recipientEmail) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    if (!recipientName) {
      return NextResponse.json(
        { error: 'Recipient name is required' },
        { status: 400 }
      );
    }

    if (!senderName) {
      return NextResponse.json(
        { error: 'Sender name is required' },
        { status: 400 }
      );
    }

    // Set expiration date (1 year from now)
    const expiresAt = new Date();
    expiresAt.setFullYear(expiresAt.getFullYear() + 1);

    // Create gift card
    const giftCard = new GiftCard({
      amount,
      balance: amount,
      recipientEmail,
      recipientName,
      senderName,
      senderEmail: user.email,
      message,
      purchasedBy: user._id,
      expiresAt,
      paymentId,
      paymentStatus: paymentId ? 'paid' : 'pending'
    });

    // Save gift card
    const savedGiftCard = await giftCard.save();

    // Send gift card email if payment is complete
    if (savedGiftCard.paymentStatus === 'paid') {
      await sendGiftCardEmail(savedGiftCard);
    }

    return NextResponse.json(savedGiftCard);
  } catch (error) {
    console.error('Error creating gift card:', error);
    return NextResponse.json(
      { error: 'Failed to create gift card' },
      { status: 500 }
    );
  }
}

// Get all gift cards for the current user
export async function GET(request) {
  try {
    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'purchased'; // 'purchased' or 'redeemed'

    let query = {};
    
    if (type === 'purchased') {
      query = { purchasedBy: user._id };
    } else if (type === 'redeemed') {
      query = { redeemedBy: user._id };
    } else {
      // If type is not valid, return both purchased and redeemed
      query = { 
        $or: [
          { purchasedBy: user._id },
          { redeemedBy: user._id }
        ]
      };
    }

    // Get gift cards
    const giftCards = await GiftCard.find(query).sort({ createdAt: -1 });

    return NextResponse.json(giftCards);
  } catch (error) {
    console.error('Error fetching gift cards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gift cards' },
      { status: 500 }
    );
  }
}
