'use client';

import { useState, useEffect, useRef } from 'react';
import { use } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useCart } from '@/context/CartContext';
import { FiShare2, FiChevronLeft, FiChevronRight, FiPlus, FiMinus, FiHeart, FiShoppingBag } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

export default function ProductDetailPage({ params }) {
  const resolvedParams = use(params);
  const scrollRef = useRef(null);
  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const [showInfo, setShowInfo] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const { addToCart, addToWishlist, removeFromWishlist, isInWishlist } = useCart();
  const productInWishlist = product ? isInWishlist(product._id) : false;

  useEffect(() => {
    const fetchProductAndRelated = async () => {
      try {
        // Fetch main product
        const response = await fetch(`/api/products/${resolvedParams.id}`);
        if (!response.ok) throw new Error('Product not found');
        const productData = await response.json();
        setProduct(productData);

        // Fetch related products
        const relatedResponse = await fetch('/api/products');
        if (!relatedResponse.ok) throw new Error('Failed to fetch related products');
        const allProducts = await relatedResponse.json();

        // Filter related products by category and exclude current product
        const related = allProducts
          .filter(p =>
            p.category === productData.category &&
            p._id !== productData._id
          )
          .slice(0, 4); // Get only 4 related products

        setRelatedProducts(related);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProductAndRelated();
  }, [resolvedParams.id]);

  const handleAddToCart = async () => {
    // Prevent adding out-of-stock items
    if (product.stock === 0) {
      toast.error('This product is out of stock');
      return;
    }

    try {
      const success = await addToCart(product, quantity);
      if (success) {
        toast.success(`Added ${quantity} item${quantity > 1 ? 's' : ''} to cart`);
      } else {
        toast.error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error(error.message || 'Failed to add to cart');
    }
  };

  const handleToggleWishlist = async () => {
    try {
      if (productInWishlist) {
        const success = await removeFromWishlist(product._id);
        if (success) {
          toast.success('Removed from wishlist');
        } else {
          toast.error('Failed to remove from wishlist');
        }
      } else {
        const success = await addToWishlist(product);
        if (success) {
          toast.success('Added to wishlist');
        } else {
          toast.error('Failed to add to wishlist');
        }
      }
    } catch (error) {
      console.error('Error updating wishlist:', error);
      toast.error('Failed to update wishlist');
    }
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const nextImage = () => {
    if (product && product.images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === product.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (product && product.images.length > 0) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? product.images.length - 1 : prev - 1
      );
    }
  };

  const selectImage = (index) => {
    setCurrentImageIndex(index);
  };

  const incrementQuantity = () => {
    if (product && quantity < product.stock) {
      setQuantity(prev => prev + 1);
    }
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white animate-pulse pt-6 md:pt-8 lg:pt-10">
        {/* Back to Products Link Skeleton */}
        <div className="px-4 py-4 border-b border-black/5 max-w-screen-xl mx-auto">
          <div className="h-4 bg-gray-200 w-32 rounded" />
        </div>

        {/* Main Product Layout Skeleton */}
        <div className="max-w-screen-xl mx-auto mt-6 md:mt-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
            {/* Left Side - Product Images Skeleton */}
            <div className="relative p-4">
              {/* Main Image Skeleton */}
              <div className="relative aspect-square w-full bg-gray-200 rounded" />

              {/* Thumbnails Skeleton */}
              <div className="flex gap-2 mt-2 px-4 overflow-x-auto">
                {[1, 2, 3, 4].map((_, index) => (
                  <div key={index} className="w-16 h-16 bg-gray-200 rounded flex-shrink-0" />
                ))}
              </div>
            </div>

            {/* Right Side - Product Info Skeleton */}
            <div className="p-6 md:p-8 lg:p-10 pt-8 md:pt-10 lg:pt-12">
              {/* Product Category and Name Skeleton */}
              <div className="mb-6">
                <div className="h-4 bg-gray-200 w-24 mb-2 rounded" />
                <div className="h-8 bg-gray-200 w-3/4 rounded" />
              </div>

              {/* Price Skeleton */}
              <div className="mb-6">
                <div className="h-6 bg-gray-200 w-32 rounded" />
              </div>

              {/* Description Skeleton */}
              <div className="mb-8 space-y-2">
                <div className="h-4 bg-gray-200 w-full rounded" />
                <div className="h-4 bg-gray-200 w-full rounded" />
                <div className="h-4 bg-gray-200 w-3/4 rounded" />
                <div className="h-4 bg-gray-200 w-1/4 mt-2 rounded" />
              </div>

              {/* Quantity Selector Skeleton */}
              <div className="mb-8">
                <div className="h-4 bg-gray-200 w-20 mb-3 rounded" />
                <div className="h-10 bg-gray-200 w-32 rounded" />
              </div>

              {/* Add to Cart Button Skeleton */}
              <div className="mb-6">
                <div className="h-12 bg-gray-200 w-full rounded" />
              </div>

              {/* Action Buttons Skeleton */}
              <div className="flex gap-4">
                <div className="h-8 bg-gray-200 w-32 rounded" />
                <div className="h-8 bg-gray-200 w-32 rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl mb-4">Product not found</h2>
          <Link
            href="/products"
            className="text-sm underline"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  if (!product) return null;

  const discountedPrice = product.discount > 0
    ? ((product.price * (100 - product.discount)) / 100).toLocaleString('en-IN')
    : null;

  return (
    <div className="min-h-screen bg-white pt-6 md:pt-8 lg:pt-10">
      {/* Back to Products Link */}
      <div className="px-4 py-4 border-b border-black/5 max-w-screen-xl mx-auto">
        <Link href="/products" className="text-xs uppercase tracking-wider hover:opacity-70 inline-flex items-center gap-1">
          <FiChevronLeft size={14} />
          <span>Back to Products</span>
        </Link>
      </div>

      {/* Main Product Layout - Zara Style with Square Images */}
      <div className="max-w-screen-xl mx-auto mt-6 md:mt-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
          {/* Left Side - Product Images */}
          <div className="relative p-4">
            {/* Main Image - Square */}
            <div className="relative aspect-square w-full">
              <Image
                src={product.images[currentImageIndex]}
                alt={product.name}
                fill
                className="object-cover"
                priority={true}
                sizes="(max-width: 768px) 100vw, 50vw"
              />

              {/* Image Navigation Arrows */}
              {product.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full hover:bg-white"
                    aria-label="Previous image"
                  >
                    <FiChevronLeft size={20} />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full hover:bg-white"
                    aria-label="Next image"
                  >
                    <FiChevronRight size={20} />
                  </button>
                </>
              )}

              {/* Discount Tag - Top Left Corner */}
              {product.discount > 0 && (
                <div className="absolute top-2 left-2 bg-black text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                  {product.discount}% Off
                </div>
              )}
            </div>

            {/* Square Thumbnail Navigation */}
            {product.images.length > 1 && (
              <div className="flex gap-2 mt-2 px-4 overflow-x-auto scrollbar-hide" ref={scrollRef}>
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => selectImage(index)}
                    className={`relative w-16 h-16 flex-shrink-0 border ${currentImageIndex === index ? 'border-black' : 'border-gray-200'}`}
                  >
                    <div className="aspect-square w-full h-full">
                      <Image
                        src={image}
                        alt={`Thumbnail ${index + 1}`}
                        fill
                        className="object-cover"
                        sizes="64px"
                      />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Right Side - Product Info */}
          <div className="p-6 md:p-8 lg:p-10 pt-8 md:pt-10 lg:pt-12">
            {/* Product Category and Name */}
            <div className="mb-6">
              <p className="text-xs text-gray-500 uppercase tracking-wider mb-1">{product.category}</p>
              <h1 className="text-2xl font-light">{product.name}</h1>
            </div>

            {/* Price */}
            <div className="mb-6">
              {discountedPrice ? (
                <div className="flex items-baseline gap-3">
                  <p className="text-xl font-light">₹{discountedPrice}</p>
                  <p className="text-sm text-gray-500 line-through">₹{product.price.toLocaleString('en-IN')}</p>
                </div>
              ) : (
                <p className="text-xl font-light">₹{product.price.toLocaleString('en-IN')}</p>
              )}
            </div>

            {/* Product Description */}
            <div className="mb-8">
              {showInfo ? (
                /* Full Description */
                <div className="text-sm text-gray-600 animate-fadeIn">
                  <p>{product.description}</p>
                </div>
              ) : (
                /* Short Version */
                <p className="text-sm text-gray-600 line-clamp-3">{product.description}</p>
              )}

              <button
                onClick={() => setShowInfo(!showInfo)}
                className="text-xs uppercase tracking-wider mt-2 hover:opacity-70"
              >
                {showInfo ? 'Show Less' : 'Read More'}
              </button>

              {/* Additional product details if available */}
              <div className="mt-4 space-y-2">
                {product.material && (
                  <p><span className="text-black">Material:</span> {product.material}</p>
                )}
                {product.dimensions && (
                  <p><span className="text-black">Dimensions:</span> {product.dimensions}</p>
                )}
                {product.stock > 5 ? (
                  <p><span className="text-black">Availability:</span> In Stock</p>
                ) : product.stock > 0 ? (
                  <p><span className="text-black">Availability:</span> <span className="text-orange-600">Low Stock</span> (Only {product.stock} left)</p>
                ) : (
                  <p className="inline-block text-red-500 font-medium bg-red-50 px-2 py-1 rounded">Out of Stock</p>
                )}
              </div>
            </div>

            {/* Size Selection (if applicable) */}
            {product.sizes && (
              <div className="mb-8">
                <p className="text-xs uppercase tracking-wider mb-3">Select Size</p>
                <div className="flex flex-wrap gap-2">
                  {product.sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`min-w-[60px] py-2 px-3 text-sm ${selectedSize === size
                        ? 'bg-black text-white'
                        : 'border border-black/20 hover:border-black'}`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Quantity Selector */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-3">
                <p className="text-xs uppercase tracking-wider">Quantity</p>
                {product.stock > 0 && product.stock <= 5 && (
                  <p className="text-xs text-orange-600">Only {product.stock} left</p>
                )}
              </div>
              <div className="flex items-center border border-black/20 w-fit">
                <button
                  onClick={decrementQuantity}
                  disabled={quantity <= 1}
                  className="px-3 py-2 hover:bg-black/5 disabled:opacity-50"
                >
                  <FiMinus size={16} />
                </button>
                <span className="px-4 py-2 text-sm">{quantity}</span>
                <button
                  onClick={incrementQuantity}
                  disabled={product.stock <= quantity}
                  className="px-3 py-2 hover:bg-black/5 disabled:opacity-50"
                >
                  <FiPlus size={16} />
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <button
                onClick={handleAddToCart}
                disabled={product.stock === 0}
                className="w-full py-3 bg-black text-white text-sm uppercase tracking-wider hover:bg-black/90 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                <FiShoppingBag size={16} />
                <span>{product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}</span>
              </button>

              <button
                onClick={handleToggleWishlist}
                className="w-full py-3 text-sm uppercase tracking-wider border border-black/20 hover:border-black transition-colors flex items-center justify-center gap-2"
              >
                <FiHeart size={16} className={productInWishlist ? 'fill-black' : ''} />
                <span>{productInWishlist ? 'Saved' : 'Save'}</span>
              </button>

              <button
                onClick={handleShare}
                className="w-full py-3 text-sm uppercase tracking-wider hover:bg-black/5 transition-colors flex items-center justify-center gap-2"
              >
                <FiShare2 size={16} />
                <span>Share</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Related Products - Zara Style */}
      {relatedProducts.length > 0 && (
        <div className="py-12 px-6 md:px-8 border-t border-black/10 mt-12">
          <div className="max-w-screen-xl mx-auto">
            <h2 className="text-xl font-light mb-8">You May Also Like</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <Link
                  key={relatedProduct._id}
                  href={`/products/${relatedProduct._id}`}
                  className="group"
                >
                  <div className="relative aspect-square mb-3 overflow-hidden border border-black/5">
                    <Image
                      src={relatedProduct.images[0]}
                      alt={relatedProduct.name}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    {relatedProduct.discount > 0 && (
                      <div className="absolute top-2 left-2 bg-black text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                        {relatedProduct.discount}% Off
                      </div>
                    )}
                    {relatedProduct.stock === 0 ? (
                      <div className="absolute top-2 right-2 bg-red-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                        Out of Stock
                      </div>
                    ) : relatedProduct.stock <= 5 && (
                      <div className="absolute top-2 right-2 bg-amber-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                        Low Stock
                      </div>
                    )}
                  </div>
                  <h3 className="text-sm font-light group-hover:opacity-70 transition-opacity">
                    {relatedProduct.name}
                  </h3>
                  <div className="flex justify-between items-center mt-1">
                    <div className="flex items-baseline gap-2">
                    {relatedProduct.discount > 0 ? (
                      <>
                        <p className="text-sm font-light">
                          ₹{((relatedProduct.price * (100 - relatedProduct.discount)) / 100).toLocaleString('en-IN')}
                        </p>
                        <p className="text-xs text-gray-500 line-through">
                          ₹{relatedProduct.price.toLocaleString('en-IN')}
                        </p>
                      </>
                    ) : (
                      <p className="text-sm font-light">
                        ₹{relatedProduct.price.toLocaleString('en-IN')}
                      </p>
                    )}
                    </div>
                    {relatedProduct.stock === 0 ? (
                      <span className="text-[10px] font-medium text-red-500 bg-red-50 px-1.5 py-0.5 rounded">
                        Out of Stock
                      </span>
                    ) : relatedProduct.stock <= 5 && (
                      <span className="text-[10px] font-medium text-amber-500 bg-amber-50 px-1.5 py-0.5 rounded">
                        Low Stock: {relatedProduct.stock}
                      </span>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
