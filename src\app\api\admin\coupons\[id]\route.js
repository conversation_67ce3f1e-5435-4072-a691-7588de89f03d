import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Coupon from '@/models/Coupon';
import { getSessionUser } from '@/lib/auth';
import mongoose from 'mongoose';

// Get a specific coupon by ID (admin only)
export async function GET(request, context) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    const params = await context.params;
    const { id } = params;

    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid coupon ID' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Find coupon
    const coupon = await Coupon.findById(id);

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(coupon);
  } catch (error) {
    console.error('Error getting coupon:', error);
    return NextResponse.json(
      { error: 'Failed to get coupon' },
      { status: 500 }
    );
  }
}

// Update a coupon (admin only)
export async function PUT(request, context) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    const params = await context.params;
    const { id } = params;

    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid coupon ID' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { 
      code,
      type,
      value,
      minPurchase,
      maxDiscount,
      startDate,
      expiryDate,
      usageLimit,
      description,
      isActive
    } = body;

    // Find coupon
    const coupon = await Coupon.findById(id);

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    // Check if code is being changed and if it already exists
    if (code && code.toUpperCase() !== coupon.code) {
      const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
      if (existingCoupon) {
        return NextResponse.json(
          { error: 'Coupon code already exists' },
          { status: 400 }
        );
      }
      coupon.code = code.toUpperCase();
    }

    // Update coupon fields
    if (type !== undefined && ['percentage', 'fixed'].includes(type)) {
      coupon.type = type;
    }

    if (value !== undefined && value > 0) {
      if (type === 'percentage' && value > 100) {
        return NextResponse.json(
          { error: 'Percentage discount cannot exceed 100%' },
          { status: 400 }
        );
      }
      coupon.value = value;
    }

    if (minPurchase !== undefined && minPurchase >= 0) {
      coupon.minPurchase = minPurchase;
    }

    if (maxDiscount !== undefined) {
      coupon.maxDiscount = maxDiscount;
    }

    if (startDate !== undefined) {
      coupon.startDate = startDate;
    }

    if (expiryDate !== undefined) {
      coupon.expiryDate = expiryDate;
    }

    if (usageLimit !== undefined) {
      coupon.usageLimit = usageLimit;
    }

    if (description !== undefined) {
      coupon.description = description;
    }

    if (isActive !== undefined) {
      coupon.isActive = isActive;
    }

    coupon.updatedAt = new Date();

    // Save coupon
    await coupon.save();

    return NextResponse.json({
      message: 'Coupon updated successfully',
      coupon
    });
  } catch (error) {
    console.error('Error updating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to update coupon' },
      { status: 500 }
    );
  }
}

// Delete a coupon (admin only)
export async function DELETE(request, context) {
  try {
    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    const params = await context.params;
    const { id } = params;

    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid coupon ID' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Find and delete coupon
    const coupon = await Coupon.findByIdAndDelete(id);

    if (!coupon) {
      return NextResponse.json(
        { error: 'Coupon not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Coupon deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting coupon:', error);
    return NextResponse.json(
      { error: 'Failed to delete coupon' },
      { status: 500 }
    );
  }
}
