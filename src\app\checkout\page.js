'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/context/CartContext';
import { useAuth } from '@/context/AuthContext';
import { FiChevronRight, FiTag } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import Image from 'next/image';
import Link from 'next/link';
import Script from 'next/script';
import GiftCardRedemption from '@/components/GiftCardRedemption';

export default function CheckoutPage() {
  const router = useRouter();
  const {
    cart,
    clearCart,
    validateCartStock,
    coupon,
    calculateCouponDiscount
  } = useCart();
  const { user } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [shippingAddress, setShippingAddress] = useState(user?.address || '');
  const [paymentMethod, setPaymentMethod] = useState('cod');
  const [razorpayOrder, setRazorpayOrder] = useState(null);
  // Use a ref to store the order data for immediate access
  const razorpayOrderRef = useRef(null);
  const [stockValidated, setStockValidated] = useState(false);
  const [giftCard, setGiftCard] = useState(null);

  useEffect(() => {
    setMounted(true);

    // Validate cart stock when component mounts
    const validateStock = async () => {
      if (cart && cart.length > 0) {
        const isValid = await validateCartStock();
        setStockValidated(isValid);
      }
    };

    validateStock();
  }, [cart, validateCartStock]);

  // Add a direct script import when the component mounts and track loading state
  const [scriptLoaded, setScriptLoaded] = useState(false);

  useEffect(() => {
    if (mounted && typeof window !== 'undefined') {
      // Check if Razorpay is already available
      if (window.Razorpay) {
        console.log('Razorpay already available in window');
        setScriptLoaded(true);
        return;
      }

      console.log('Adding Razorpay script directly');
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.async = false; // Load synchronously for more reliable loading
      script.onload = () => {
        console.log('Razorpay script loaded via direct injection');
        setScriptLoaded(true);
      };
      script.onerror = (error) => {
        console.error('Failed to load Razorpay script:', error);
      };
      document.body.appendChild(script);
    }
  }, [mounted]);

  useEffect(() => {
    // Redirect if cart is empty or user is not logged in
    if (mounted) {
      if (!user) {
        toast.error('Please login to continue');
        router.push('/login');
        return;
      }
      if (!cart || cart.length === 0) {
        toast.error('Your cart is empty');
        router.push('/cart');
        return;
      }
    }
  }, [mounted, cart, user, router]);

  const getDiscountedPrice = (item) => {
    if (!item.discount || item.discount <= 0) return Number(item.price) || 0;
    return (Number(item.price) * (100 - Number(item.discount))) / 100;
  };

  const calculateSubtotal = () => {
    return cart.reduce((total, item) => {
      const price = getDiscountedPrice(item);
      const quantity = Number(item.quantity) || 0;
      return total + (price * quantity);
    }, 0);
  };

  // Effect to automatically select gift card payment method when gift card covers the entire amount
  useEffect(() => {
    if (giftCard && giftCard.amount >= calculateSubtotal()) {
      setPaymentMethod('giftcard');
    }
  }, [giftCard]);

  const calculateShipping = () => {
    return 0; // Free shipping
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const shipping = calculateShipping();
    const giftCardAmount = giftCard ? giftCard.amount : 0;
    const couponDiscount = coupon ? calculateCouponDiscount(subtotal) : 0;

    // Cap the gift card amount to the subtotal to avoid negative totals
    const appliedGiftCardAmount = Math.min(giftCardAmount, subtotal);

    // Ensure total doesn't go below zero
    return Math.max(0, subtotal + shipping - appliedGiftCardAmount - couponDiscount);
  };

  const formatPrice = (price) => {
    const numPrice = Number(price);
    return !isNaN(numPrice) ? numPrice.toLocaleString('en-IN') : '0';
  };

  // Initialize Razorpay when needed
  const initializeRazorpayOrder = async () => {
    if (paymentMethod !== 'razorpay') return;

    try {
      console.log('Initializing Razorpay order...');

      // Check if gift card covers the entire amount
      const subtotal = calculateSubtotal();
      const giftCardAmount = giftCard ? giftCard.amount : 0;

      if (giftCardAmount >= subtotal) {
        console.log('Gift card covers entire amount, switching to gift card payment method');
        setPaymentMethod('giftcard');
        toast.info('Your gift card covers the entire order. Switching to gift card payment.');
        return null;
      }

      // Verify total amount is valid (allow zero for gift card payments)
      const totalAmount = calculateTotal();
      // Use the giftCardAmount that was already defined above
      const isFullyCovered = giftCardAmount >= calculateSubtotal();

      if (typeof totalAmount !== 'number' || (totalAmount <= 0 && !isFullyCovered)) {
        console.error('Invalid order amount:', totalAmount);
        toast.error('Invalid order amount. Please try again.');
        return null;
      }

      // Ensure we have a valid user
      if (!user || !user._id) {
        console.error('User information not available');
        toast.error('User information not available. Please log in again.');
        return null;
      }

      // Prepare order data with all required fields
      const orderData = {
        amount: totalAmount,
        currency: 'INR',
        receipt: `order_rcpt_${Date.now()}`,
        notes: {
          name: user.name || 'Guest',
          email: user.email || '<EMAIL>',
          userId: user._id || 'guest',
          cartItems: cart.length,
          timestamp: new Date().toISOString()
        }
      };

      console.log('Order data prepared:', {
        amount: orderData.amount,
        currency: orderData.currency,
        receipt: orderData.receipt
      });

      // Make the API request with proper error handling and retry logic
      let response;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          console.log(`API request attempt ${retryCount + 1}/${maxRetries + 1}`);

          response = await fetch('/api/payment/razorpay/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderData),
          });

          console.log('Razorpay API response status:', response.status);

          // If successful, break out of retry loop
          if (response.ok) break;

          // If we get a 500 error, it might be temporary, so retry
          if (response.status >= 500) {
            retryCount++;
            if (retryCount <= maxRetries) {
              console.log(`Retrying API request after server error (${response.status})`);
              await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
              continue;
            }
          }

          // For other errors, break and handle them below
          break;

        } catch (fetchError) {
          console.error('Network error during Razorpay API call:', fetchError);

          // Retry network errors
          retryCount++;
          if (retryCount <= maxRetries) {
            console.log(`Retrying after network error, attempt ${retryCount}/${maxRetries}`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
            continue;
          }

          toast.error('Network error. Please check your connection and try again.');
          return null;
        }
      }

      // Handle API response errors
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Razorpay API error response:', errorText);

        let errorData = {};
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          console.error('Failed to parse error response as JSON');
        }

        const errorMessage = errorData.error || `Server error: ${response.status}`;
        console.error('Formatted error message:', errorMessage);
        toast.error(`Payment initialization failed: ${errorMessage}`);
        return null;
      }

      // Parse and validate the response data
      let data;
      try {
        data = await response.json();
        console.log('Razorpay order created successfully:', {
          id: data.id,
          amount: data.amount,
          currency: data.currency,
          key_exists: !!data.key
        });

        // Validate required fields
        if (!data.id || !data.key || !data.amount) {
          console.error('Invalid Razorpay order data:', {
            id_exists: !!data.id,
            key_exists: !!data.key,
            amount_exists: !!data.amount
          });
          toast.error('Invalid payment data received. Please try again.');
          return null;
        }
      } catch (jsonError) {
        console.error('Error parsing Razorpay response:', jsonError);
        toast.error('Error processing payment data. Please try again.');
        return null;
      }

      // Store the order data in both state and ref
      console.log('Setting Razorpay order data in state and ref');
      setRazorpayOrder(data);

      // Store in ref for immediate access
      razorpayOrderRef.current = data;

      console.log('Razorpay order data set, returning data');
      return data;
    } catch (error) {
      console.error('Razorpay initialization error:', error);
      toast.error(`Failed to initialize payment: ${error.message}`);
      return null;
    }
  };

  // Handle Razorpay payment
  const handleRazorpayPayment = async (directOrderData = null) => {
    // Use ref first, then state, then direct data
    const orderData = razorpayOrderRef.current || razorpayOrder || directOrderData;

    // Validate order data
    if (!orderData) {
      console.error('No Razorpay order available in ref, state, or direct data');
      toast.error('Payment order not created. Please try again.');
      return false;
    }

    // Log which source we're using
    const dataSource = razorpayOrderRef.current ? 'ref' : (razorpayOrder ? 'state' : 'direct');
    console.log(`Using order data from ${dataSource}`);

    console.log('Razorpay order validation:', {
      id_exists: !!orderData.id,
      amount_exists: !!orderData.amount,
      key_exists: !!orderData.key
    });

    // Double-check if Razorpay is loaded
    if (typeof window === 'undefined') {
      console.error('Window is undefined');
      toast.error('Browser environment not available');
      return false;
    }

    // Final check for Razorpay availability
    if (!window.Razorpay) {
      console.error('Razorpay not available in window object');
      toast.error('Payment system not ready. Please refresh the page and try again.');
      return false;
    }

    // Check if Razorpay is a constructor
    if (typeof window.Razorpay !== 'function') {
      console.error('Razorpay is not a constructor', typeof window.Razorpay);
      toast.error('Payment system not properly initialized. Please refresh the page.');
      return false;
    }

    console.log('Razorpay is properly loaded and ready to use');

    const options = {
      key: orderData.key,
      amount: orderData.amount,
      currency: orderData.currency,
      name: 'FURNITUREBAZAAR',
      description: 'Purchase from FURNITUREBAZAAR',
      order_id: orderData.id,
      handler: async function (response) {
        try {
          // Verify payment signature
          const verifyResponse = await fetch('/api/payment/razorpay/', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature
            }),
          });

          if (!verifyResponse.ok) {
            const errorData = await verifyResponse.json().catch(() => ({}));
            throw new Error(errorData.error || `Verification failed: ${verifyResponse.status}`);
          }

          const verifyData = await verifyResponse.json();

          // Create order with payment details
          await createOrder('razorpay', verifyData.paymentId);
        } catch (error) {
          console.error('Payment verification error:', error);
          toast.error('Payment verification failed. Please contact support.');
        }
      },
      prefill: {
        name: user.name,
        email: user.email,
        contact: user.phone || ''
      },
      theme: {
        color: '#000000'
      }
    };

    try {
      // Log options (redacted for security)
      console.log('Creating Razorpay instance with options:', {
        ...options,
        key: '***',
        amount: options.amount,
        currency: options.currency,
        order_id: options.order_id
      });

      // Create Razorpay instance
      const razorpayInstance = new window.Razorpay(options);
      console.log('Razorpay instance created successfully');

      // Set up event handlers
      razorpayInstance.on('payment.failed', function (response) {
        console.error('Razorpay payment failed:', response.error);
        toast.error(`Payment failed: ${response.error.description || 'Unknown error'}`);
      });

      // Open Razorpay checkout
      razorpayInstance.open();
      console.log('Razorpay checkout opened');

      // Return success
      return true;
    } catch (error) {
      // Log detailed error information
      console.error('Error creating Razorpay instance:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });

      // Show user-friendly error message
      toast.error(`Payment initialization failed: ${error.message}. Please try again.`);
      return false;
    }
  };

  // Create order function
  const createOrder = async (method, paymentId = null) => {
    try {
      setIsProcessing(true);

      // Validate stock one more time before creating order
      const isStockValid = await validateCartStock();
      if (!isStockValid) {
        toast.error('Some items in your cart are out of stock. Please review your cart before proceeding.');
        return;
      }

      // Format cart items
      const formattedItems = cart.map(item => ({
        _id: item._id,
        quantity: parseInt(item.quantity) || 1,
        price: parseFloat(getDiscountedPrice(item)) || 0,
        originalPrice: parseFloat(item.price) || 0,
        discount: item.discount || 0,
        name: item.name || '',
        images: Array.isArray(item.images) ? item.images : []
      }));

      // Check if gift card covers the entire amount and adjust payment method if needed
      const subtotal = calculateSubtotal();
      const giftCardAmount = giftCard ? giftCard.amount : 0;

      // Calculate the remaining amount to pay after gift card
      const remainingAmount = Math.max(0, subtotal - giftCardAmount);

      // Force gift card payment method if gift card covers the entire amount
      if (giftCardAmount >= subtotal) {
        console.log('Gift card covers entire amount, using gift card payment method');
        method = 'giftcard';
      }

      // If using Razorpay but there's nothing left to pay, switch to gift card
      if (method === 'razorpay' && remainingAmount <= 0) {
        console.log('No remaining amount to pay with Razorpay, switching to gift card payment');
        method = 'giftcard';
      }

      const orderData = {
        items: formattedItems,
        subtotal: parseFloat(calculateSubtotal()) || 0,
        total: parseFloat(calculateTotal()) || 0,
        shippingAddress: shippingAddress.trim(),
        paymentMethod: method,
        paymentId: paymentId,
        giftCardCode: giftCard ? giftCard.code : null,
        giftCardAmount: giftCardAmount,
        couponCode: coupon ? coupon.code : null,
        couponDiscount: coupon ? parseFloat(calculateCouponDiscount(calculateSubtotal())) : 0
      };

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();

      clearCart();
      toast.success('Order placed successfully!');
      router.push(`/orders/${data._id}`);
    } catch (error) {
      console.error('Checkout error:', error);
      toast.error(error.message || 'Failed to place order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!shippingAddress.trim()) {
      toast.error('Please enter a shipping address');
      return;
    }

    // Validate stock before proceeding
    const isStockValid = await validateCartStock();
    if (!isStockValid) {
      toast.error('Some items in your cart are out of stock or quantities have been adjusted');
      return;
    }

    // Calculate if gift card covers the entire amount
    const subtotal = calculateSubtotal();
    const giftCardAmount = giftCard ? giftCard.amount : 0;
    const isFullyCovered = giftCardAmount >= subtotal;

    // Force gift card payment if it covers the entire amount
    if (isFullyCovered) {
      console.log('Gift card fully covers the order amount');

      if (paymentMethod !== 'giftcard') {
        console.log('Switching payment method to gift card');
        setPaymentMethod('giftcard');
        toast.info('Your gift card covers the entire order. Using gift card payment.');
      }

      // Process the order with gift card payment
      await createOrder('giftcard');
      return;
    }

    if (paymentMethod === 'razorpay') {
      try {
        setIsProcessing(true);

        // First, ensure the script is loaded
        if (!scriptLoaded && typeof window !== 'undefined' && !window.Razorpay) {
          console.log('Razorpay script not loaded yet, waiting...');
          toast.info('Preparing payment system...');

          // Wait for up to 5 seconds for the script to load
          for (let i = 0; i < 10; i++) {
            await new Promise(resolve => setTimeout(resolve, 500));
            if (window.Razorpay) {
              console.log('Razorpay script loaded after waiting');
              break;
            }
          }

          // If still not loaded, show error
          if (!window.Razorpay) {
            console.error('Razorpay script failed to load after waiting');
            toast.error('Payment system not available. Please refresh the page and try again.');
            return;
          }
        }

        console.log('Proceeding with Razorpay payment, script loaded:', !!window.Razorpay);

        // Always initialize a new Razorpay order to ensure it's fresh
        console.log('Initializing Razorpay order from handleSubmit');
        const orderData = await initializeRazorpayOrder();

        if (!orderData) {
          console.error('Failed to get order data from initializeRazorpayOrder');
          toast.error('Failed to create payment order. Please try again.');
          return;
        }

        // Check if we have the order data in the ref
        if (!razorpayOrderRef.current) {
          console.error('Order data not stored in ref after initialization');

          // If we have orderData but not in the ref, store it
          if (orderData) {
            console.log('Storing order data in ref directly');
            razorpayOrderRef.current = orderData;
          } else {
            console.error('No order data available in orderData or ref');
            toast.error('Error preparing payment. Please try again.');
            return;
          }
        } else {
          console.log('Order data found in ref:', {
            id: razorpayOrderRef.current.id,
            amount: razorpayOrderRef.current.amount
          });
        }

        // Double-check Razorpay availability
        if (typeof window === 'undefined' || !window.Razorpay) {
          console.error('Razorpay not available after order creation');
          toast.error('Payment system not ready. Please refresh the page and try again.');
          return;
        }

        console.log('Razorpay is available and order is created, proceeding to payment');

        // Double-check that we have a valid order before proceeding
        const orderForPayment = razorpayOrderRef.current || orderData;
        if (!orderForPayment) {
          console.error('No Razorpay order available before payment');
          toast.error('Payment order not created. Please try again.');
          return;
        }

        console.log('Proceeding to payment with order:', {
          id: orderForPayment.id,
          amount: orderForPayment.amount,
          key_exists: !!orderForPayment.key
        });

        // Proceed with Razorpay payment, passing the order data from ref or direct data
        const paymentResult = await handleRazorpayPayment(orderForPayment);

        // If payment initialization failed, show error and let user try again
        if (paymentResult === false) {
          console.log('Razorpay initialization failed, showing error to user');
          toast.error('Payment system initialization failed. Please try again or refresh the page.');
        }
      } catch (error) {
        console.error('Error in Razorpay flow:', error);
        toast.error('Payment initialization failed. Please try again or refresh the page.');
      } finally {
        setIsProcessing(false);
      }
    } else {
      // For COD, create order directly
      await createOrder('cod');
    }
  };

  if (!mounted || !user || !cart?.length) {
    return null;
  }

  // Handle Razorpay script load
  const handleRazorpayLoad = () => {
    console.log('Razorpay script loaded via Next.js Script component');
  };

  return (
    <div className="min-h-screen bg-white pt-20">
      {/* Razorpay Script */}
      <Script
        src="https://checkout.razorpay.com/v1/checkout.js"
        onLoad={handleRazorpayLoad}
        strategy="afterInteractive"
      />
      <div className="max-w-6xl mx-auto px-4 xl:px-0 py-12">
        <h1 className="text-2xl font-light mb-2 text-center">CHECKOUT</h1>
        <p className="text-sm text-center text-gray-500 mb-10">
          Complete your order
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Left side - Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-12">
              {/* Shipping Address */}
              <div className="border-b border-gray-100 pb-8">
                <h2 className="text-sm uppercase tracking-wider mb-6">Delivery Address</h2>
                <div>
                  <textarea
                    value={shippingAddress}
                    onChange={(e) => setShippingAddress(e.target.value)}
                    placeholder="Enter your complete delivery address"
                    rows={4}
                    required
                    className="w-full px-0 py-2 bg-transparent border-0 border-b border-gray-200 focus:ring-0 focus:border-black text-gray-900 resize-none"
                  />
                </div>
              </div>

              {/* Gift Card */}
              <GiftCardRedemption
                onApply={setGiftCard}
                onRemove={() => setGiftCard(null)}
                disabled={isProcessing}
                total={calculateSubtotal()}
              />

              {/* Payment Method */}
              <div className="border-b border-gray-100 pb-8">
                <h2 className="text-sm uppercase tracking-wider mb-6">Payment Method</h2>
                <div className="space-y-4">
                  {/* Cash on Delivery Option */}
                  <div
                    className={`border p-4 rounded cursor-pointer transition-all ${paymentMethod === 'cod' ? 'border-black bg-gray-50' : 'border-gray-200 hover:border-gray-300'}`}
                    onClick={() => setPaymentMethod('cod')}
                  >
                    <label className="flex items-center justify-between cursor-pointer w-full">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          className="form-radio text-black focus:ring-black"
                          name="payment"
                          value="cod"
                          checked={paymentMethod === 'cod'}
                          onChange={() => setPaymentMethod('cod')}
                        />
                        <div className="ml-3">
                          <span className="text-sm font-medium">Cash on Delivery</span>
                          <p className="text-xs text-gray-500 mt-1">Pay when your order is delivered</p>
                        </div>
                      </div>
                      <span className="text-xs bg-gray-100 px-2 py-1 rounded">Available</span>
                    </label>
                  </div>

                  {/* Gift Card Only Option - Show only if gift card covers the total amount */}
                  {giftCard && giftCard.amount >= calculateSubtotal() && (
                    <div
                      className={`border p-4 rounded cursor-pointer transition-all ${paymentMethod === 'giftcard' ? 'border-black bg-gray-50' : 'border-gray-200 hover:border-gray-300'}`}
                      onClick={() => setPaymentMethod('giftcard')}
                    >
                      <label className="flex items-center justify-between cursor-pointer w-full">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            className="form-radio text-black focus:ring-black"
                            name="payment"
                            value="giftcard"
                            checked={paymentMethod === 'giftcard'}
                            onChange={() => setPaymentMethod('giftcard')}
                          />
                          <div className="ml-3">
                            <span className="text-sm font-medium">Gift Card</span>
                            <p className="text-xs text-gray-500 mt-1">Pay with gift card balance</p>
                          </div>
                        </div>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Applied</span>
                      </label>
                    </div>
                  )}

                  {/* Online Payment Option - Hide if gift card covers the total amount */}
                  {(!giftCard || giftCard.amount < calculateSubtotal()) && (
                    <div
                      className={`border p-4 rounded cursor-pointer transition-all ${paymentMethod === 'razorpay' ? 'border-black bg-gray-50' : 'border-gray-200 hover:border-gray-300'}`}
                      onClick={() => setPaymentMethod('razorpay')}
                    >
                      <label className="flex items-center justify-between cursor-pointer w-full">
                        <div className="flex items-center">
                          <input
                            type="radio"
                            className="form-radio text-black focus:ring-black"
                            name="payment"
                            value="razorpay"
                            checked={paymentMethod === 'razorpay'}
                            onChange={() => setPaymentMethod('razorpay')}
                          />
                          <div className="ml-3">
                            <span className="text-sm font-medium">Online Payment</span>
                            <p className="text-xs text-gray-500 mt-1">Cards, UPI, NetBanking, Wallets</p>
                          </div>
                        </div>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Secure</span>
                      </label>

                      {/* Payment method icons */}
                      {paymentMethod === 'razorpay' && (
                        <div className="mt-3 ml-7 grid grid-cols-5 gap-2">
                          <div className="text-xs text-center">
                            <div className="bg-gray-100 p-1 rounded mb-1">Cards</div>
                          </div>
                          <div className="text-xs text-center">
                            <div className="bg-gray-100 p-1 rounded mb-1">UPI</div>
                          </div>
                          <div className="text-xs text-center">
                            <div className="bg-gray-100 p-1 rounded mb-1">NetBanking</div>
                          </div>
                          <div className="text-xs text-center">
                            <div className="bg-gray-100 p-1 rounded mb-1">Wallets</div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Contact Information */}
              <div className="border-b border-gray-100 pb-8">
                <h2 className="text-sm uppercase tracking-wider mb-6">Contact Information</h2>
                <p className="text-sm mb-2">{user.name}</p>
                <p className="text-sm mb-2">{user.email}</p>
                <p className="text-sm">{user.phone || 'No phone number provided'}</p>
              </div>

              <div className="mt-8">
                <button
                  type="submit"
                  disabled={isProcessing}
                  className="w-full py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isProcessing ? 'Processing...' :
                    paymentMethod === 'razorpay' ? 'Continue to Payment' : 'Place Order'
                  }
                </button>
                <Link
                  href="/cart"
                  className="flex items-center text-xs uppercase hover:opacity-70 transition-opacity duration-200 mt-4"
                >
                  <span>Return to shopping cart</span>
                  <FiChevronRight size={12} className="ml-1" />
                </Link>
              </div>
            </form>
          </div>

          {/* Right side - Order Summary */}
          <div className="lg:col-span-1">
            <div className="border-t border-b border-gray-100 py-8">
              <h2 className="text-sm uppercase tracking-wider mb-6">Order Summary</h2>

              <div className="space-y-6 mb-8">
                {cart.map((item) => (
                  <div key={item._id} className="flex gap-4">
                    <div className="w-16 aspect-[4/5] relative flex-shrink-0 bg-gray-100">
                      {item.images?.[0] && (
                        <Image
                          src={item.images[0]}
                          alt={item.name}
                          fill
                          className="object-cover object-center"
                        />
                      )}
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-xs font-medium">{item.name}</h3>
                      <p className="text-xs text-gray-500 mt-1">Qty: {item.quantity}</p>
                      {item.discount && item.discount > 0 ? (
                        <div className="mt-1">
                          <p className="text-xs">₹{formatPrice(getDiscountedPrice(item) * item.quantity)}</p>
                          <p className="text-xs text-gray-500 line-through">₹{formatPrice(item.price * item.quantity)}</p>
                          <p className="text-xs text-green-600">{item.discount}% OFF</p>
                        </div>
                      ) : (
                        <p className="text-xs mt-1">₹{formatPrice(item.price * item.quantity)}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-3">
                {/* Original Price Total */}
                <div className="flex justify-between text-sm">
                  <span>Original Price</span>
                  <span>₹{formatPrice(cart.reduce((total, item) => {
                    const price = Number(item.price) || 0;
                    const quantity = Number(item.quantity) || 0;
                    return total + (price * quantity);
                  }, 0))}</span>
                </div>

                {/* Discount */}
                {cart.some(item => item.discount > 0) && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-₹{formatPrice(cart.reduce((total, item) => {
                      if (!item.discount || item.discount <= 0) return total;
                      const originalPrice = Number(item.price) || 0;
                      const discountedPrice = getDiscountedPrice(item);
                      const quantity = Number(item.quantity) || 0;
                      return total + ((originalPrice - discountedPrice) * quantity);
                    }, 0))}</span>
                  </div>
                )}

                {/* Subtotal after discount */}
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>₹{formatPrice(calculateSubtotal())}</span>
                </div>

                {/* Shipping */}
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>

                {/* Coupon Discount */}
                {coupon && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Coupon Discount ({coupon.code})</span>
                    <span>-₹{formatPrice(calculateCouponDiscount(calculateSubtotal()))}</span>
                  </div>
                )}

                {/* Gift Card Applied */}
                {giftCard && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Gift Card Applied</span>
                    <span>-₹{formatPrice(giftCard.amount)}</span>
                  </div>
                )}

                {/* Total */}
                <div className="border-t border-gray-100 pt-3 mt-4">
                  <div className="flex justify-between text-sm font-medium">
                    <span>Total</span>
                    <span>₹{formatPrice(calculateTotal())}</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Including GST</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}