'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import { FiAlertCircle, FiEye, FiEyeOff, FiCheck, FiInfo } from 'react-icons/fi';
import Image from 'next/image';
import Link from 'next/link';

export default function LoginPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState({
    name: '',
    email: '',
    password: '',
  });
  const [emailStatus, setEmailStatus] = useState({
    checking: false,
    exists: false,
    checked: false
  });
  const [submissionError, setSubmissionError] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendingVerification, setResendingVerification] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);
  const emailCheckTimeoutRef = useRef(null);
  const router = useRouter();
  const { login, register } = useAuth();

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password) => {
    return password.length >= 8;
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: '',
      email: '',
      password: '',
    };

    // Validate name if on register page
    if (!isLogin && !formData.name.trim()) {
      newErrors.name = 'Name is required';
      isValid = false;
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
      isValid = false;
    } else if (!isLogin && emailStatus.checked && emailStatus.exists) {
      // If registering and email already exists
      newErrors.email = 'This email is already registered. Please log in instead.';
      isValid = false;
    } else if (isLogin && emailStatus.checked && !emailStatus.exists) {
      // If logging in and email doesn't exist
      newErrors.email = 'No account found with this email. Please register first.';
      isValid = false;
    }

    // Validate password
    if (!formData.password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (!validatePassword(formData.password)) {
      newErrors.password = 'Password must be at least 8 characters';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmissionError('');

    // If email is valid but hasn't been checked yet, check it now
    if (validateEmail(formData.email) && !emailStatus.checked) {
      await checkEmailExists(formData.email);
    }

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      if (isLogin) {
        const result = await login(formData.email, formData.password);
        if (!result.success) {
          setSubmissionError(result.error);
        }
      } else {
        const result = await register(formData.name, formData.email, formData.password);
        if (!result.success) {
          setSubmissionError(result.error);
        }
      }
    } catch (error) {
      setSubmissionError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkEmailExists = async (email) => {
    if (!validateEmail(email)) return;

    setEmailStatus(prev => ({ ...prev, checking: true }));

    try {
      const response = await fetch('/api/auth/check-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (response.ok) {
        setEmailStatus({
          checking: false,
          exists: data.exists,
          checked: true
        });
      } else {
        throw new Error(data.error || 'Failed to check email');
      }
    } catch (error) {
      console.error('Error checking email:', error);
      setEmailStatus({
        checking: false,
        exists: false,
        checked: false
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear specific field error when user starts typing again
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Check email existence with debounce
    if (name === 'email' && validateEmail(value)) {
      // Clear any existing timeout
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current);
      }

      // Set a new timeout
      emailCheckTimeoutRef.current = setTimeout(() => {
        checkEmailExists(value);
      }, 500); // 500ms debounce
    } else if (name === 'email' && !value) {
      // Reset email status when field is cleared
      setEmailStatus({
        checking: false,
        exists: false,
        checked: false
      });
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const handleResendVerification = async () => {
    if (!validateEmail(formData.email)) {
      setSubmissionError('Please enter a valid email address to resend verification');
      return;
    }

    setResendingVerification(true);

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: formData.email })
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationSent(true);
      } else {
        setSubmissionError(data.error || 'Failed to resend verification email');
      }
    } catch (error) {
      setSubmissionError('An error occurred. Please try again.');
    } finally {
      setResendingVerification(false);
    }
  };

  return (
    <div className="min-h-screen bg-white pt-16 md:pt-20">
      <div className="max-w-screen-xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 h-[calc(100vh-4rem)]">
          {/* Left side - Image for larger screens */}
          <div className="hidden lg:block relative h-full">
            <Image
              src="/images/modern-furniture.jpg"
              alt="FURNITUREBAZAAR"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
              <div className="text-center text-white px-8">
                <h1 className="text-2xl font-light tracking-wider uppercase mb-4">FURNITUREBAZAAR</h1>
              </div>
            </div>
          </div>

          {/* Right side - Form */}
          <div className="w-full px-4 sm:px-8 flex items-center justify-center">
            <div className="w-full max-w-md">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="pb-12"
              >
                {/* Form header */}
                <div className="mb-12 text-center">
                  <h1 className="text-xl font-light uppercase tracking-widest mb-2">
                    {isLogin ? 'LOG IN TO YOUR ACCOUNT' : 'CREATE AN ACCOUNT'}
                  </h1>
                  <p className="text-xs text-gray-500 tracking-wide">
                    {isLogin
                      ? 'Enter your email and password to access your account'
                      : 'Join FURNITUREBAZAAR to discover our premium collections'
                    }
                  </p>
                </div>

                {submissionError && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-red-200 p-3 mb-6"
                  >
                    <div className="flex items-center">
                      <FiAlertCircle className="text-red-500 mr-2 flex-shrink-0" />
                      <p className="text-xs text-red-500">{submissionError}</p>
                    </div>

                    {submissionError.includes('verify your email') && (
                      <div className="mt-2 pt-2 border-t border-red-100">
                        <button
                          type="button"
                          onClick={handleResendVerification}
                          disabled={resendingVerification || verificationSent}
                          className="text-xs text-blue-600 hover:underline focus:outline-none disabled:text-gray-400"
                        >
                          {resendingVerification
                            ? 'Sending verification email...'
                            : verificationSent
                              ? 'Verification email sent! Check your inbox.'
                              : 'Resend verification email'}
                        </button>
                      </div>
                    )}
                  </motion.div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {!isLogin && (
                    <div className="space-y-1">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        placeholder="FULL NAME"
                        value={formData.name}
                        onChange={handleChange}
                        className={`w-full bg-transparent border-b ${
                          errors.name ? 'border-red-500' : 'border-gray-200'
                        } py-2 text-sm focus:outline-none focus:border-black transition-colors`}
                      />
                      {errors.name && (
                        <p className="text-xs text-red-500">{errors.name}</p>
                      )}
                    </div>
                  )}

                  <div className="space-y-1">
                    <div className="relative">
                      <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="EMAIL ADDRESS"
                        value={formData.email}
                        onChange={handleChange}
                        onBlur={() => {
                          if (validateEmail(formData.email)) {
                            checkEmailExists(formData.email);
                          }
                        }}
                        className={`w-full bg-transparent border-b ${
                          errors.email ? 'border-red-500' :
                          emailStatus.checked && !isLogin && emailStatus.exists ? 'border-red-500' :
                          emailStatus.checked && isLogin && emailStatus.exists ? 'border-green-500' :
                          'border-gray-200'
                        } py-2 text-sm focus:outline-none focus:border-black transition-colors pr-10`}
                      />
                      {emailStatus.checking && (
                        <span className="absolute right-0 top-2 text-gray-400">
                          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </span>
                      )}
                      {emailStatus.checked && !emailStatus.checking && isLogin && emailStatus.exists && (
                        <span className="absolute right-0 top-2 text-green-500">
                          <FiCheck className="h-5 w-5" />
                        </span>
                      )}
                      {emailStatus.checked && !emailStatus.checking && !isLogin && emailStatus.exists && (
                        <span className="absolute right-0 top-2 text-red-500">
                          <FiInfo className="h-5 w-5" />
                        </span>
                      )}
                    </div>
                    {errors.email && (
                      <p className="text-xs text-red-500">{errors.email}</p>
                    )}
                    {!errors.email && emailStatus.checked && !isLogin && emailStatus.exists && (
                      <p className="text-xs text-red-500">This email is already registered. Please log in instead.</p>
                    )}
                    {!errors.email && emailStatus.checked && isLogin && !emailStatus.exists && (
                      <p className="text-xs text-amber-500">No account found with this email. Please register first.</p>
                    )}
                  </div>

                  <div className="space-y-1 relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      placeholder="PASSWORD"
                      value={formData.password}
                      onChange={handleChange}
                      className={`w-full bg-transparent border-b ${
                        errors.password ? 'border-red-500' : 'border-gray-200'
                      } py-2 text-sm focus:outline-none focus:border-black transition-colors pr-10`}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-0 top-2 text-gray-400 hover:text-gray-700 focus:outline-none"
                      aria-label={showPassword ? "Hide password" : "Show password"}
                    >
                      {showPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                    </button>
                    {errors.password && (
                      <p className="text-xs text-red-500">{errors.password}</p>
                    )}
                    {!isLogin && !errors.password && (
                      <p className="text-xs text-gray-500">Password must be at least 8 characters</p>
                    )}
                  </div>

                  {isLogin && (
                    <div className="text-right">
                      <Link href="/forgot-password" className="text-xs text-gray-500 hover:text-black hover:underline">
                        FORGOT PASSWORD?
                      </Link>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full border border-black bg-black text-white py-3 uppercase text-xs tracking-widest hover:bg-white hover:text-black transition-colors duration-300 disabled:bg-gray-300 disabled:border-gray-300"
                  >
                    {loading ? 'PLEASE WAIT...' : isLogin ? 'LOG IN' : 'CREATE ACCOUNT'}
                  </button>

                  <div className="pt-4">
                    <button
                      type="button"
                      onClick={() => setIsLogin(!isLogin)}
                      className="w-full text-center text-xs text-gray-700 hover:underline uppercase tracking-wide"
                    >
                      {isLogin
                        ? "DON'T HAVE AN ACCOUNT? REGISTER"
                        : 'ALREADY HAVE AN ACCOUNT? LOG IN'
                      }
                    </button>
                  </div>
                </form>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}