'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowRight } from 'react-icons/fi';

export default function Editorial() {
  const editorials = [
    {
      id: 'minimalist-living',
      title: 'THE ART OF MINIMALIST LIVING',
      subtitle: 'EDITORIAL 2024',
      description: 'Explore the beauty of simplicity and intentional design in our latest collection.',
      image: '/images/editorial/minimalist-hero.jpg',
      date: 'MARCH 2024'
    },
    {
      id: 'sustainable-living',
      title: 'SUSTAINABLE LIVING',
      subtitle: 'EDITORIAL 2024',
      description: 'Discover our eco-conscious collection crafted with respect for nature.',
      image: '/images/editorial/sustainable-hero.jpg',
      date: 'FEBRUARY 2024'
    },
    {
      id: 'urban-retreat',
      title: 'URBAN RETREAT',
      subtitle: 'EDITORIAL 2024',
      description: 'Transform your city home into a peaceful sanctuary.',
      image: '/images/editorial/urban-retreat-hero.jpg',
      date: 'JANUARY 2024'
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen">
        <Image
          src="/images/editorial/editorial-main.jpg"
          alt="Editorial Collection"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-sm tracking-[0.2em] mb-4"
          >
            FURNITUREBAAZAR
          </motion.p>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-4xl md:text-6xl lg:text-7xl tracking-[0.1em] text-center font-light"
          >
            EDITORIAL
          </motion.h1>
        </div>
      </section>

      {/* Editorial Grid */}
      <section className="py-24">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-24">
            {editorials.map((editorial, index) => (
              <motion.div
                key={editorial.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2 }}
              >
                <Link href={`/editorial/${editorial.id}`} className="group">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                    <div className="relative aspect-[4/5]">
                      <Image
                        src={editorial.image}
                        alt={editorial.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="space-y-8">
                      <div>
                        <p className="text-sm tracking-[0.2em] text-gray-500 mb-4">
                          {editorial.date}
                        </p>
                        <p className="text-sm tracking-[0.2em] mb-4">
                          {editorial.subtitle}
                        </p>
                        <h2 className="text-3xl md:text-4xl tracking-[0.1em] font-light">
                          {editorial.title}
                        </h2>
                      </div>
                      <p className="text-gray-600">
                        {editorial.description}
                      </p>
                      <div className="flex items-center group-hover:text-gray-600">
                        <span className="text-sm tracking-[0.2em] mr-4">VIEW EDITORIAL</span>
                        <FiArrowRight className="transform group-hover:translate-x-2 transition-transform" />
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}