'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>Calendar, FiUser, FiTag, FiSearch, FiArrowRight } from 'react-icons/fi';

export default function BlogPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('All');

  // Blog categories
  const categories = [
    { name: 'All', count: 12 },
    { name: 'Interior Design', count: 5 },
    { name: 'Furniture Care', count: 3 },
    { name: 'Buying Guides', count: 2 },
    { name: 'Trends', count: 2 }
  ];

  // Featured article
  const featuredArticle = {
    id: 1,
    title: "10 Interior Design Trends to Transform Your Home in 2024",
    excerpt: "Discover the hottest interior design trends of 2024 that will help you refresh your living space and create a stylish, comfortable home that reflects your personality.",
    category: "Interior Design",
    author: "<PERSON><PERSON>",
    date: "March 15, 2024",
    readTime: "8 min read",
    image: "/blog/interior-trends-2024.jpg",
    slug: "interior-design-trends-2024"
  };

  // Blog articles
  const blogArticles = [
    {
      id: 2,
      title: "How to Choose the Perfect Sofa for Your Living Room",
      excerpt: "A comprehensive guide to selecting the ideal sofa that combines comfort, style, and durability for your living space.",
      category: "Buying Guides",
      author: "Rahul Patel",
      date: "March 10, 2024",
      readTime: "6 min read",
      image: "/blog/perfect-sofa-guide.jpg",
      slug: "choose-perfect-sofa-living-room"
    },
    {
      id: 3,
      title: "5 Essential Tips for Maintaining Wooden Furniture",
      excerpt: "Learn how to properly care for your wooden furniture to ensure it remains beautiful and lasts for generations.",
      category: "Furniture Care",
      author: "Ananya Desai",
      date: "March 5, 2024",
      readTime: "5 min read",
      image: "/blog/wooden-furniture-care.jpg",
      slug: "wooden-furniture-maintenance-tips"
    },
    {
      id: 4,
      title: "Small Space Solutions: Maximizing Your Apartment",
      excerpt: "Creative furniture arrangements and multi-functional pieces that help you make the most of limited living space.",
      category: "Interior Design",
      author: "Vikram Mehta",
      date: "February 28, 2024",
      readTime: "7 min read",
      image: "/blog/small-space-solutions.jpg",
      slug: "small-space-furniture-solutions"
    },
    {
      id: 5,
      title: "The Art of Mixing and Matching Furniture Styles",
      excerpt: "How to successfully combine different furniture styles to create a cohesive and personalized interior design.",
      category: "Interior Design",
      author: "Priya Sharma",
      date: "February 20, 2024",
      readTime: "6 min read",
      image: "/blog/mixing-furniture-styles.jpg",
      slug: "mixing-matching-furniture-styles"
    },
    {
      id: 6,
      title: "Sustainable Furniture: Eco-Friendly Choices for Your Home",
      excerpt: "A guide to environmentally conscious furniture options that don't compromise on style or quality.",
      category: "Buying Guides",
      author: "Arjun Singh",
      date: "February 15, 2024",
      readTime: "7 min read",
      image: "/blog/sustainable-furniture.jpg",
      slug: "sustainable-eco-friendly-furniture"
    },
    {
      id: 7,
      title: "How to Remove Common Stains from Upholstery",
      excerpt: "Practical tips and techniques for removing various types of stains from your upholstered furniture.",
      category: "Furniture Care",
      author: "Neha Kapoor",
      date: "February 10, 2024",
      readTime: "5 min read",
      image: "/blog/upholstery-stain-removal.jpg",
      slug: "remove-stains-from-upholstery"
    },
    {
      id: 8,
      title: "Creating a Productive Home Office Space",
      excerpt: "Design ideas and furniture recommendations for a comfortable and efficient home workspace.",
      category: "Interior Design",
      author: "Rahul Patel",
      date: "February 5, 2024",
      readTime: "6 min read",
      image: "/blog/home-office-design.jpg",
      slug: "productive-home-office-design"
    },
    {
      id: 9,
      title: "The Return of Vintage Furniture: Nostalgic Designs Making a Comeback",
      excerpt: "Explore how classic furniture styles from past decades are being reimagined for contemporary homes.",
      category: "Trends",
      author: "Ananya Desai",
      date: "January 28, 2024",
      readTime: "7 min read",
      image: "/blog/vintage-furniture-trends.jpg",
      slug: "vintage-furniture-comeback"
    }
  ];

  // Filter articles based on category and search term
  const filteredArticles = blogArticles.filter(article => {
    const matchesCategory = activeCategory === 'All' || article.category === activeCategory;
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Placeholder image component for demo purposes
  // In a real implementation, you would use actual images
  const PlaceholderImage = ({ className, alt }) => (
    <div className={`bg-gray-200 ${className} flex items-center justify-center`}>
      <span className="text-gray-400 text-sm">{alt || 'Image'}</span>
    </div>
  );

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-b from-amber-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              FurnitureBazaar Blog
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-600 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Inspiration, tips, and insights for creating your dream home
            </motion.p>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0 opacity-10">
          <div className="absolute -top-24 -left-24 w-96 h-96 rounded-full bg-amber-300"></div>
          <div className="absolute top-1/2 -right-24 w-64 h-64 rounded-full bg-emerald-300"></div>
        </div>
      </section>

      {/* Featured Article */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-8">Featured Article</h2>
              
              <div className="bg-white rounded-xl overflow-hidden shadow-lg">
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="relative h-64 md:h-auto">
                    {/* In a real implementation, replace with actual Image component */}
                    <PlaceholderImage 
                      className="w-full h-full object-cover" 
                      alt={featuredArticle.title}
                    />
                  </div>
                  <div className="p-6 md:p-8">
                    <div className="flex items-center mb-4">
                      <span className="inline-block px-3 py-1 bg-amber-100 text-amber-800 text-sm font-medium rounded-full">
                        {featuredArticle.category}
                      </span>
                      <span className="mx-2 text-gray-300">•</span>
                      <span className="text-gray-500 text-sm flex items-center">
                        <FiCalendar className="mr-1" size={14} />
                        {featuredArticle.date}
                      </span>
                    </div>
                    
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {featuredArticle.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-6">
                      {featuredArticle.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gray-200 rounded-full mr-3"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{featuredArticle.author}</p>
                          <p className="text-xs text-gray-500">{featuredArticle.readTime}</p>
                        </div>
                      </div>
                      
                      <Link 
                        href={`/blog/${featuredArticle.slug}`}
                        className="inline-flex items-center text-amber-600 font-medium hover:text-amber-700"
                      >
                        Read Article
                        <FiArrowRight className="ml-2" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Main Content */}
              <div className="lg:w-2/3">
                {/* Search */}
                <div className="mb-8">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search articles..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                {/* Articles Grid */}
                {filteredArticles.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {filteredArticles.map((article, index) => (
                      <motion.div
                        key={article.id}
                        className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className="relative h-48">
                          {/* In a real implementation, replace with actual Image component */}
                          <PlaceholderImage 
                            className="w-full h-full object-cover" 
                            alt={article.title}
                          />
                        </div>
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            <span className="inline-block px-2 py-1 bg-amber-100 text-amber-800 text-xs font-medium rounded-full">
                              {article.category}
                            </span>
                            <span className="mx-2 text-gray-300">•</span>
                            <span className="text-gray-500 text-xs flex items-center">
                              <FiCalendar className="mr-1" size={12} />
                              {article.date}
                            </span>
                          </div>
                          
                          <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                            {article.title}
                          </h3>
                          
                          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                            {article.excerpt}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-gray-200 rounded-full mr-2"></div>
                              <p className="text-xs text-gray-500">{article.author}</p>
                            </div>
                            
                            <Link 
                              href={`/blog/${article.slug}`}
                              className="text-sm text-amber-600 font-medium hover:text-amber-700"
                            >
                              Read More
                            </Link>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12 bg-white rounded-xl">
                    <p className="text-gray-600 text-lg">No articles found matching your criteria.</p>
                    <button 
                      className="mt-4 text-amber-600 hover:text-amber-700"
                      onClick={() => {
                        setSearchTerm('');
                        setActiveCategory('All');
                      }}
                    >
                      Clear filters
                    </button>
                  </div>
                )}
                
                {/* Pagination - For demo purposes */}
                {filteredArticles.length > 0 && (
                  <div className="mt-12 flex justify-center">
                    <nav className="flex items-center space-x-2">
                      <button className="px-3 py-2 rounded-md bg-amber-50 text-amber-600 font-medium">1</button>
                      <button className="px-3 py-2 rounded-md text-gray-500 hover:bg-gray-100">2</button>
                      <button className="px-3 py-2 rounded-md text-gray-500 hover:bg-gray-100">3</button>
                      <span className="px-2 text-gray-400">...</span>
                      <button className="px-3 py-2 rounded-md text-gray-500 hover:bg-gray-100">Next</button>
                    </nav>
                  </div>
                )}
              </div>
              
              {/* Sidebar */}
              <div className="lg:w-1/3">
                {/* Categories */}
                <motion.div
                  className="bg-white rounded-xl p-6 shadow-sm mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Categories</h3>
                  <ul className="space-y-2">
                    {categories.map((category, index) => (
                      <li key={index}>
                        <button
                          className={`flex items-center justify-between w-full px-3 py-2 rounded-md text-left ${
                            activeCategory === category.name
                              ? 'bg-amber-50 text-amber-600 font-medium'
                              : 'text-gray-600 hover:bg-gray-50'
                          }`}
                          onClick={() => setActiveCategory(category.name)}
                        >
                          <span>{category.name}</span>
                          <span className="text-sm text-gray-400">({category.count})</span>
                        </button>
                      </li>
                    ))}
                  </ul>
                </motion.div>
                
                
                
                {/* Newsletter Signup */}
                <motion.div
                  className="bg-amber-50 rounded-xl p-6"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <h3 className="text-lg font-bold text-gray-900 mb-2">Subscribe to Our Newsletter</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Get the latest articles, design tips, and furniture care advice delivered to your inbox.
                  </p>
                  <form className="space-y-3">
                    <input
                      type="email"
                      placeholder="Your email address"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                      required
                    />
                    <button
                      type="submit"
                      className="w-full px-4 py-2 bg-amber-600 text-white font-medium rounded-md hover:bg-amber-700 transition duration-300"
                    >
                      Subscribe
                    </button>
                  </form>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Return to Home */}
      <div className="py-12 text-center">
        <Link 
          href="/" 
          className="inline-flex items-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-50 font-medium rounded-md transition duration-300"
        >
          Return to Home
        </Link>
      </div>
    </div>
  );
} 