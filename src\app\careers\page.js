'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiUsers, FiStar, FiHeart, FiTrendingUp, FiSearch, FiChevronDown, FiChevronUp } from 'react-icons/fi';

export default function CareersPage() {
  const [activeFilter, setActiveFilter] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedJob, setExpandedJob] = useState(null);

  // Company values
  const companyValues = [
    {
      title: "Customer Obsession",
      description: "We put our customers first in everything we do, striving to exceed their expectations and create delightful experiences.",
      icon: FiUsers
    },
    {
      title: "Quality Excellence",
      description: "We are committed to crafting furniture of exceptional quality, paying attention to every detail from design to delivery.",
      icon: FiStar
    },
    {
      title: "Sustainability",
      description: "We prioritize sustainable practices, sourcing responsibly and minimizing our environmental footprint.",
      icon: FiHeart
    },
    {
      title: "Innovation",
      description: "We continuously innovate in our designs, materials, and processes to stay at the forefront of the furniture industry.",
      icon: FiTrendingUp
    }
  ];

  // Job categories
  const jobCategories = ['All', 'Design', 'Technology', 'Marketing', 'Operations', 'Customer Service', 'Finance'];

  // Job listings
  const jobListings = [
    {
      title: "Senior Furniture Designer",
      department: "Design",
      location: "Surat, Gujarat (Hybrid)",
      type: "Full-time",
      description: "We're looking for a Senior Furniture Designer to join our creative team. You'll be responsible for designing innovative and functional furniture pieces that align with our brand aesthetic and customer needs.",
      responsibilities: [
        "Create original furniture designs for various product lines",
        "Develop technical drawings and specifications for manufacturing",
        "Collaborate with product development team to ensure designs are feasible",
        "Stay updated on industry trends and incorporate relevant elements into designs",
        "Mentor junior designers and provide feedback on their work"
      ],
      requirements: [
        "5+ years of experience in furniture design",
        "Bachelor's degree in Industrial Design, Furniture Design, or related field",
        "Proficiency in CAD software and 3D modeling tools",
        "Strong portfolio demonstrating furniture design expertise",
        "Excellent communication and collaboration skills"
      ]
    },
    {
      title: "Full Stack Developer",
      department: "Technology",
      location: "Remote (India)",
      type: "Full-time",
      description: "We're seeking a talented Full Stack Developer to help enhance our e-commerce platform. You'll work on both frontend and backend development to create seamless user experiences and robust functionality.",
      responsibilities: [
        "Develop and maintain our e-commerce website and internal tools",
        "Implement responsive designs and ensure cross-browser compatibility",
        "Build RESTful APIs and integrate with third-party services",
        "Optimize applications for maximum speed and scalability",
        "Collaborate with design and product teams to implement new features"
      ],
      requirements: [
        "3+ years of experience in full stack development",
        "Proficiency in React, Next.js, Node.js, and SQL/NoSQL databases",
        "Experience with e-commerce platforms and payment gateways",
        "Strong understanding of web performance optimization",
        "Knowledge of AWS or similar cloud services"
      ]
    },
    {
      title: "Digital Marketing Specialist",
      department: "Marketing",
      location: "Surat, Gujarat (On-site)",
      type: "Full-time",
      description: "We're looking for a Digital Marketing Specialist to drive our online presence and customer acquisition. You'll be responsible for creating and executing digital marketing campaigns across various channels.",
      responsibilities: [
        "Plan and execute digital marketing campaigns across social media, email, and search",
        "Manage social media accounts and create engaging content",
        "Analyze campaign performance and optimize for better results",
        "Collaborate with content team to develop marketing materials",
        "Stay updated on digital marketing trends and best practices"
      ],
      requirements: [
        "3+ years of experience in digital marketing",
        "Proficiency in social media management and email marketing tools",
        "Experience with Google Analytics, Google Ads, and Facebook Ads",
        "Strong analytical skills and data-driven approach",
        "Excellent written and verbal communication skills"
      ]
    },
    {
      title: "Logistics Coordinator",
      department: "Operations",
      location: "Surat, Gujarat (On-site)",
      type: "Full-time",
      description: "We're seeking a detail-oriented Logistics Coordinator to manage our product delivery operations. You'll ensure that our furniture is delivered to customers efficiently and in perfect condition.",
      responsibilities: [
        "Coordinate with warehouse team and delivery partners",
        "Schedule and track deliveries to ensure timely fulfillment",
        "Resolve delivery issues and customer concerns",
        "Optimize delivery routes and processes for efficiency",
        "Maintain accurate records of inventory and shipments"
      ],
      requirements: [
        "2+ years of experience in logistics or supply chain management",
        "Familiarity with logistics software and inventory management systems",
        "Strong problem-solving and organizational skills",
        "Excellent communication and customer service abilities",
        "Knowledge of furniture delivery logistics is a plus"
      ]
    },
    {
      title: "Customer Support Representative",
      department: "Customer Service",
      location: "Surat, Gujarat (Hybrid)",
      type: "Full-time",
      description: "We're looking for a Customer Support Representative to provide exceptional service to our customers. You'll be the voice of our brand, addressing inquiries and ensuring customer satisfaction.",
      responsibilities: [
        "Respond to customer inquiries via phone, email, and chat",
        "Process orders, returns, and exchanges",
        "Provide product information and assist with purchase decisions",
        "Resolve customer complaints and escalate issues when necessary",
        "Maintain accurate records of customer interactions"
      ],
      requirements: [
        "1+ years of experience in customer service",
        "Excellent communication and interpersonal skills",
        "Ability to remain calm and professional in challenging situations",
        "Basic computer skills and familiarity with CRM systems",
        "Fluency in English and Hindi; additional languages are a plus"
      ]
    },
    {
      title: "Financial Analyst",
      department: "Finance",
      location: "Surat, Gujarat (On-site)",
      type: "Full-time",
      description: "We're seeking a Financial Analyst to support our financial planning and analysis. You'll help us make data-driven decisions by providing financial insights and recommendations.",
      responsibilities: [
        "Prepare financial reports and forecasts",
        "Analyze financial data and identify trends",
        "Support budget planning and expense management",
        "Conduct cost analysis for new products and initiatives",
        "Collaborate with department heads on financial matters"
      ],
      requirements: [
        "3+ years of experience in financial analysis",
        "Bachelor's degree in Finance, Accounting, or related field",
        "Proficiency in Excel and financial modeling",
        "Strong analytical and problem-solving skills",
        "Knowledge of ERP systems and financial software"
      ]
    }
  ];

  // Filter jobs based on category and search term
  const filteredJobs = jobListings.filter(job => {
    const matchesCategory = activeFilter === 'All' || job.department === activeFilter;
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          job.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Toggle job details
  const toggleJobDetails = (index) => {
    setExpandedJob(expandedJob === index ? null : index);
  };

  return (
    <div className="bg-white min-h-screen pt-10 md:pt-16">
      {/* Hero Section - Zara Style */}
      <section className="relative py-8 md:py-12 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p
              className="text-xs uppercase tracking-wider text-gray-500 mb-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              Careers
            </motion.p>
            <motion.h1
              className="text-2xl md:text-3xl font-light text-black mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              Join Our Team
            </motion.h1>
            <motion.p
              className="text-sm md:text-base text-gray-600 max-w-2xl mb-8"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              Build your career with FurnitureBazaar and help us transform homes across India with thoughtfully designed furniture and exceptional customer experiences.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <a
                href="#open-positions"
                className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
              >
                View Open Positions
              </a>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Company Culture Section - Zara Style */}
      <section className="py-12 md:py-16 bg-white">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Our Culture & Values</h2>
              <p className="text-sm text-gray-600">
                At FurnitureBazaar, we're passionate about creating beautiful spaces and exceptional experiences.
                Our team is driven by shared values that guide everything we do.
              </p>
            </motion.div>

            <div className="space-y-12 mb-16">
              {companyValues.map((value, index) => (
                <motion.div
                  key={index}
                  className="border-t border-black/5 pt-6"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-start">
                    <div className="mr-6">
                      <value.icon className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">{value.title}</h3>
                      <p className="text-sm text-gray-600">{value.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="border-t border-black/5 pt-12"
            >
              <h3 className="text-xl font-light mb-8">Why Work With Us?</h3>
              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <h4 className="text-base font-light mb-3">Growth Opportunities</h4>
                  <p className="text-sm text-gray-600">
                    We invest in our team's development with training, mentorship, and clear career paths.
                  </p>
                </div>
                <div className="border-t border-black/5 pt-6">
                  <h4 className="text-base font-light mb-3">Work-Life Balance</h4>
                  <p className="text-sm text-gray-600">
                    We promote flexible work arrangements and respect personal time to ensure wellbeing.
                  </p>
                </div>
                <div className="border-t border-black/5 pt-6">
                  <h4 className="text-base font-light mb-3">Inclusive Environment</h4>
                  <p className="text-sm text-gray-600">
                    We celebrate diversity and create a workplace where everyone feels valued and heard.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Open Positions Section - Zara Style */}
      <section id="open-positions" className="py-12 md:py-16 bg-white border-t border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Open Positions</h2>
              <p className="text-sm text-gray-600">
                Join our team and be part of our journey to transform the furniture industry in India.
              </p>
            </motion.div>

            {/* Search and Filter - Zara Style */}
            <div className="mb-12 border-t border-black/5 pt-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    placeholder="SEARCH POSITIONS"
                    className="w-full pl-0 pr-8 py-2 border-0 border-b border-black/10 focus:outline-none focus:border-black text-xs uppercase tracking-wider"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <FiSearch className="absolute right-0 top-1/2 transform -translate-y-1/2 text-black/60" />
                </div>
                <div className="flex-shrink-0">
                  <div className="relative inline-block w-full md:w-auto">
                    <select
                      className="appearance-none w-full pl-0 pr-8 py-2 border-0 border-b border-black/10 focus:outline-none focus:border-black text-xs uppercase tracking-wider bg-transparent"
                      value={activeFilter}
                      onChange={(e) => setActiveFilter(e.target.value)}
                    >
                      {jobCategories.map((category, index) => (
                        <option key={index} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                    <FiChevronDown className="absolute right-0 top-1/2 transform -translate-y-1/2 text-black/60 pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            {/* Job Listings - Zara Style */}
            <div className="space-y-0">
              {filteredJobs.length > 0 ? (
                filteredJobs.map((job, index) => (
                  <motion.div
                    key={index}
                    className="border-t border-black/5"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div
                      className="py-6 cursor-pointer"
                      onClick={() => toggleJobDetails(index)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-base font-light mb-1">{job.title}</h3>
                          <div className="flex flex-wrap gap-4 text-xs text-gray-500">
                            <span>{job.department}</span>
                            <span>{job.location}</span>
                            <span>{job.type}</span>
                          </div>
                        </div>
                        <div>
                          {expandedJob === index ? (
                            <FiChevronUp className="h-5 w-5 text-black" />
                          ) : (
                            <FiChevronDown className="h-5 w-5 text-black/60" />
                          )}
                        </div>
                      </div>
                    </div>

                    {expandedJob === index && (
                      <div className="pb-8 animate-fadeIn">
                        <p className="text-sm text-gray-600 mb-6">{job.description}</p>

                        <div className="mb-6">
                          <h4 className="text-xs uppercase tracking-wider text-gray-500 mb-3">Responsibilities</h4>
                          <div className="space-y-3">
                            {job.responsibilities.map((item, i) => (
                              <div key={i} className="flex items-start">
                                <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                                <p className="text-sm">{item}</p>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="mb-8">
                          <h4 className="text-xs uppercase tracking-wider text-gray-500 mb-3">Requirements</h4>
                          <div className="space-y-3">
                            {job.requirements.map((item, i) => (
                              <div key={i} className="flex items-start">
                                <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                                <p className="text-sm">{item}</p>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <a
                            href={`mailto:<EMAIL>?subject=Application for ${job.title}`}
                            className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                          >
                            Apply Now
                          </a>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))
              ) : (
                <div className="border-t border-black/5 py-8">
                  <p className="text-sm text-gray-600">No positions found matching your criteria.</p>
                  <button
                    className="mt-4 text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                    onClick={() => {
                      setSearchTerm('');
                      setActiveFilter('All');
                    }}
                  >
                    Clear filters
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Application Process Section - Zara Style */}
      <section className="py-12 md:py-16 bg-white border-t border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-12"
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Our Hiring Process</h2>
              <p className="text-sm text-gray-600">
                We've designed a straightforward process to help us find the right talent for our team.
              </p>
            </motion.div>

            <div className="space-y-0">
              <motion.div
                className="border-t border-black/5 pt-6 pb-8"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start">
                  <div className="mr-6 flex-shrink-0">
                    <div className="w-6 h-6 border border-black rounded-full flex items-center justify-center text-xs">
                      1
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-light mb-3">Application Review</h3>
                    <p className="text-sm text-gray-600">
                      Our recruitment team reviews your application and resume to assess your qualifications and experience.
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="border-t border-black/5 pt-6 pb-8"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start">
                  <div className="mr-6 flex-shrink-0">
                    <div className="w-6 h-6 border border-black rounded-full flex items-center justify-center text-xs">
                      2
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-light mb-3">Initial Interview</h3>
                    <p className="text-sm text-gray-600">
                      If selected, you'll have a phone or video interview with our HR team to discuss your background and the role.
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="border-t border-black/5 pt-6 pb-8"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start">
                  <div className="mr-6 flex-shrink-0">
                    <div className="w-6 h-6 border border-black rounded-full flex items-center justify-center text-xs">
                      3
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-light mb-3">Technical Assessment</h3>
                    <p className="text-sm text-gray-600">
                      Depending on the role, you may be asked to complete a skills assessment, design task, or case study.
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="border-t border-black/5 pt-6 pb-8"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start">
                  <div className="mr-6 flex-shrink-0">
                    <div className="w-6 h-6 border border-black rounded-full flex items-center justify-center text-xs">
                      4
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-light mb-3">Team Interview</h3>
                    <p className="text-sm text-gray-600">
                      Meet with the hiring manager and potential team members to discuss the role in detail and assess cultural fit.
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="border-t border-black/5 pt-6 pb-8"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <div className="flex items-start">
                  <div className="mr-6 flex-shrink-0">
                    <div className="w-6 h-6 border border-black rounded-full flex items-center justify-center text-xs">
                      5
                    </div>
                  </div>
                  <div>
                    <h3 className="text-base font-light mb-3">Offer & Onboarding</h3>
                    <p className="text-sm text-gray-600">
                      If selected, you'll receive an offer letter. Once accepted, our HR team will guide you through the onboarding process.
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section - Zara Style */}
      <section className="py-12 md:py-16 bg-white border-t border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Don't See a Perfect Match?</h2>
              <p className="text-sm text-gray-600 mb-8">
                We're always looking for talented individuals to join our team. Send us your resume, and we'll keep you in mind for future opportunities.
              </p>
              <div className="space-x-6">
                <a
                  href="mailto:<EMAIL>?subject=General Application"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                >
                  Send Your Resume
                </a>
                <Link
                  href="/"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black/50 pb-1 hover:opacity-70 transition-opacity"
                >
                  Return to Home
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}