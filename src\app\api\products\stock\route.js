import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Product from '@/models/Product';

// POST endpoint to check stock for multiple products
export async function POST(request) {
  try {
    // Parse request body
    const { items } = await request.json();

    if (!Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Expected array of items with _id and quantity.' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Check stock for each product
    const stockResults = [];
    let allValid = true;

    for (const item of items) {
      if (!item._id) {
        return NextResponse.json(
          { error: 'Invalid item. Missing _id.' },
          { status: 400 }
        );
      }

      try {
        const product = await Product.findById(item._id).select('name stock');

        if (!product) {
          stockResults.push({
            _id: item._id,
            name: item.name || 'Unknown Product',
            requested: item.quantity || 0,
            available: 0,
            valid: false,
            error: 'Product not found'
          });
          allValid = false;
          continue;
        }

        const isValid = product.stock >= (item.quantity || 0);

        stockResults.push({
          _id: item._id,
          name: product.name,
          requested: item.quantity || 0,
          available: product.stock,
          valid: isValid
        });

        if (!isValid) {
          allValid = false;
        }
      } catch (err) {
        console.error(`Error checking stock for product ${item._id}:`, err);
        stockResults.push({
          _id: item._id,
          name: item.name || 'Unknown Product',
          requested: item.quantity || 0,
          available: 0,
          valid: false,
          error: 'Error checking product'
        });
        allValid = false;
      }
    }

    return NextResponse.json({
      valid: allValid,
      items: stockResults
    });

  } catch (error) {
    console.error('Error checking stock:', error);
    return NextResponse.json(
      { error: 'Failed to check stock', details: error.message },
      { status: 500 }
    );
  }
}

