import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Newsletter from '@/models/Newsletter';

export async function POST(request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    await connectToDB();

    // Find the subscriber
    const subscriber = await Newsletter.findOne({ 
      email: email.toLowerCase() 
    });
    
    if (!subscriber) {
      return NextResponse.json(
        { message: 'Email not found in our newsletter list' },
        { status: 200 }
      );
    }

    // Update status to unsubscribed
    subscriber.status = 'unsubscribed';
    await subscriber.save();
    
    return NextResponse.json({
      message: 'You have been successfully unsubscribed from our newsletter'
    });
  } catch (error) {
    console.error('Error unsubscribing from newsletter:', error);
    return NextResponse.json(
      { error: 'Failed to process your request' },
      { status: 500 }
    );
  }
}
