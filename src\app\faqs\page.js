'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

export default function FAQsPage() {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleAccordion = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  // FAQ categories and questions
  const faqCategories = [
    {
      title: "Orders & Delivery",
      questions: [
        {
          question: "How do I track my order?",
          answer: "You can track your order by logging into your account and visiting the 'My Orders' section. Alternatively, you can use the tracking number provided in your order confirmation email to track your delivery on our website or the courier's website."
        },
        {
          question: "Do you offer free delivery?",
          answer: "Yes, we offer free delivery for orders above ₹10,000 within city limits. For orders below this amount or for delivery outside city limits, a nominal delivery fee will be charged based on the distance."
        },
        {
          question: "How long does delivery take?",
          answer: "For in-stock items, delivery typically takes 3-5 business days. For custom orders or items that need to be manufactured, the delivery time can range from 2-4 weeks depending on the complexity of the item."
        },
        {
          question: "Do you deliver to my area?",
          answer: "We deliver to most locations across India. During checkout, you can enter your pincode to check if delivery is available in your area and to get an estimated delivery time."
        }
      ]
    },
    {
      title: "Products & Customization",
      questions: [
        {
          question: "Can I customize my furniture?",
          answer: "Yes, we offer customization options for many of our furniture pieces. You can choose from different fabrics, colors, and sometimes dimensions. Look for the 'Customize' option on product pages or contact our customer service for more information."
        },
        {
          question: "What materials do you use in your furniture?",
          answer: "We use a variety of high-quality materials including solid wood (teak, sheesham, mango), engineered wood, metal, glass, and premium fabrics. Each product page specifies the materials used for that particular item."
        },
        {
          question: "How do I care for my furniture?",
          answer: "Care instructions vary depending on the material. Generally, wooden furniture should be kept away from direct sunlight and moisture, and cleaned with a soft, dry cloth. Specific care instructions are included with each product and can also be found on the product pages."
        },
        {
          question: "Do you offer assembly services?",
          answer: "Yes, we provide professional assembly services for all our furniture. Our trained technicians will assemble your furniture at your home at a time convenient for you. This service is complimentary for orders above ₹20,000."
        }
      ]
    },
    {
      title: "Payment & Pricing",
      questions: [
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit and debit cards, UPI payments, net banking, and cash on delivery. For orders above ₹50,000, we also offer EMI options through select banks."
        },
        {
          question: "Do you offer discounts or promotions?",
          answer: "Yes, we regularly run promotions and seasonal sales. You can sign up for our newsletter to stay updated on our latest offers. We also have a loyalty program that offers exclusive discounts to returning customers."
        },
        {
          question: "Is the price inclusive of taxes?",
          answer: "Yes, all prices displayed on our website are inclusive of GST and other applicable taxes."
        },
        {
          question: "Do you offer price matching?",
          answer: "We strive to offer competitive pricing. If you find the same product at a lower price from another authorized retailer, please contact our customer service, and we'll do our best to match the price."
        }
      ]
    },
    {
      title: "Returns & Warranty",
      questions: [
        {
          question: "What is your return policy?",
          answer: "We offer a 7-day return policy for most items. If you're not satisfied with your purchase, you can return it within 7 days in its original condition and packaging. Custom-made furniture items cannot be returned unless they have manufacturing defects."
        },
        {
          question: "How do I initiate a return?",
          answer: "To initiate a return, log into your account and go to 'My Orders'. Select the order you wish to return and follow the return instructions. Alternatively, you can contact our customer service for assistance."
        },
        {
          question: "What warranty do you offer?",
          answer: "We offer a 1-year warranty against manufacturing defects for all our furniture. Some premium products come with extended warranties of up to 5 years. Warranty details are specified on each product page."
        },
        {
          question: "What if my furniture arrives damaged?",
          answer: "If your furniture arrives damaged, please take photos of the damage and contact our customer service within 48 hours of delivery. We'll arrange for a replacement or repair as soon as possible."
        }
      ]
    },
    {
      title: "Account & Privacy",
      questions: [
        {
          question: "How do I create an account?",
          answer: "You can create an account by clicking on the 'Sign Up' button in the top right corner of our website. You'll need to provide your name, email address, and create a password."
        },
        {
          question: "How do you protect my personal information?",
          answer: "We take data protection seriously and comply with all applicable privacy laws. Your personal information is encrypted and stored securely. For more details, please refer to our Privacy Policy."
        },
        {
          question: "Can I shop without creating an account?",
          answer: "Yes, we offer a guest checkout option. However, creating an account allows you to track orders, save your favorite products, and enjoy a faster checkout process for future purchases."
        },
        {
          question: "How do I reset my password?",
          answer: "If you've forgotten your password, click on the 'Login' button, then select 'Forgot Password'. Enter your email address, and we'll send you instructions to reset your password."
        }
      ]
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-b from-amber-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Frequently Asked Questions
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-600 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Find answers to common questions about our products, services, and policies.
            </motion.p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className="mb-12">
                <motion.h2 
                  className="text-2xl font-bold text-gray-900 mb-6"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  {category.title}
                </motion.h2>
                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => {
                    const index = categoryIndex * 100 + faqIndex;
                    return (
                      <motion.div 
                        key={index}
                        className="border border-gray-200 rounded-lg overflow-hidden"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: faqIndex * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <button
                          className="flex justify-between items-center w-full px-6 py-4 text-left bg-white hover:bg-gray-50 transition-colors duration-200"
                          onClick={() => toggleAccordion(index)}
                        >
                          <span className="font-medium text-gray-900">{faq.question}</span>
                          {activeIndex === index ? (
                            <FiChevronUp className="h-5 w-5 text-amber-600" />
                          ) : (
                            <FiChevronDown className="h-5 w-5 text-gray-500" />
                          )}
                        </button>
                        {activeIndex === index && (
                          <div className="px-6 py-4 bg-gray-50">
                            <p className="text-gray-600">{faq.answer}</p>
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            ))}

            {/* Still have questions section */}
            <motion.div 
              className="mt-16 bg-amber-50 rounded-xl p-8 text-center"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Still have questions?</h3>
              <p className="text-gray-600 mb-6">
                Our customer support team is here to help. Feel free to reach out to us.
              </p>
              <Link 
                href="/contact" 
                className="inline-flex items-center px-6 py-3 bg-amber-600 text-white font-medium rounded-md hover:bg-amber-700 transition duration-300"
              >
                Contact Us
              </Link>
            </motion.div>

            <div className="mt-12 text-center">
              <Link 
                href="/" 
                className="inline-flex items-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-50 font-medium rounded-md transition duration-300"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 