'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import {
  FiSearch,
  FiFilter,
  FiTrash2,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiArrowLeft,
  FiMail,
  FiSend
} from 'react-icons/fi';
import Pagination from '@/components/admin/Pagination';

export default function NewsletterManagement() {
  const router = useRouter();
  const [subscribers, setSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedSubscribers, setSelectedSubscribers] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showComposeModal, setShowComposeModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortConfig, setSortConfig] = useState({
    field: 'subscribedAt',
    direction: 'desc'
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    unsubscribed: 0,
    recent: 0,
    today: 0
  });
  const [newsletterData, setNewsletterData] = useState({
    subject: '',
    content: '',
    testEmail: ''
  });
  const [sendingNewsletter, setSendingNewsletter] = useState(false);
  const [testMode, setTestMode] = useState(false);

  useEffect(() => {
    fetchSubscribers();
  }, [currentPage, sortConfig, statusFilter]);

  const fetchSubscribers = async () => {
    try {
      setLoading(true);
      // Add a cache-busting parameter to ensure we get fresh data
      const timestamp = new Date().getTime();
      let url = `/api/admin/newsletter?page=${currentPage}&sort=${sortConfig.field}&direction=${sortConfig.direction}&includeStats=true&_=${timestamp}`;

      if (statusFilter !== 'all') {
        url += `&status=${statusFilter}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch subscribers');
      }

      const data = await response.json();
      setSubscribers(data.subscribers);
      setTotalPages(data.pages);

      // Update statistics if available
      if (data.stats) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching subscribers:', error);
      toast.error(error.message || 'Failed to load subscribers');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field) => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'desc' ? 'asc' : 'desc'
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    fetchSubscribersWithSearch();
  };

  const fetchSubscribersWithSearch = async () => {
    try {
      setLoading(true);
      const timestamp = new Date().getTime();
      let url = `/api/admin/newsletter?page=1&sort=${sortConfig.field}&direction=${sortConfig.direction}&includeStats=true&_=${timestamp}`;

      if (statusFilter !== 'all') {
        url += `&status=${statusFilter}`;
      }

      if (searchQuery.trim()) {
        url += `&search=${encodeURIComponent(searchQuery.trim())}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch subscribers');
      }

      const data = await response.json();
      setSubscribers(data.subscribers);
      setTotalPages(data.pages);
      setCurrentPage(1);

      // Update statistics if available
      if (data.stats) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error searching subscribers:', error);
      toast.error(error.message || 'Failed to search subscribers');
    } finally {
      setLoading(false);
    }
  };

  const toggleSubscriberSelection = (id) => {
    setSelectedSubscribers(prev =>
      prev.includes(id)
        ? prev.filter(subId => subId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedSubscribers.length === subscribers.length) {
      setSelectedSubscribers([]);
    } else {
      setSelectedSubscribers(subscribers.map(sub => sub._id));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedSubscribers.length === 0) return;

    try {
      const response = await fetch('/api/admin/newsletter', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids: selectedSubscribers }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete subscribers');
      }

      toast.success('Subscribers deleted successfully');
      setSelectedSubscribers([]);
      fetchSubscribers();
    } catch (error) {
      console.error('Error deleting subscribers:', error);
      toast.error(error.message || 'Failed to delete subscribers');
    } finally {
      setShowDeleteModal(false);
    }
  };

  const handleUpdateStatus = async (status, ids = null) => {
    const subscribersToUpdate = ids || selectedSubscribers;
    if (subscribersToUpdate.length === 0) return;

    try {
      const response = await fetch('/api/admin/newsletter', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: subscribersToUpdate,
          status
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update subscribers');
      }

      toast.success(`Subscribers marked as ${status}`);
      setSelectedSubscribers([]);
      fetchSubscribers();
    } catch (error) {
      console.error('Error updating subscribers:', error);
      toast.error(error.message || 'Failed to update subscribers');
    }
  };

  const handleNewsletterInputChange = (e) => {
    const { name, value } = e.target;
    setNewsletterData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSendNewsletter = async (e) => {
    e.preventDefault();

    // Validate inputs
    if (!newsletterData.subject.trim()) {
      toast.error('Please enter a subject for the newsletter');
      return;
    }

    if (!newsletterData.content.trim()) {
      toast.error('Please enter content for the newsletter');
      return;
    }

    // If in test mode, validate test email
    if (testMode && !newsletterData.testEmail.trim()) {
      toast.error('Please enter a test email address');
      return;
    }

    // Confirm before sending to all subscribers
    if (!testMode) {
      if (!window.confirm(`Are you sure you want to send this newsletter to all active subscribers (${stats.active})? This action cannot be undone.`)) {
        return;
      }
    }

    setSendingNewsletter(true);

    try {
      const response = await fetch('/api/admin/newsletter/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: newsletterData.subject,
          content: newsletterData.content,
          testEmail: testMode ? newsletterData.testEmail : null
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send newsletter');
      }

      toast.success(testMode
        ? 'Test newsletter sent successfully!'
        : `Newsletter sent successfully to ${data.recipients} subscribers!`);

      if (!testMode) {
        // Reset form after successful send to all subscribers
        setNewsletterData({
          subject: '',
          content: '',
          testEmail: ''
        });
        setShowComposeModal(false);
      }
    } catch (error) {
      console.error('Error sending newsletter:', error);
      toast.error(error.message || 'Failed to send newsletter');
    } finally {
      setSendingNewsletter(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-4">
          <button
            onClick={() => router.push('/admin')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO ADMIN
          </button>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xs tracking-[0.2em] text-gray-400">NEWSLETTER SUBSCRIBERS</h1>
          <div className="flex space-x-4">
            <button
              onClick={() => setShowComposeModal(true)}
              className="bg-blue-600 text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
            >
              <FiMail className="mr-2" />
              COMPOSE NEWSLETTER
            </button>
            <button
              onClick={() => fetchSubscribers()}
              className="bg-black text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
            >
              <FiRefreshCw className="mr-2" />
              REFRESH
            </button>
          </div>
        </div>

        {/* Newsletter Statistics */}
        <div className="grid grid-cols-5 gap-4 mb-6">
          <div className="p-4 border border-gray-200 bg-gray-50">
            <p className="text-sm">TOTAL SUBSCRIBERS</p>
            <p className="text-2xl font-medium">{stats.total}</p>
          </div>
          <div className="p-4 border border-green-100 bg-green-50">
            <p className="text-sm text-green-700">ACTIVE</p>
            <p className="text-2xl font-medium text-green-700">{stats.active}</p>
          </div>
          <div className="p-4 border border-yellow-100 bg-yellow-50">
            <p className="text-sm text-yellow-700">UNSUBSCRIBED</p>
            <p className="text-2xl font-medium text-yellow-700">{stats.unsubscribed}</p>
          </div>
          <div className="p-4 border border-blue-100 bg-blue-50">
            <p className="text-sm text-blue-700">LAST 7 DAYS</p>
            <p className="text-2xl font-medium text-blue-700">{stats.recent}</p>
          </div>
          <div className="p-4 border border-purple-100 bg-purple-50">
            <p className="text-sm text-purple-700">TODAY</p>
            <p className="text-2xl font-medium text-purple-700">{stats.today}</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 bg-white border border-black/10">
          <div className="p-4">
            <form onSubmit={handleSearch} className="flex items-center">
              <div className="relative flex-grow">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-transparent border-none focus:ring-0 text-sm"
                />
              </div>
              <button
                type="submit"
                className="ml-2 px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
              >
                SEARCH
              </button>
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="ml-2 p-2 border border-gray-200 hover:bg-gray-50"
                title="Filter"
              >
                <FiFilter size={16} />
              </button>
            </form>

            {showFilters && (
              <div className="mt-4 p-4 border-t border-gray-100">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded text-sm"
                    >
                      <option value="all">All Subscribers</option>
                      <option value="active">Active Only</option>
                      <option value="unsubscribed">Unsubscribed Only</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sort By
                    </label>
                    <select
                      value={`${sortConfig.field}-${sortConfig.direction}`}
                      onChange={(e) => {
                        const [field, direction] = e.target.value.split('-');
                        setSortConfig({ field, direction });
                      }}
                      className="w-full p-2 border border-gray-300 rounded text-sm"
                    >
                      <option value="subscribedAt-desc">Date (Newest First)</option>
                      <option value="subscribedAt-asc">Date (Oldest First)</option>
                      <option value="email-asc">Email (A-Z)</option>
                      <option value="email-desc">Email (Z-A)</option>
                      <option value="status-asc">Status (Active First)</option>
                      <option value="status-desc">Status (Unsubscribed First)</option>
                    </select>
                  </div>
                </div>
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
                      setShowFilters(false);
                      fetchSubscribers();
                    }}
                    className="px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
                  >
                    APPLY FILTERS
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Subscribers Table */}
        <div className="overflow-x-auto bg-white border border-black/10">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedSubscribers.length === subscribers.length && subscribers.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 border-gray-300 rounded"
                  />
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('email')}
                >
                  Email
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('subscribedAt')}
                >
                  Subscribed On
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading subscribers...
                  </td>
                </tr>
              ) : subscribers.length === 0 ? (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-center text-sm text-gray-500">
                    No subscribers found
                  </td>
                </tr>
              ) : (
                subscribers.map((subscriber) => (
                  <tr key={subscriber._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedSubscribers.includes(subscriber._id)}
                        onChange={() => toggleSubscriberSelection(subscriber._id)}
                        className="h-4 w-4 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {subscriber.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(subscriber.subscribedAt), 'dd MMM yyyy, HH:mm')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        subscriber.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {subscriber.status === 'active' ? 'Active' : 'Unsubscribed'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        {subscriber.status === 'unsubscribed' ? (
                          <button
                            onClick={() => handleUpdateStatus('active', [subscriber._id])}
                            className="text-green-600 hover:text-green-900"
                            title="Mark as Active"
                          >
                            <FiCheck size={18} />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleUpdateStatus('unsubscribed', [subscriber._id])}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Mark as Unsubscribed"
                          >
                            <FiX size={18} />
                          </button>
                        )}
                        <button
                          onClick={() => {
                            setSelectedSubscribers([subscriber._id]);
                            setShowDeleteModal(true);
                          }}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <FiTrash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>

        {/* Bulk Actions */}
        {selectedSubscribers.length > 0 && (
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-20">
            <div className="container mx-auto flex items-center justify-between">
              <span className="text-xs tracking-[0.2em]">
                {selectedSubscribers.length} SUBSCRIBERS SELECTED
              </span>
              <div className="flex gap-4">
                <button
                  onClick={() => handleUpdateStatus('active')}
                  className="px-4 py-2 text-xs tracking-[0.2em] bg-green-600 text-white hover:bg-green-700 flex items-center"
                >
                  <FiCheck className="mr-2" /> MARK ACTIVE
                </button>
                <button
                  onClick={() => handleUpdateStatus('unsubscribed')}
                  className="px-4 py-2 text-xs tracking-[0.2em] bg-yellow-600 text-white hover:bg-yellow-700 flex items-center"
                >
                  <FiX className="mr-2" /> MARK UNSUBSCRIBED
                </button>
                <button
                  onClick={() => setShowDeleteModal(true)}
                  className="px-4 py-2 text-xs tracking-[0.2em] bg-red-600 text-white hover:bg-red-700 flex items-center"
                >
                  <FiTrash2 className="mr-2" /> DELETE
                </button>
                <button
                  onClick={() => setSelectedSubscribers([])}
                  className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50"
                >
                  CANCEL
                </button>
              </div>
            </div>
          </div>
        )}

      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
          <div className="bg-white w-full max-w-md rounded-lg overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xs tracking-[0.2em] text-gray-400">CONFIRM DELETION</h2>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="text-xs tracking-[0.2em] hover:opacity-50"
                >
                  CLOSE
                </button>
              </div>

              <div className="mb-6">
                <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mx-auto mb-4">
                  <FiTrash2 className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-center text-sm mb-2">
                  Are you sure you want to delete {selectedSubscribers.length} subscriber(s)?
                </p>
                <p className="text-center text-xs text-gray-500">
                  This action cannot be undone.
                </p>
              </div>

              <div className="flex justify-end gap-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50"
                >
                  CANCEL
                </button>
                <button
                  onClick={handleDeleteSelected}
                  className="px-4 py-2 text-xs tracking-[0.2em] bg-red-600 text-white hover:bg-red-700"
                >
                  DELETE
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Newsletter Composer Modal */}
      {showComposeModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
          <div className="bg-white w-full max-w-2xl rounded-lg overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xs tracking-[0.2em] text-gray-400">COMPOSE NEWSLETTER</h2>
                <button
                  onClick={() => setShowComposeModal(false)}
                  className="text-xs tracking-[0.2em] hover:opacity-50"
                >
                  CLOSE
                </button>
              </div>

              <form onSubmit={handleSendNewsletter} className="space-y-6">
                <div>
                  <label htmlFor="subject" className="block text-xs font-medium text-gray-700 mb-1">
                    Subject Line
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={newsletterData.subject}
                    onChange={handleNewsletterInputChange}
                    className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                    placeholder="Enter newsletter subject"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="content" className="block text-xs font-medium text-gray-700 mb-1">
                    Content (HTML supported)
                  </label>
                  <textarea
                    id="content"
                    name="content"
                    value={newsletterData.content}
                    onChange={handleNewsletterInputChange}
                    rows={10}
                    className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black font-mono text-sm"
                    placeholder="Enter newsletter content (HTML supported)"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    You can use HTML tags for formatting. Images should be hosted externally and referenced by URL.
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="testMode"
                    checked={testMode}
                    onChange={() => setTestMode(!testMode)}
                    className="h-4 w-4 border-gray-300 rounded"
                  />
                  <label htmlFor="testMode" className="ml-2 block text-sm text-gray-700">
                    Send test email first
                  </label>
                </div>

                {testMode && (
                  <div>
                    <label htmlFor="testEmail" className="block text-xs font-medium text-gray-700 mb-1">
                      Test Email Address
                    </label>
                    <input
                      type="email"
                      id="testEmail"
                      name="testEmail"
                      value={newsletterData.testEmail}
                      onChange={handleNewsletterInputChange}
                      className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                      placeholder="Enter email address for testing"
                    />
                  </div>
                )}

                <div className="flex justify-end gap-4 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setShowComposeModal(false)}
                    className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50"
                  >
                    CANCEL
                  </button>
                  <button
                    type="submit"
                    disabled={sendingNewsletter}
                    className="px-4 py-2 text-xs tracking-[0.2em] bg-blue-600 text-white hover:bg-blue-700 flex items-center"
                  >
                    <FiSend className="mr-2" />
                    {sendingNewsletter
                      ? 'SENDING...'
                      : testMode
                        ? 'SEND TEST'
                        : 'SEND TO ALL SUBSCRIBERS'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
