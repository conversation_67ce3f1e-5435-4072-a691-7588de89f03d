import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Coupon from '@/models/Coupon';
import { getSessionUser } from '@/lib/auth';
import mongoose from 'mongoose';

// Validate a coupon code
export async function POST(request) {
  try {
    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { code, subtotal } = body;

    if (!code) {
      return NextResponse.json(
        { error: 'Coupon code is required' },
        { status: 400 }
      );
    }

    if (!subtotal || subtotal <= 0) {
      return NextResponse.json(
        { error: 'Valid subtotal amount is required' },
        { status: 400 }
      );
    }

    // Check if Coupon model exists
    if (!mongoose.models.Coupon) {
      return NextResponse.json(
        { error: 'Coupon system not available' },
        { status: 404 }
      );
    }

    // Find coupon
    const coupon = await Coupon.findOne({ code: code.toUpperCase() });

    if (!coupon) {
      return NextResponse.json(
        { error: 'Invalid coupon code' },
        { status: 404 }
      );
    }

    // Check if coupon is valid
    const validationResult = coupon.isValid(user._id, subtotal);
    if (!validationResult.valid) {
      return NextResponse.json(
        { error: validationResult.message },
        { status: 400 }
      );
    }

    // Calculate discount amount
    const discountAmount = coupon.calculateDiscount(subtotal);

    // Return coupon details
    return NextResponse.json({
      code: coupon.code,
      type: coupon.type,
      value: coupon.value,
      discountAmount,
      minPurchase: coupon.minPurchase,
      expiryDate: coupon.expiryDate,
      description: coupon.description
    });
  } catch (error) {
    console.error('Error validating coupon:', error);
    return NextResponse.json(
      { error: 'Failed to validate coupon' },
      { status: 500 }
    );
  }
}
