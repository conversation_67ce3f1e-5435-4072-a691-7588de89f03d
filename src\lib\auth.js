import { cookies } from 'next/headers';
import User from '@/models/User';
import { connectToDB } from './mongodb';

export async function isAdmin() {
  try {
    const cookieStore = await cookies();
    const sessionId = (await cookieStore.get('session'))?.value;
    if (!sessionId) return false;

    await connectToDB();
    const user = await User.findById(sessionId);
    
    return user?.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

export async function getSessionUser() {
  try {
    const cookieStore = await cookies();
    const sessionId = (await cookieStore.get('session'))?.value;
    if (!sessionId) return null;

    await connectToDB();
    const user = await User.findById(sessionId).select('-password');
    
    return user;
  } catch (error) {
    console.error('Error getting session user:', error);
    return null;
  }
}