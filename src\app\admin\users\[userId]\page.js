'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import {
  FiArrowLeft,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiShoppingBag,
  FiHeart,
  FiClock,
  FiEdit,
  FiAlertCircle,
  FiCheckCircle,
  FiXCircle,
  FiDollarSign,
  FiPackage,
  FiTruck,
  FiInbox
} from 'react-icons/fi';

export default function UserProfilePage({ params }) {
  const resolvedParams = use(params);
  const userId = resolvedParams.userId;

  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState(null);
  const [orderStats, setOrderStats] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0,
    cancelled: 0
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [error, setError] = useState(null);
  const [orders, setOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersPagination, setOrdersPagination] = useState({
    page: 1,
    pageSize: 10,
    totalPages: 1,
    totalItems: 0
  });
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }

    fetchUserProfile();
  }, [user, userId, router]);

  // Fetch orders when tab changes to orders or when pagination/filter changes
  useEffect(() => {
    if (activeTab === 'orders' && userProfile) {
      fetchOrders();
    }
  }, [activeTab, ordersPagination.page, statusFilter, userProfile]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        } else {
          throw new Error('Failed to fetch user profile');
        }
      }

      const data = await response.json();
      setUserProfile(data);

      // Fetch user's order stats
      fetchOrderStats();
      // Fetch user's recent orders
      fetchRecentOrders();
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrderStats = async () => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/orders/stats`);

      if (response.ok) {
        const data = await response.json();
        setOrderStats(data);
      }
    } catch (error) {
      console.error('Error fetching order stats:', error);
    }
  };

  const fetchRecentOrders = async () => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/orders?limit=5`);

      if (response.ok) {
        const data = await response.json();
        setRecentOrders(data);
      }
    } catch (error) {
      console.error('Error fetching recent orders:', error);
    }
  };

  const fetchOrders = async () => {
    try {
      setOrdersLoading(true);
      const { page, pageSize } = ordersPagination;
      const statusParam = statusFilter !== 'all' ? `&status=${statusFilter}` : '';

      const response = await fetch(
        `/api/admin/users/${userId}/orders?page=${page}&pageSize=${pageSize}${statusParam}`
      );

      if (response.ok) {
        const data = await response.json();
        setOrders(data);

        // Update pagination from headers
        const totalItems = parseInt(response.headers.get('X-Total-Count') || '0');
        const totalPages = parseInt(response.headers.get('X-Total-Pages') || '1');

        setOrdersPagination(prev => ({
          ...prev,
          totalItems,
          totalPages
        }));
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to load orders');
    } finally {
      setOrdersLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setOrdersPagination(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatPrice = (price) => {
    return `₹${Number(price).toLocaleString('en-IN')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-sm text-gray-500">Loading user profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <h1 className="mt-4 text-xl font-medium text-gray-900">Error</h1>
          <p className="mt-2 text-sm text-gray-500">{error}</p>
          <button
            onClick={() => router.push('/admin/users')}
            className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <h1 className="mt-4 text-xl font-medium text-gray-900">User Not Found</h1>
          <p className="mt-2 text-sm text-gray-500">The user you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => router.push('/admin/users')}
            className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none"
          >
            Back to Users
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/admin/users')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO USERS
          </button>
        </div>

        {/* User Header */}
        <div className="mb-8 md:mb-12 flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-xl md:text-2xl font-light tracking-wide">{userProfile.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <span className={`px-2 py-0.5 text-xs rounded-full ${
                userProfile.role === 'admin' ? 'bg-black text-white' : 'bg-gray-100 text-gray-800'
              }`}>
                {userProfile.role.toUpperCase()}
              </span>
              <span className={`px-2 py-0.5 text-xs rounded-full ${
                userProfile.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {userProfile.status.toUpperCase()}
              </span>
              {userProfile.isSubscribedToNewsletter && (
                <span className="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800">
                  NEWSLETTER
                </span>
              )}
            </div>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <button
              onClick={() => router.push(`/admin/users/${userProfile._id}/edit`)}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              <FiEdit className="mr-1.5" />
              Edit User
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-black text-black'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'orders'
                  ? 'border-black text-black'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Orders
            </button>
            <button
              onClick={() => setActiveTab('activity')}
              className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'activity'
                  ? 'border-black text-black'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Activity
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - User Information */}
            <div className="lg:col-span-2 space-y-8">
              {/* Personal Information */}
              <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-sm font-medium text-gray-900">Personal Information</h2>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-start gap-3">
                      <FiUser className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Full Name</p>
                        <p className="text-sm font-medium">{userProfile.name}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <FiMail className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="text-sm font-medium">{userProfile.email}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {userProfile.emailVerified ? (
                            <span className="flex items-center text-green-600">
                              <FiCheckCircle className="mr-1" /> Verified
                            </span>
                          ) : (
                            <span className="flex items-center text-yellow-600">
                              <FiAlertCircle className="mr-1" /> Not verified
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <FiPhone className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="text-sm font-medium">{userProfile.phone || 'Not provided'}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <FiCalendar className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Member Since</p>
                        <p className="text-sm font-medium">{formatDate(userProfile.createdAt)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-sm font-medium text-gray-900">Address Information</h2>
                </div>
                <div className="p-6">
                  <div className="flex items-start gap-3">
                    <FiMapPin className="text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-500">Address</p>
                      <p className="text-sm font-medium whitespace-pre-line">
                        {userProfile.address || 'No address provided'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                  <h2 className="text-sm font-medium text-gray-900">Recent Orders</h2>
                  <button
                    onClick={() => setActiveTab('orders')}
                    className="text-xs text-black hover:underline"
                  >
                    View All
                  </button>
                </div>
                <div className="divide-y divide-gray-200">
                  {recentOrders.length === 0 ? (
                    <div className="p-6 text-center text-sm text-gray-500">
                      No orders found for this user.
                    </div>
                  ) : (
                    recentOrders.map((order) => (
                      <div key={order._id} className="p-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                          <div>
                            <Link
                              href={`/admin/orders/${order._id}`}
                              className="text-sm font-medium hover:underline"
                            >
                              Order #{order.orderNumber || order._id.slice(-6)}
                            </Link>
                            <p className="text-xs text-gray-500 mt-1">{formatDate(order.createdAt)}</p>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                              order.status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
                              order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {order.status.toUpperCase()}
                            </span>
                            <span className="text-sm font-medium">{formatPrice(order.total)}</span>
                          </div>
                        </div>
                        <div className="mt-4 text-xs text-gray-500">
                          {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Right Column - Stats & Activity */}
            <div className="space-y-8">
              {/* Order Statistics */}
              <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-sm font-medium text-gray-900">Order Statistics</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiShoppingBag className="text-gray-400" />
                        <span className="text-sm text-gray-500">Total Orders</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.total}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiClock className="text-yellow-500" />
                        <span className="text-sm text-gray-500">Pending</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.pending}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiPackage className="text-blue-500" />
                        <span className="text-sm text-gray-500">Processing</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.processing}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiTruck className="text-indigo-500" />
                        <span className="text-sm text-gray-500">Shipped</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.shipped}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiCheckCircle className="text-green-500" />
                        <span className="text-sm text-gray-500">Delivered</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.delivered}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <FiXCircle className="text-red-500" />
                        <span className="text-sm text-gray-500">Cancelled</span>
                      </div>
                      <span className="text-sm font-medium">{orderStats.cancelled}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-sm font-medium text-gray-900">Account Information</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Account Status</span>
                      <span className={`px-2 py-0.5 text-xs rounded-full ${
                        userProfile.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {userProfile.status.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Role</span>
                      <span className={`px-2 py-0.5 text-xs rounded-full ${
                        userProfile.role === 'admin' ? 'bg-black text-white' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {userProfile.role.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Email Verified</span>
                      <span className={`flex items-center text-xs ${
                        userProfile.emailVerified ? 'text-green-600' : 'text-yellow-600'
                      }`}>
                        {userProfile.emailVerified ? (
                          <>
                            <FiCheckCircle className="mr-1" /> Yes
                          </>
                        ) : (
                          <>
                            <FiAlertCircle className="mr-1" /> No
                          </>
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Newsletter</span>
                      <span className={`flex items-center text-xs ${
                        userProfile.isSubscribedToNewsletter ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {userProfile.isSubscribedToNewsletter ? (
                          <>
                            <FiCheckCircle className="mr-1" /> Subscribed
                          </>
                        ) : (
                          <>
                            <FiXCircle className="mr-1" /> Not Subscribed
                          </>
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Last Updated</span>
                      <span className="text-xs text-gray-500">{formatDate(userProfile.updatedAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h2 className="text-sm font-medium text-gray-900">Order History</h2>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">Filter:</span>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="text-xs border-gray-200 rounded-sm py-1"
                >
                  <option value="all">All Orders</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="delivered">Delivered</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
            </div>

            {ordersLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">Loading orders...</p>
              </div>
            ) : orders.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-sm text-gray-500">No orders found.</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Order ID
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Items
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Payment
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {orders.map((order) => (
                        <tr key={order._id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {order.orderNumber || order._id.slice(-6)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(order.createdAt)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {formatPrice(order.total)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                              order.status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
                              order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {order.status.toUpperCase()}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              order.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :
                              order.paymentStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {order.paymentStatus?.toUpperCase() || 'N/A'}
                            </span>
                            <span className="ml-2 text-xs">
                              {order.paymentMethod === 'cod' ? 'COD' :
                               order.paymentMethod === 'upi' ? 'UPI' :
                               order.paymentMethod?.toUpperCase() || 'N/A'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link
                              href={`/admin/orders/${order._id}`}
                              className="text-black hover:underline"
                            >
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {ordersPagination.totalPages > 1 && (
                  <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      Showing {((ordersPagination.page - 1) * ordersPagination.pageSize) + 1} to {
                        Math.min(ordersPagination.page * ordersPagination.pageSize, ordersPagination.totalItems)
                      } of {ordersPagination.totalItems} orders
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(ordersPagination.page - 1)}
                        disabled={ordersPagination.page === 1}
                        className="px-3 py-1 border border-gray-300 rounded-sm text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      {Array.from({ length: ordersPagination.totalPages }, (_, i) => i + 1)
                        .filter(page =>
                          page === 1 ||
                          page === ordersPagination.totalPages ||
                          Math.abs(page - ordersPagination.page) <= 1
                        )
                        .map((page, index, array) => {
                          // Add ellipsis
                          if (index > 0 && page - array[index - 1] > 1) {
                            return (
                              <span key={`ellipsis-${page}`} className="px-3 py-1">
                                ...
                              </span>
                            );
                          }

                          return (
                            <button
                              key={page}
                              onClick={() => handlePageChange(page)}
                              className={`px-3 py-1 border rounded-sm text-sm ${
                                ordersPagination.page === page
                                  ? 'bg-black text-white border-black'
                                  : 'border-gray-300 hover:bg-gray-50'
                              }`}
                            >
                              {page}
                            </button>
                          );
                        })
                      }
                      <button
                        onClick={() => handlePageChange(ordersPagination.page + 1)}
                        disabled={ordersPagination.page === ordersPagination.totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-sm text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Order Activity */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order Activity</h2>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Order Summary</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-sm">
                        <p className="text-xs text-gray-500">Total Orders</p>
                        <p className="text-xl font-medium">{orderStats.total}</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-sm">
                        <p className="text-xs text-gray-500">Total Spent</p>
                        <p className="text-xl font-medium">{formatPrice(orderStats.totalSpent || 0)}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Recent Activity</h3>
                    {recentOrders.length === 0 ? (
                      <p className="text-sm text-gray-500">No recent orders.</p>
                    ) : (
                      <div className="space-y-4">
                        {recentOrders.slice(0, 3).map(order => (
                          <div key={order._id} className="bg-gray-50 p-4 rounded-sm">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium">
                                  Placed order #{order.orderNumber || order._id.slice(-6)}
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  {formatDate(order.createdAt)}
                                </p>
                              </div>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                                order.status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
                                order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {order.status.toUpperCase()}
                              </span>
                            </div>
                            <p className="text-sm mt-2">
                              {formatPrice(order.total)} • {order.items.length} {order.items.length === 1 ? 'item' : 'items'}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Account Activity */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Account Activity</h2>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Account Timeline</h3>
                    <div className="relative">
                      {/* Timeline line */}
                      <div className="absolute left-2.5 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                      <div className="space-y-6 relative">
                        {/* Account Created */}
                        <div className="ml-10 relative">
                          <div className="absolute -left-10 mt-1.5 w-5 h-5 rounded-full bg-green-400 border-2 border-white"></div>
                          <div>
                            <p className="text-sm font-medium">Account Created</p>
                            <p className="text-xs text-gray-500 mt-1">{formatDate(userProfile.createdAt)}</p>
                          </div>
                        </div>

                        {/* Email Verification */}
                        <div className="ml-10 relative">
                          <div className={`absolute -left-10 mt-1.5 w-5 h-5 rounded-full border-2 border-white ${userProfile.emailVerified ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                          <div>
                            <p className="text-sm font-medium">Email Verification</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {userProfile.emailVerified ? 'Verified' : 'Not verified'}
                            </p>
                          </div>
                        </div>

                        {/* Newsletter Subscription */}
                        <div className="ml-10 relative">
                          <div className={`absolute -left-10 mt-1.5 w-5 h-5 rounded-full border-2 border-white ${userProfile.isSubscribedToNewsletter ? 'bg-blue-400' : 'bg-gray-300'}`}></div>
                          <div>
                            <p className="text-sm font-medium">Newsletter Subscription</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {userProfile.isSubscribedToNewsletter ? 'Subscribed' : 'Not subscribed'}
                            </p>
                          </div>
                        </div>

                        {/* Last Updated */}
                        <div className="ml-10 relative">
                          <div className="absolute -left-10 mt-1.5 w-5 h-5 rounded-full bg-gray-400 border-2 border-white"></div>
                          <div>
                            <p className="text-sm font-medium">Last Profile Update</p>
                            <p className="text-xs text-gray-500 mt-1">{formatDate(userProfile.updatedAt)}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Wishlist & Cart */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Wishlist & Cart</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FiHeart className="text-red-500" />
                      <span className="text-sm text-gray-500">Wishlist Items</span>
                    </div>
                    <span className="text-sm font-medium">{userProfile.wishlist?.length || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FiShoppingBag className="text-blue-500" />
                      <span className="text-sm text-gray-500">Cart Items</span>
                    </div>
                    <span className="text-sm font-medium">{userProfile.cart?.length || 0}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Preferences */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Notification Preferences</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FiPackage className="text-indigo-500" />
                      <span className="text-sm text-gray-500">Order Updates</span>
                    </div>
                    <span className={`flex items-center text-xs ${
                      userProfile.notifications?.orderUpdates ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {userProfile.notifications?.orderUpdates ? (
                        <>
                          <FiCheckCircle className="mr-1" /> Enabled
                        </>
                      ) : (
                        <>
                          <FiXCircle className="mr-1" /> Disabled
                        </>
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FiDollarSign className="text-green-500" />
                      <span className="text-sm text-gray-500">Promotions</span>
                    </div>
                    <span className={`flex items-center text-xs ${
                      userProfile.notifications?.promotions ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {userProfile.notifications?.promotions ? (
                        <>
                          <FiCheckCircle className="mr-1" /> Enabled
                        </>
                      ) : (
                        <>
                          <FiXCircle className="mr-1" /> Disabled
                        </>
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FiInbox className="text-purple-500" />
                      <span className="text-sm text-gray-500">Recommendations</span>
                    </div>
                    <span className={`flex items-center text-xs ${
                      userProfile.notifications?.recommendations ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {userProfile.notifications?.recommendations ? (
                        <>
                          <FiCheckCircle className="mr-1" /> Enabled
                        </>
                      ) : (
                        <>
                          <FiXCircle className="mr-1" /> Disabled
                        </>
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
