'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

export default function AdminCouponForm({ coupon, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    code: '',
    type: 'percentage',
    value: '',
    minPurchase: 0,
    maxDiscount: '',
    startDate: '',
    expiryDate: '',
    usageLimit: '',
    description: '',
    isActive: true
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (coupon) {
      // Format dates for input fields
      const startDate = coupon.startDate 
        ? new Date(coupon.startDate).toISOString().split('T')[0]
        : '';
      
      const expiryDate = coupon.expiryDate
        ? new Date(coupon.expiryDate).toISOString().split('T')[0]
        : '';

      setFormData({
        code: coupon.code || '',
        type: coupon.type || 'percentage',
        value: coupon.value || '',
        minPurchase: coupon.minPurchase || 0,
        maxDiscount: coupon.maxDiscount || '',
        startDate,
        expiryDate,
        usageLimit: coupon.usageLimit || '',
        description: coupon.description || '',
        isActive: coupon.isActive !== undefined ? coupon.isActive : true
      });
    } else {
      // Set default start date to today
      const today = new Date().toISOString().split('T')[0];
      
      // Set default expiry date to 30 days from now
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      const defaultExpiryDate = thirtyDaysFromNow.toISOString().split('T')[0];
      
      setFormData(prev => ({
        ...prev,
        startDate: today,
        expiryDate: defaultExpiryDate
      }));
    }
  }, [coupon]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.code.trim()) {
      newErrors.code = 'Coupon code is required';
    }
    
    if (!formData.value || formData.value <= 0) {
      newErrors.value = 'Valid discount value is required';
    }
    
    if (formData.type === 'percentage' && formData.value > 100) {
      newErrors.value = 'Percentage discount cannot exceed 100%';
    }
    
    if (!formData.expiryDate) {
      newErrors.expiryDate = 'Expiry date is required';
    } else if (new Date(formData.expiryDate) <= new Date(formData.startDate)) {
      newErrors.expiryDate = 'Expiry date must be after start date';
    }
    
    if (formData.minPurchase && formData.minPurchase < 0) {
      newErrors.minPurchase = 'Minimum purchase cannot be negative';
    }
    
    if (formData.maxDiscount && formData.maxDiscount < 0) {
      newErrors.maxDiscount = 'Maximum discount cannot be negative';
    }
    
    if (formData.usageLimit && formData.usageLimit < 1) {
      newErrors.usageLimit = 'Usage limit must be at least 1';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    // Convert empty strings to null for optional numeric fields
    const processedData = {
      ...formData,
      maxDiscount: formData.maxDiscount === '' ? null : Number(formData.maxDiscount),
      usageLimit: formData.usageLimit === '' ? null : Number(formData.usageLimit),
      value: Number(formData.value),
      minPurchase: Number(formData.minPurchase)
    };
    
    onSubmit(processedData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Coupon Code */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Coupon Code*
          </label>
          <input
            type="text"
            name="code"
            value={formData.code}
            onChange={handleChange}
            className={`w-full p-2 border ${
              errors.code ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-0 focus:border-black`}
            placeholder="e.g. SUMMER20"
          />
          {errors.code && (
            <p className="text-red-500 text-xs">{errors.code}</p>
          )}
        </div>

        {/* Discount Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Discount Type*
          </label>
          <select
            name="type"
            value={formData.type}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-0 focus:border-black"
          >
            <option value="percentage">Percentage (%)</option>
            <option value="fixed">Fixed Amount (₹)</option>
          </select>
        </div>

        {/* Discount Value */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Discount Value*
          </label>
          <div className="relative">
            <input
              type="number"
              name="value"
              value={formData.value}
              onChange={handleChange}
              className={`w-full p-2 border ${
                errors.value ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-0 focus:border-black`}
              placeholder={formData.type === 'percentage' ? 'e.g. 20' : 'e.g. 100'}
              min="0"
              step="any"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              {formData.type === 'percentage' ? '%' : '₹'}
            </div>
          </div>
          {errors.value && (
            <p className="text-red-500 text-xs">{errors.value}</p>
          )}
        </div>

        {/* Minimum Purchase */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Minimum Purchase (₹)
          </label>
          <input
            type="number"
            name="minPurchase"
            value={formData.minPurchase}
            onChange={handleChange}
            className={`w-full p-2 border ${
              errors.minPurchase ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-0 focus:border-black`}
            placeholder="0 for no minimum"
            min="0"
            step="any"
          />
          {errors.minPurchase && (
            <p className="text-red-500 text-xs">{errors.minPurchase}</p>
          )}
        </div>

        {/* Maximum Discount (for percentage type) */}
        {formData.type === 'percentage' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Maximum Discount (₹)
            </label>
            <input
              type="number"
              name="maxDiscount"
              value={formData.maxDiscount}
              onChange={handleChange}
              className={`w-full p-2 border ${
                errors.maxDiscount ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-0 focus:border-black`}
              placeholder="Leave empty for no maximum"
              min="0"
              step="any"
            />
            {errors.maxDiscount && (
              <p className="text-red-500 text-xs">{errors.maxDiscount}</p>
            )}
          </div>
        )}

        {/* Start Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Start Date
          </label>
          <input
            type="date"
            name="startDate"
            value={formData.startDate}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-0 focus:border-black"
          />
        </div>

        {/* Expiry Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Expiry Date*
          </label>
          <input
            type="date"
            name="expiryDate"
            value={formData.expiryDate}
            onChange={handleChange}
            className={`w-full p-2 border ${
              errors.expiryDate ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-0 focus:border-black`}
          />
          {errors.expiryDate && (
            <p className="text-red-500 text-xs">{errors.expiryDate}</p>
          )}
        </div>

        {/* Usage Limit */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Usage Limit
          </label>
          <input
            type="number"
            name="usageLimit"
            value={formData.usageLimit}
            onChange={handleChange}
            className={`w-full p-2 border ${
              errors.usageLimit ? 'border-red-500' : 'border-gray-300'
            } focus:outline-none focus:ring-0 focus:border-black`}
            placeholder="Leave empty for unlimited"
            min="1"
          />
          {errors.usageLimit && (
            <p className="text-red-500 text-xs">{errors.usageLimit}</p>
          )}
        </div>

        {/* Active Status */}
        <div className="space-y-2">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleChange}
              className="h-4 w-4 text-black focus:ring-0 focus:ring-offset-0 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">Active</span>
          </label>
          <p className="text-xs text-gray-500">
            Inactive coupons cannot be used even if they are not expired
          </p>
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          rows="3"
          className="w-full p-2 border border-gray-300 focus:outline-none focus:ring-0 focus:border-black"
          placeholder="Optional description for this coupon"
        ></textarea>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 text-sm"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-black text-white text-sm"
        >
          {coupon ? 'Update Coupon' : 'Create Coupon'}
        </button>
      </div>
    </form>
  );
}
