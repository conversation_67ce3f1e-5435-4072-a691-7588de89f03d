import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Order from '@/models/Order';
import { getSessionUser } from '@/lib/auth';

export async function GET(request) {
  try {
    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get user's orders
    const orders = await Order.find({ user: user._id })
      .sort({ createdAt: -1 }) // Sort by newest first
      .populate('items.product', 'name images price'); // Populate product details

    return NextResponse.json(orders);
  } catch (error) {
    console.error('Error fetching user orders:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
} 