'use client';
import { useState } from 'react';
import { FiLoader } from 'react-icons/fi';

const GIFT_CARD_AMOUNTS = [500, 1000, 2000, 5000, 10000];

export default function GiftCardForm({ onSubmit, isProcessing }) {
  const [formData, setFormData] = useState({
    amount: 1000,
    recipientName: '',
    recipientEmail: '',
    senderName: '',
    message: '',
  });
  const [customAmount, setCustomAmount] = useState('');
  const [useCustomAmount, setUseCustomAmount] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAmountSelect = (amount) => {
    setFormData((prev) => ({ ...prev, amount }));
    setUseCustomAmount(false);
  };

  const handleCustomAmountChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setCustomAmount(value);
    if (value && parseInt(value) >= 100) {
      setFormData((prev) => ({ ...prev, amount: parseInt(value) }));
    }
  };

  const toggleCustomAmount = () => {
    setUseCustomAmount(!useCustomAmount);
    if (!useCustomAmount) {
      setFormData((prev) => ({ ...prev, amount: customAmount ? parseInt(customAmount) : 100 }));
    } else {
      setFormData((prev) => ({ ...prev, amount: 1000 }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.recipientName) {
      alert('Please enter recipient name');
      return;
    }
    
    if (!formData.recipientEmail) {
      alert('Please enter recipient email');
      return;
    }
    
    if (!formData.senderName) {
      alert('Please enter your name');
      return;
    }
    
    if (!formData.amount || formData.amount < 100) {
      alert('Please enter a valid amount (minimum ₹100)');
      return;
    }
    
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Amount Selection */}
      <div className="space-y-4">
        <label className="block text-sm uppercase tracking-wider mb-2">
          Gift Card Amount
        </label>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {GIFT_CARD_AMOUNTS.map((amount) => (
            <button
              key={amount}
              type="button"
              onClick={() => handleAmountSelect(amount)}
              className={`py-3 px-4 border ${
                !useCustomAmount && formData.amount === amount
                  ? 'border-black bg-black text-white'
                  : 'border-gray-300 hover:border-gray-400'
              } transition-colors`}
              disabled={isProcessing}
            >
              ₹{amount.toLocaleString('en-IN')}
            </button>
          ))}
          
          <button
            type="button"
            onClick={toggleCustomAmount}
            className={`py-3 px-4 border ${
              useCustomAmount
                ? 'border-black bg-black text-white'
                : 'border-gray-300 hover:border-gray-400'
            } transition-colors`}
            disabled={isProcessing}
          >
            Custom
          </button>
        </div>
        
        {useCustomAmount && (
          <div className="mt-3">
            <label htmlFor="customAmount" className="sr-only">
              Custom Amount
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-gray-500">₹</span>
              </div>
              <input
                type="text"
                id="customAmount"
                value={customAmount}
                onChange={handleCustomAmountChange}
                placeholder="Enter amount (min ₹100)"
                className="w-full pl-8 py-2 border-0 border-b border-gray-300 focus:ring-0 focus:border-black"
                disabled={isProcessing}
              />
            </div>
            {customAmount && parseInt(customAmount) < 100 && (
              <p className="text-red-500 text-sm mt-1">
                Minimum amount is ₹100
              </p>
            )}
          </div>
        )}
      </div>

      {/* Recipient Information */}
      <div className="space-y-4">
        <label className="block text-sm uppercase tracking-wider mb-2">
          Recipient Information
        </label>
        
        <div>
          <input
            type="text"
            name="recipientName"
            value={formData.recipientName}
            onChange={handleChange}
            placeholder="Recipient's Name"
            className="w-full px-0 py-2 bg-transparent border-0 border-b border-gray-300 focus:ring-0 focus:border-black"
            required
            disabled={isProcessing}
          />
        </div>
        
        <div>
          <input
            type="email"
            name="recipientEmail"
            value={formData.recipientEmail}
            onChange={handleChange}
            placeholder="Recipient's Email"
            className="w-full px-0 py-2 bg-transparent border-0 border-b border-gray-300 focus:ring-0 focus:border-black"
            required
            disabled={isProcessing}
          />
        </div>
      </div>

      {/* Sender Information */}
      <div className="space-y-4">
        <label className="block text-sm uppercase tracking-wider mb-2">
          Your Information
        </label>
        
        <div>
          <input
            type="text"
            name="senderName"
            value={formData.senderName}
            onChange={handleChange}
            placeholder="Your Name (as it will appear to the recipient)"
            className="w-full px-0 py-2 bg-transparent border-0 border-b border-gray-300 focus:ring-0 focus:border-black"
            required
            disabled={isProcessing}
          />
        </div>
      </div>

      {/* Personal Message */}
      <div className="space-y-4">
        <label className="block text-sm uppercase tracking-wider mb-2">
          Personal Message (Optional)
        </label>
        
        <div>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder="Add a personal message to the recipient"
            rows={4}
            className="w-full px-0 py-2 bg-transparent border-0 border-b border-gray-300 focus:ring-0 focus:border-black resize-none"
            disabled={isProcessing}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          className="w-full bg-black text-white py-3 px-6 hover:bg-gray-800 transition-colors flex items-center justify-center"
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <FiLoader className="animate-spin mr-2" />
              Processing...
            </>
          ) : (
            `Purchase Gift Card (₹${formData.amount?.toLocaleString('en-IN') || '0'})`
          )}
        </button>
      </div>

      {/* Terms */}
      <div className="text-sm text-gray-500">
        <p>
          By purchasing a gift card, you agree to our{' '}
          <a href="/terms" className="underline">
            Terms and Conditions
          </a>
          . Gift cards are valid for one year from the date of purchase and cannot be exchanged for cash.
        </p>
      </div>
    </form>
  );
}
