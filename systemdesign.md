# FurnitureBazaar System Design

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Tech Stack](#tech-stack)
- [Database Design](#database-design)
- [Authentication & Authorization](#authentication--authorization)
- [Frontend Architecture](#frontend-architecture)
- [API Design](#api-design)
- [Payment Processing](#payment-processing)
- [Performance Considerations](#performance-considerations)
- [Security Considerations](#security-considerations)
- [Deployment Strategy](#deployment-strategy)
- [Future Enhancements](#future-enhancements)

## Overview

FurnitureBazaar is a modern e-commerce platform specializing in furniture and home decor. The application is built with Next.js 15 and MongoDB, providing a seamless shopping experience with advanced features for both customers and administrators.

The platform combines elegant design with powerful functionality, offering users an intuitive interface to browse, search, and purchase furniture items while giving administrators robust tools to manage products, orders, and customer relationships.

## Architecture

FurnitureBazaar follows a modern architecture pattern with the following key components:

1. **Client Layer** - Next.js frontend with React components
2. **API Layer** - Next.js API routes handling requests
3. **Database Layer** - MongoDB storing application data
4. **Authentication System** - JWT-based auth flow
5. **External Services** - Payment processing, email service, etc.

![Architecture Diagram](./public/docs/architecture-diagram.png)

### Key Components

- **Next.js App Router**: Handles routing and server-side rendering
- **API Routes**: Provides backend functionality
- **MongoDB**: Stores application data
- **Context API**: Manages global state
- **Middleware**: Handles authentication and route protection

## Tech Stack

### Frontend
- **Framework**: Next.js 15.2.0, React 19.0.0
- **Styling**: TailwindCSS 3.4.1, Framer Motion 12.4.4
- **State Management**: React Context API
- **UI Components**: React Icons 5.4.0
- **Notifications**: React Hot Toast 2.5.2

### Backend
- **API Routes**: Next.js API Routes
- **Database**: MongoDB with Mongoose 8.10.1
- **Authentication**: JWT (jsonwebtoken 9.0.2)
- **Security**: bcryptjs 3.0.2
- **Date Handling**: date-fns 4.1.0
- **Email Service**: Resend for transactional emails
- **Payment Processing**: Razorpay for secure online payments

### Development Tools
- **Linting**: ESLint 9
- **Build Tool**: Turbopack

## Database Design

FurnitureBazaar uses MongoDB as its primary database. The main data models include:

### User Model
```javascript
{
  name: String,
  email: String,
  password: String (hashed),
  role: String (user/admin),
  status: String (active/inactive),
  phone: String,
  address: String,
  avatar: String,
  emailVerified: Boolean,
  verificationToken: String,
  verificationTokenExpires: Date,
  resetToken: String,
  resetTokenExpires: Date,
  notifications: {
    orderUpdates: Boolean,
    promotions: Boolean,
    recommendations: Boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Product Model
```javascript
{
  name: String,
  description: String,
  price: Number,
  category: String,
  stock: Number,
  images: [String],
  discount: Number,
  isPublished: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Order Model
```javascript
{
  user: ObjectId (ref: User),
  items: [{
    product: ObjectId (ref: Product),
    quantity: Number,
    price: Number,
    originalPrice: Number,
    discount: Number,
    name: String,
    image: String
  }],
  total: Number,
  status: String (pending/processing/shipped/delivered/cancelled),
  paymentMethod: String (cod/razorpay/card/upi/netbanking/wallet),
  paymentStatus: String (pending/paid/failed),
  paymentId: String,
  shippingAddress: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Contact Model
```javascript
{
  name: String,
  email: String,
  phone: String,
  subject: String,
  message: String,
  createdAt: Date
}
```

## Authentication & Authorization

FurnitureBazaar implements a secure authentication system using JWT (JSON Web Tokens).

### Authentication Flow
1. User registers or logs in
2. Server validates credentials and generates a JWT
3. JWT is stored in an HTTP-only cookie
4. Middleware validates the JWT on protected routes
5. Context API manages authentication state on the client

### Authorization Levels
- **Public**: Accessible to all users
- **Protected**: Requires authentication
- **Admin**: Requires admin role

### Route Protection
- Middleware checks authentication status for protected routes
- Admin routes are further protected by role checks
- Redirects unauthenticated users to login page

## Frontend Architecture

### Component Structure
The frontend is organized into reusable components:

- **Layout Components**: Navbar, Footer, etc.
- **UI Components**: Buttons, Cards, Forms, etc.
- **Page Components**: Home, Products, Cart, etc.
- **Admin Components**: Dashboard, Product Management, etc.

### State Management
FurnitureBazaar uses React Context API for global state management:

- **AuthContext**: Manages user authentication state
- **CartContext**: Manages shopping cart and wishlist
- **UIContext**: Manages UI state (modals, notifications, etc.)

### Responsive Design
- Mobile-first approach
- Optimized for all device sizes
- Consistent experience across platforms

## API Design

FurnitureBazaar provides a comprehensive API for interacting with the platform:

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/check-email` - Check if email exists
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token
- `GET /api/auth/verify-email` - Verify email address
- `GET /api/auth/check` - Check authentication status

### Products Endpoints
- `GET /api/products` - Fetch all products
- `GET /api/products/[id]` - Fetch single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/[id]` - Update product (admin only)
- `DELETE /api/products/[id]` - Delete product (admin only)

### Orders Endpoints
- `POST /api/orders` - Create new order
- `GET /api/orders` - Fetch user orders
- `GET /api/orders/[id]` - Fetch single order
- `PUT /api/orders/[id]` - Update order status (admin only)

### Payment Endpoints
- `POST /api/payment/razorpay` - Create Razorpay payment order
- `PUT /api/payment/razorpay` - Verify Razorpay payment
- `POST /api/payment/razorpay/webhook` - Handle Razorpay webhooks

### User Endpoints
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/cart` - Get user cart
- `PUT /api/user/cart` - Update user cart
- `GET /api/user/wishlist` - Get user wishlist
- `PUT /api/user/wishlist` - Update user wishlist

## Payment Processing

FurnitureBazaar integrates with Razorpay for payment processing, specifically optimized for Indian customers.

### Payment Flow
1. User initiates checkout
2. Server creates a Razorpay order
3. Client displays Razorpay payment form
4. User completes payment
5. Server verifies payment signature
6. Order is created with payment status

### Payment Methods
- Cash on Delivery (COD)
- Online payments via Razorpay:
  - Credit/Debit Cards
  - UPI
  - Net Banking
  - Wallets

### Security Measures
- Server-side signature verification
- Webhook integration for payment status updates
- Secure handling of payment information

## Performance Considerations

### Image Optimization
- Next.js Image component for automatic optimization
- Responsive images with appropriate sizes
- CDN integration for faster delivery

### Code Optimization
- Server-side rendering for critical pages
- Client-side rendering for dynamic content
- Code splitting for faster initial load

### Caching Strategy
- Static generation for product pages
- Incremental Static Regeneration for frequently updated content
- API response caching

## Security Considerations

### Data Protection
- Password hashing with bcrypt
- HTTPS for all communications
- Input validation and sanitization

### Authentication Security
- JWT stored in HTTP-only cookies
- Token expiration and refresh mechanism
- CSRF protection

### API Security
- Rate limiting
- Input validation
- Error handling that doesn't expose sensitive information

## Deployment Strategy

FurnitureBazaar is configured for deployment on Vercel, with the following considerations:

### Environment Setup
- Environment variables for sensitive information
- Production, staging, and development environments

### Continuous Integration/Deployment
- Automated testing before deployment
- Staged rollouts
- Rollback capability

### Monitoring
- Error tracking
- Performance monitoring
- User analytics

## Future Enhancements

### Technical Enhancements
- Implement server-side caching
- Add WebSocket for real-time updates
- Implement progressive web app (PWA) features

### Feature Enhancements
- Enhanced search with filters and sorting
- Product recommendations
- User reviews and ratings
- Social media integration
- 3D furniture model viewing capability
