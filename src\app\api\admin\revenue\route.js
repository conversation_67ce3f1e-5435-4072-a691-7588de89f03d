import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';
import Product from '@/models/Product';
import { startOfDay, subDays, format } from 'date-fns';

export async function GET(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get timeframe from query params
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30days';

    // Calculate date range
    const now = new Date();
    let startDate;
    let daysToCalculate;

    switch(timeframe) {
      case '7days':
        startDate = subDays(now, 7);
        daysToCalculate = 7;
        break;
      case '30days':
        startDate = subDays(now, 30);
        daysToCalculate = 30;
        break;
      case '12months':
        startDate = subDays(now, 365);
        daysToCalculate = 365;
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1); // January 1st of current year
        daysToCalculate = Math.ceil((now - startDate) / (1000 * 60 * 60 * 24)); // Days elapsed in current year
        break;
      default:
        startDate = subDays(now, 30); // Default to 30 days
        daysToCalculate = 30;
    }

    // Get orders within date range
    const orders = await Order.find({
      createdAt: { $gte: startDate }
    }).populate({
      path: 'items.product',
      model: Product,
      select: 'name images price' // Only select fields we need
    });

    // Calculate summary statistics
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);

    // Default values if no previous data
    const revenueChange = 0;
    const conversionRate = 2.4;

    const response = {
      summary: {
        totalRevenue,
        totalOrders: orders.length,
        averageOrderValue: orders.length > 0 ? totalRevenue / orders.length : 0,
        revenueChange,
        conversionRate
      },
      dailyRevenue: [],
      topProducts: [],
      paymentMethods: []
    };

    // Calculate revenue based on timeframe
    if (timeframe === '7days' || timeframe === '30days') {
      // Daily revenue for 7 days and 30 days
      for (let i = 0; i < daysToCalculate; i++) {
        const date = subDays(now, i);
        const dayStart = startOfDay(date);
        const dayEnd = new Date(dayStart);
        dayEnd.setDate(dayStart.getDate() + 1);

        const dayRevenue = orders
          .filter(order => order.createdAt >= dayStart && order.createdAt < dayEnd)
          .reduce((sum, order) => sum + order.total, 0);

        response.dailyRevenue.unshift({
          date: format(date, 'yyyy-MM-dd'),
          revenue: dayRevenue,
          displayDate: format(date, 'dd MMM')
        });
      }
    } else if (timeframe === '12months' || timeframe === 'year') {
      // Monthly revenue for 12 months or this year
      const monthlyData = {};

      // Initialize all months with zero revenue
      const monthCount = timeframe === '12months' ? 12 : new Date().getMonth() + 1;
      const startYear = timeframe === 'year' ? new Date().getFullYear() : new Date().getFullYear() - 1;
      const startMonth = timeframe === 'year' ? 0 : new Date().getMonth() + 1;

      for (let i = 0; i < monthCount; i++) {
        let month, year;

        if (timeframe === 'year') {
          month = i;
          year = startYear;
        } else {
          // For 12months, calculate backward from current month
          month = (startMonth + i) % 12;
          year = startYear + Math.floor((startMonth + i) / 12);
        }

        const monthKey = `${year}-${String(month + 1).padStart(2, '0')}`;
        const monthName = new Date(year, month, 1).toLocaleString('default', { month: 'short' });

        monthlyData[monthKey] = {
          date: `${year}-${String(month + 1).padStart(2, '0')}-01`,
          revenue: 0,
          displayDate: `${monthName} ${year}`
        };
      }

      // Aggregate order data by month
      orders.forEach(order => {
        const orderDate = new Date(order.createdAt);
        const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`;

        if (monthlyData[monthKey]) {
          monthlyData[monthKey].revenue += order.total;
        }
      });

      // Convert to array and sort by date
      response.dailyRevenue = Object.values(monthlyData).sort((a, b) =>
        new Date(a.date) - new Date(b.date)
      );
    }

    // Calculate top products
    const productMap = new Map();
    orders.forEach(order => {
      order.items.forEach(item => {
        if (!item.product) return; // Skip if product reference is missing

        const product = item.product;
        const productId = product._id.toString();

        if (!productMap.has(productId)) {
          productMap.set(productId, {
            _id: productId,
            name: product.name,
            revenue: 0,
            unitsSold: 0
          });
        }

        const productStats = productMap.get(productId);
        productStats.revenue += item.price * item.quantity;
        productStats.unitsSold += item.quantity;
      });
    });

    response.topProducts = Array.from(productMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Calculate payment methods
    const paymentMethodsMap = {};
    orders.forEach(order => {
      const method = order.paymentMethod || 'unknown';
      if (!paymentMethodsMap[method]) {
        paymentMethodsMap[method] = { name: method, total: 0, count: 0 };
      }
      paymentMethodsMap[method].total += order.total;
      paymentMethodsMap[method].count += 1;
    });

    response.paymentMethods = Object.values(paymentMethodsMap);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in revenue API:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
