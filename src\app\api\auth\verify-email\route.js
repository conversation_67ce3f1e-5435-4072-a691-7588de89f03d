import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import User from '@/models/User';
import { verifyToken } from '@/lib/jwt';

// Helper function to handle verification logic
async function verifyEmailToken(token) {
  if (!token) {
    return {
      success: false,
      status: 400,
      message: 'Verification token is required'
    };
  }

  // Verify the token
  const decoded = verifyToken(token);

  if (!decoded || !decoded.userId) {
    return {
      success: false,
      status: 400,
      message: 'Invalid or expired verification token'
    };
  }

  // Connect to database
  await connectToDB();

  // First check if user exists and is not already verified
  const existingUser = await User.findById(decoded.userId);

  if (!existingUser) {
    return {
      success: false,
      status: 404,
      message: 'User not found'
    };
  }

  if (existingUser.emailVerified) {
    return {
      success: true,
      alreadyVerified: true,
      user: {
        id: existingUser._id,
        name: existingUser.name,
        email: existingUser.email,
        emailVerified: existingUser.emailVerified
      }
    };
  }

  // Update user's email verification status
  const user = await User.findByIdAndUpdate(
    decoded.userId,
    { emailVerified: true },
    { new: true }
  );

  return {
    success: true,
    user: {
      id: user._id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified
    }
  };
}

// GET handler for direct link verification
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    const result = await verifyEmailToken(token);

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: result.status }
      );
    }

    if (result.alreadyVerified) {
      return NextResponse.json({
        message: 'Email was already verified',
        user: result.user
      });
    }

    return NextResponse.json({
      message: 'Email verified successfully',
      user: result.user
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'An error occurred during email verification' },
      { status: 500 }
    );
  }
}

// POST handler for client-side verification
export async function POST(request) {
  try {
    const { token } = await request.json();

    const result = await verifyEmailToken(token);

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: result.status }
      );
    }

    if (result.alreadyVerified) {
      return NextResponse.json({
        message: 'Email was already verified',
        user: result.user
      });
    }

    return NextResponse.json({
      message: 'Email verified successfully',
      user: result.user
    });
  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'An error occurred during email verification' },
      { status: 500 }
    );
  }
}