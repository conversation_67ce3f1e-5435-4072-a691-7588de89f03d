import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

// Common button style using black
const buttonStyle = "background-color: #000000; color: white; padding: 12px 24px; text-decoration: none; border-radius: 0; display: inline-block;";

// Common styles for order confirmation email
const tableStyle = "width: 100%; border-collapse: collapse; margin-bottom: 20px;";
const thStyle = "text-align: left; padding: 10px; border-bottom: 1px solid #eee; color: #666; font-weight: normal;";
const tdStyle = "text-align: left; padding: 10px; border-bottom: 1px solid #eee;";
const tdRightStyle = "text-align: right; padding: 10px; border-bottom: 1px solid #eee;";
const totalRowStyle = "font-weight: bold; border-top: 2px solid #eee;";


export async function sendVerificationEmail(email, token) {
  try {
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${token}`;

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: 'Verify your email address',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Verify Your Email Address</h1>
          <p>Thank you for registering with FurnitureBazaar! Please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" style="${buttonStyle}">
              Verify Email
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="color: #666; word-break: break-all;">${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
          <p>If you didn't create an account with FurnitureBazaar, you can safely ignore this email.</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendVerificationEmail:', error);
    return { success: false, error };
  }
}

export async function sendWelcomeEmail(email, name) {
  try {
    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: 'Welcome to FurnitureBazaar!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Welcome to FurnitureBazaar!</h1>
          <p>Dear ${name},</p>
          <p>Thank you for joining FurnitureBazaar! We're excited to have you as part of our community.</p>
          <p>With your new account, you can:</p>
          <ul style="color: #666;">
            <li>Browse our extensive collection of furniture</li>
            <li>Save your favorite items</li>
            <li>Track your orders</li>
            <li>Get exclusive deals and offers</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/products" style="${buttonStyle}">
              Start Shopping
            </a>
          </div>
          <p>If you have any questions, our customer service team is always here to help!</p>
          <p>Best regards,<br>The FurnitureBazaar Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending welcome email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendWelcomeEmail:', error);
    return { success: false, error };
  }
}

export async function sendLoginNotificationEmail(email, name, loginInfo) {
  try {
    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: 'New Login to Your FurnitureBazaar Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">New Login Detected</h1>
          <p>Hi ${name},</p>
          <p>We detected a new login to your FurnitureBazaar account.</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 0; margin: 20px 0;">
            <p style="margin: 0;"><strong>Login Details:</strong></p>
            <p style="margin: 5px 0;">Time: ${loginInfo.time}</p>
            <p style="margin: 5px 0;">Device: ${loginInfo.device}</p>
            <p style="margin: 5px 0;">Location: ${loginInfo.location || 'Unknown'}</p>
          </div>
          <p>If this was you, you can ignore this email. If you didn't log in to your account, please secure your account immediately:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/profile/security" style="${buttonStyle}">
              Secure Your Account
            </a>
          </div>
          <p>For your security, we recommend:</p>
          <ul style="color: #666;">
            <li>Using a strong, unique password</li>
            <li>Enabling two-factor authentication</li>
            <li>Regularly reviewing your account activity</li>
          </ul>
          <p>If you need help, please contact our support team immediately.</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending login notification email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendLoginNotificationEmail:', error);
    return { success: false, error };
  }
}

export async function sendPasswordResetEmail(email, token) {
  try {
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${token}`;

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: 'Reset your password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Reset Your Password</h1>
          <p>You requested to reset your password. Click the button below to create a new password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="${buttonStyle}">
              Reset Password
            </a>
          </div>
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="color: #666; word-break: break-all;">${resetUrl}</p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request a password reset, you can safely ignore this email.</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendPasswordResetEmail:', error);
    return { success: false, error };
  }
}

export async function sendOrderConfirmationEmail(email, name, order) {
  try {
    // Format the order date
    const orderDate = new Date(order.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Format the order items for the email
    const orderItemsHtml = order.items.map(item => `
      <tr>
        <td style="${tdStyle}">${item.name}</td>
        <td style="${tdStyle}">${item.quantity}</td>
        <td style="${tdRightStyle}">₹${item.price.toLocaleString('en-IN')}</td>
        <td style="${tdRightStyle}">₹${(item.price * item.quantity).toLocaleString('en-IN')}</td>
      </tr>
    `).join('');

    // Format the order total
    const formattedTotal = order.total.toLocaleString('en-IN');

    // Get the order status with proper formatting
    const getStatusDisplay = (status) => {
      switch(status) {
        case 'pending': return '<span style="color: #f59e0b;">Pending</span>';
        case 'processing': return '<span style="color: #3b82f6;">Processing</span>';
        case 'shipped': return '<span style="color: #6366f1;">Shipped</span>';
        case 'delivered': return '<span style="color: #10b981;">Delivered</span>';
        case 'cancelled': return '<span style="color: #ef4444;">Cancelled</span>';
        default: return '<span style="color: #6b7280;">Processing</span>';
      }
    };

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: `Order Confirmation #${order._id.toString().slice(-6)}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Order Confirmation</h1>
          <p>Dear ${name},</p>
          <p>Thank you for your order! We're pleased to confirm that we've received your order and it's being processed.</p>

          <div style="background-color: #f9fafb; padding: 20px; margin: 20px 0;">
            <h2 style="font-size: 16px; margin-top: 0;">Order Summary</h2>
            <p><strong>Order Number:</strong> #${order._id.toString().slice(-6)}</p>
            <p><strong>Order Date:</strong> ${orderDate}</p>
            <p><strong>Order Status:</strong> ${getStatusDisplay(order.status)}</p>
          </div>

          <h2 style="font-size: 16px;">Order Details</h2>
          <table style="${tableStyle}">
            <thead>
              <tr>
                <th style="${thStyle}">Product</th>
                <th style="${thStyle}">Quantity</th>
                <th style="${thStyle}">Price</th>
                <th style="${thStyle}">Total</th>
              </tr>
            </thead>
            <tbody>
              ${orderItemsHtml}
              <tr style="${totalRowStyle}">
                <td style="${tdStyle}" colspan="3">Total</td>
                <td style="${tdRightStyle}">₹${formattedTotal}</td>
              </tr>
            </tbody>
          </table>

          <div style="margin: 30px 0;">
            <h2 style="font-size: 16px;">Shipping Address</h2>
            <p style="white-space: pre-line;">${order.shippingAddress}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/orders/${order._id.toString()}" style="${buttonStyle}">
              View Order Details
            </a>
          </div>

          <p>If you have any questions about your order, please contact our customer service team.</p>
          <p>Thank you for shopping with FurnitureBazaar!</p>
          <p>Best regards,<br>The FurnitureBazaar Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending order confirmation email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendOrderConfirmationEmail:', error);
    return { success: false, error };
  }
}

export async function sendOrderStatusUpdateEmail(email, name, order, previousStatus) {
  try {
    // Get status information
    const getStatusInfo = (status) => {
      switch(status) {
        case 'pending':
          return {
            color: '#f59e0b',
            label: 'Pending',
            description: 'Your order has been received and is being reviewed.'
          };
        case 'processing':
          return {
            color: '#3b82f6',
            label: 'Processing',
            description: 'Your order is being processed and prepared for shipping.'
          };
        case 'shipped':
          return {
            color: '#6366f1',
            label: 'Shipped',
            description: 'Your order has been shipped and is on its way to you.'
          };
        case 'delivered':
          return {
            color: '#10b981',
            label: 'Delivered',
            description: 'Your order has been delivered successfully.'
          };
        case 'cancelled':
          return {
            color: '#ef4444',
            label: 'Cancelled',
            description: 'Your order has been cancelled.'
          };
        default:
          return {
            color: '#6b7280',
            label: 'Processing',
            description: 'Your order is being processed.'
          };
      }
    };

    const currentStatus = getStatusInfo(order.status);

    // Format the order date
    const orderDate = new Date(order.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Create subject line based on status
    let subject = '';
    const orderIdShort = order._id.toString().slice(-6);
    switch(order.status) {
      case 'processing':
        subject = `Your Order #${orderIdShort} is Being Processed`;
        break;
      case 'shipped':
        subject = `Your Order #${orderIdShort} Has Been Shipped`;
        break;
      case 'delivered':
        subject = `Your Order #${orderIdShort} Has Been Delivered`;
        break;
      case 'cancelled':
        subject = `Your Order #${orderIdShort} Has Been Cancelled`;
        break;
      default:
        subject = `Order #${orderIdShort} Status Update`;
    }

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Order Status Update</h1>
          <p>Dear ${name},</p>
          <p>The status of your order has been updated.</p>

          <div style="background-color: #f9fafb; padding: 20px; margin: 20px 0;">
            <h2 style="font-size: 16px; margin-top: 0;">Order Summary</h2>
            <p><strong>Order Number:</strong> #${order._id.toString().slice(-6)}</p>
            <p><strong>Order Date:</strong> ${orderDate}</p>
            <p><strong>Previous Status:</strong> ${getStatusInfo(previousStatus).label}</p>
            <p><strong>New Status:</strong> <span style="color: ${currentStatus.color};">${currentStatus.label}</span></p>
          </div>

          <div style="border-left: 4px solid ${currentStatus.color}; padding-left: 20px; margin: 20px 0;">
            <p><strong>Status Details:</strong> ${currentStatus.description}</p>
          </div>

          ${order.status === 'shipped' ? `
          <div style="background-color: #f0f9ff; padding: 20px; margin: 20px 0; border-left: 4px solid #3b82f6;">
            <h3 style="margin-top: 0; font-size: 16px;">Shipping Information</h3>
            <p>Your order is on its way! You should receive it within 3-5 business days.</p>
            <p>If you have any questions about your delivery, please contact our customer service team.</p>
          </div>
          ` : ''}

          ${order.status === 'cancelled' ? `
          <div style="background-color: #fef2f2; padding: 20px; margin: 20px 0; border-left: 4px solid #ef4444;">
            <h3 style="margin-top: 0; font-size: 16px;">Cancellation Information</h3>
            <p>Your order has been cancelled. If you did not request this cancellation, please contact our customer service team immediately.</p>
            <p>If you requested the cancellation, any payment will be refunded within 5-7 business days.</p>
          </div>
          ` : ''}

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/orders/${order._id.toString()}" style="${buttonStyle}">
              View Order Details
            </a>
          </div>

          <p>If you have any questions about your order, please contact our customer service team.</p>
          <p>Thank you for shopping with FurnitureBazaar!</p>
          <p>Best regards,<br>The FurnitureBazaar Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending order status update email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendOrderStatusUpdateEmail:', error);
    return { success: false, error };
  }
}

export async function sendNewsletterWelcomeEmail(email) {
  try {
    // Get current date for the footer
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });

    // Create unsubscribe URL
    const unsubscribeUrl = `${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe`;

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: 'Welcome to FurnitureBazaar Newsletter!',
      html: `
        <div style="font-family: 'Helvetica Neue', Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333; line-height: 1.5;">
          <!-- Header with logo -->
          <div style="text-align: center; margin-bottom: 30px; padding-top: 30px;">
            <h1 style="text-transform: uppercase; letter-spacing: 3px; font-size: 24px; font-weight: 500; margin: 0;">FurnitureBazaar</h1>
          </div>

          <!-- Main banner -->
          <div style="background-color: #f8f8f8; padding: 40px 30px; text-align: center; margin-bottom: 30px;">
            <h2 style="font-size: 28px; margin: 0 0 15px; font-weight: 500; text-transform: uppercase; letter-spacing: 1px;">Welcome to Our Newsletter</h2>
            <p style="font-size: 16px; margin: 0; color: #555;">Thank you for joining our community of design enthusiasts</p>
          </div>

          <!-- Welcome message -->
          <div style="padding: 0 20px; margin-bottom: 30px;">
            <p style="font-size: 16px; margin-bottom: 20px;">
              We're excited to have you join the FurnitureBazaar family. As a subscriber, you'll be the first to know about our latest collections, exclusive offers, and interior design inspiration.
            </p>
          </div>

          <!-- What to expect section -->
          <div style="padding: 0 20px; margin-bottom: 40px;">
            <h3 style="font-size: 18px; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 20px; font-weight: 500;">What You'll Receive</h3>

            <div style="display: flex; margin-bottom: 15px;">
              <div style="width: 30px; min-width: 30px; text-align: center; margin-right: 15px;">
                <span style="font-size: 18px;">✦</span>
              </div>
              <div>
                <p style="margin: 0; font-size: 16px;"><strong>New Collections</strong> - Be the first to discover our latest furniture pieces</p>
              </div>
            </div>

            <div style="display: flex; margin-bottom: 15px;">
              <div style="width: 30px; min-width: 30px; text-align: center; margin-right: 15px;">
                <span style="font-size: 18px;">✦</span>
              </div>
              <div>
                <p style="margin: 0; font-size: 16px;"><strong>Exclusive Offers</strong> - Enjoy special discounts and promotions for subscribers only</p>
              </div>
            </div>

            <div style="display: flex; margin-bottom: 15px;">
              <div style="width: 30px; min-width: 30px; text-align: center; margin-right: 15px;">
                <span style="font-size: 18px;">✦</span>
              </div>
              <div>
                <p style="margin: 0; font-size: 16px;"><strong>Design Inspiration</strong> - Get expert tips and ideas for styling your home</p>
              </div>
            </div>

            <div style="display: flex; margin-bottom: 15px;">
              <div style="width: 30px; min-width: 30px; text-align: center; margin-right: 15px;">
                <span style="font-size: 18px;">✦</span>
              </div>
              <div>
                <p style="margin: 0; font-size: 16px;"><strong>Seasonal Trends</strong> - Stay updated with the latest interior design trends</p>
              </div>
            </div>
          </div>

          <!-- Featured products section -->
          <div style="background-color: #f8f8f8; padding: 30px; margin-bottom: 30px; text-align: center;">
            <h3 style="font-size: 18px; text-transform: uppercase; letter-spacing: 1px; margin: 0 0 20px; font-weight: 500;">Explore Our Collection</h3>
            <p style="margin-bottom: 25px; font-size: 16px;">Discover timeless pieces that transform your space</p>

            <div style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/products"
                 style="background-color: #000000; color: white; padding: 14px 28px; text-decoration: none;
                        font-size: 14px; text-transform: uppercase; letter-spacing: 1px; display: inline-block;">
                SHOP NOW
              </a>
            </div>
          </div>

          <!-- Social media section -->
          <div style="text-align: center; margin-bottom: 30px; padding: 0 20px;">
            <p style="font-size: 16px; margin-bottom: 15px;">Follow us for more inspiration</p>
            <div>
              <a href="#" style="display: inline-block; margin: 0 10px; text-decoration: none;">
                <img src="https://cdn.cosmos.so/d/icons/instagram-black.png" alt="Instagram" width="24" height="24" style="border: none;">
              </a>
              <a href="#" style="display: inline-block; margin: 0 10px; text-decoration: none;">
                <img src="https://cdn.cosmos.so/d/icons/facebook-black.png" alt="Facebook" width="24" height="24" style="border: none;">
              </a>
              <a href="#" style="display: inline-block; margin: 0 10px; text-decoration: none;">
                <img src="https://cdn.cosmos.so/d/icons/pinterest-black.png" alt="Pinterest" width="24" height="24" style="border: none;">
              </a>
            </div>
          </div>

          <!-- Footer -->
          <div style="margin-top: 40px; padding: 20px 0; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280; text-align: center;">
            <p style="margin-bottom: 10px;">
              You're receiving this email because you subscribed to our newsletter.
              <br>
              <a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: underline;">
                Unsubscribe
              </a>
            </p>
            <p style="margin: 0;">
              © ${new Date().getFullYear()} FurnitureBazaar. All rights reserved.
              <br>
              ${currentDate}
            </p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending newsletter welcome email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendNewsletterWelcomeEmail:', error);
    return { success: false, error };
  }
}

export async function sendOrderTrackingUpdateEmail(email, name, order) {
  try {
    // Format the order date
    const orderDate = new Date(order.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Format the shipped date if available
    const shippedDate = order.shippedAt
      ? new Date(order.shippedAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      : 'Not available';

    // Create tracking link HTML if URL is provided
    const trackingLinkHtml = order.trackingUrl
      ? `<a href="${order.trackingUrl}" style="${buttonStyle}" target="_blank">Track Your Package</a>`
      : '';

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: email,
      subject: `Tracking Information for Order #${order._id.toString().slice(-6)}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #333;">Your Order Has Shipped!</h1>
          <p>Dear ${name},</p>
          <p>Great news! Your order is on its way to you. Here's your tracking information:</p>

          <div style="background-color: #f9fafb; padding: 20px; margin: 20px 0;">
            <h2 style="font-size: 16px; margin-top: 0;">Shipping Details</h2>
            <p><strong>Order Number:</strong> #${order._id.toString().slice(-6)}</p>
            <p><strong>Order Date:</strong> ${orderDate}</p>
            <p><strong>Shipped Date:</strong> ${shippedDate}</p>
            <p><strong>Tracking Number:</strong> ${order.trackingNumber}</p>
            ${order.trackingCompany ? `<p><strong>Carrier:</strong> ${order.trackingCompany}</p>` : ''}
          </div>

          ${trackingLinkHtml ? `
          <div style="text-align: center; margin: 30px 0;">
            ${trackingLinkHtml}
          </div>
          ` : ''}

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/orders/${order._id.toString()}" style="${buttonStyle}">
              View Order Details
            </a>
          </div>

          <p>If you have any questions about your order, please contact our customer service team.</p>
          <p>Thank you for shopping with FurnitureBazaar!</p>
          <p>Best regards,<br>The FurnitureBazaar Team</p>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending tracking update email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendOrderTrackingUpdateEmail:', error);
    return { success: false, error };
  }
}

export async function sendGiftCardEmail(giftCard) {
  try {
    // Format the expiration date
    const expirationDate = new Date(giftCard.expiresAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Format the amount
    const formattedAmount = giftCard.amount.toLocaleString('en-IN');

    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: giftCard.recipientEmail,
      subject: `You've Received a ₹${formattedAmount} Gift Card from ${giftCard.senderName}!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f9fafb; padding: 30px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; margin-bottom: 10px;">You've Received a Gift Card!</h1>
            <h2 style="color: #4b5563; font-size: 24px; margin-top: 0;">₹${formattedAmount}</h2>
          </div>

          <p>Dear ${giftCard.recipientName},</p>
          <p>${giftCard.senderName} has sent you a FurnitureBazaar gift card worth ₹${formattedAmount}.</p>

          ${giftCard.message ? `<p style="font-style: italic; padding: 15px; background-color: #f3f4f6; border-left: 4px solid #d1d5db;">"${giftCard.message}"</p>` : ''}

          <div style="background-color: #f9fafb; padding: 20px; margin: 20px 0; text-align: center; border: 1px dashed #d1d5db;">
            <h2 style="margin-top: 0; font-size: 18px;">Your Gift Card Code</h2>
            <p style="font-size: 24px; letter-spacing: 2px; font-weight: bold; margin: 15px 0;">${giftCard.code}</p>
            <p style="margin-bottom: 0; color: #6b7280; font-size: 14px;">Valid until ${expirationDate}</p>
          </div>

          <div style="margin: 30px 0;">
            <h3 style="font-size: 16px;">How to Use Your Gift Card:</h3>
            <ol style="color: #4b5563;">
              <li>Visit our website and add items to your cart</li>
              <li>Proceed to checkout</li>
              <li>Enter your gift card code in the payment section</li>
              <li>The gift card amount will be applied to your purchase</li>
            </ol>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/products" style="${buttonStyle}">
              Start Shopping
            </a>
          </div>

          <p>If you have any questions about your gift card, please contact our customer service team.</p>
          <p>Thank you for shopping with FurnitureBazaar!</p>
          <p>Best regards,<br>The FurnitureBazaar Team</p>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280;">
            <p>This gift card is valid for one year from the date of purchase. It cannot be exchanged for cash or returned.</p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending gift card email:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendGiftCardEmail:', error);
    return { success: false, error };
  }
}

export async function sendBulkNewsletter(emails, subject, content) {
  try {
    // Get current date for the footer
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });

    // Create unsubscribe URL
    const unsubscribeUrl = `${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe`;

    // Send the newsletter
    const { data, error } = await resend.emails.send({
      from: 'FurnitureBazaar <<EMAIL>>',
      to: '<EMAIL>', // This is a placeholder
      bcc: emails, // Use BCC for bulk sending to protect subscriber privacy
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <!-- Header with logo -->
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333; text-transform: uppercase; letter-spacing: 2px; font-size: 24px;">FurnitureBazaar</h1>
          </div>

          <!-- Newsletter content -->
          <div style="margin-bottom: 30px;">
            ${content}
          </div>

          <!-- Call to action button -->
          <div style="text-align: center; margin: 40px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/products" style="${buttonStyle}">
              SHOP NOW
            </a>
          </div>

          <!-- Footer with unsubscribe link -->
          <div style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280; text-align: center;">
            <p>
              You're receiving this email because you subscribed to our newsletter.
              <br>
              <a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: underline;">
                Unsubscribe
              </a>
            </p>
            <p>
              © ${new Date().getFullYear()} FurnitureBazaar. All rights reserved.
              <br>
              ${currentDate}
            </p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Error sending bulk newsletter:', error);
      throw error;
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in sendBulkNewsletter:', error);
    return { success: false, error };
  }
}
