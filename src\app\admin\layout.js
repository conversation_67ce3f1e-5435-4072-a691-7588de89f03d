'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { FiX, FiMenu } from 'react-icons/fi';

export default function AdminLayout({ children }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { user, loading, logout } = useAuth();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Only redirect if we're done loading and the user is not an admin
    if (mounted && !loading && (!user || user.role !== 'admin')) {
      router.push('/login');
    }
  }, [mounted, loading, user, router]);

  // Show nothing while loading or if user is not an admin
  if (!mounted || loading || !user || user.role !== 'admin') {
    // You could return a loading indicator here instead of null
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-sm tracking-[0.2em]">LOADING...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Top Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-black/10">
        <div className="flex items-center justify-between px-6 py-4">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            {isSidebarOpen ? 'CLOSE' : 'MENU'}
          </button>
          <Link href="/" className="text-xs tracking-[0.2em]">
            FURNITUREBAZAAR
          </Link>
          <button
            onClick={async () => {
              await logout();
              // No need to push to login here as the logout function already handles it
            }}
            className="text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            LOGOUT
          </button>
        </div>
      </nav>

      {/* Sidebar Menu */}
      {isSidebarOpen && (
        <div className="fixed inset-0 z-30 bg-white">
          <div className="pt-20 px-6">
            <div className="space-y-6">
              <Link
                href="/admin"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                DASHBOARD
              </Link>
              <Link
                href="/admin/products"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                PRODUCTS
              </Link>
              <Link
                href="/admin/orders"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                ORDERS
              </Link>
              <Link
                href="/admin/users"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                USERS
              </Link>
              <Link
                href="/admin/contacts"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                CONTACTS
              </Link>
              <Link
                href="/admin/gift-cards"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                GIFT CARDS
              </Link>
              <Link
                href="/admin/newsletter"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                NEWSLETTER
              </Link>
              <Link
                href="/admin/coupons"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                COUPONS
              </Link>
              <Link
                href="/admin/stores"
                className="block text-sm tracking-[0.2em] hover:opacity-50 transition-opacity"
                onClick={() => setIsSidebarOpen(false)}
              >
                STORES
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="pt-16">
        {children}
      </main>
    </div>
  );
}

