'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, FiArrowRight } from 'react-icons/fi';

export default function SustainabilityPage() {
  // Sustainable materials data
  const sustainableMaterials = [
    {
      name: "FSC-Certified Wood",
      description: "Wood sourced from forests that are responsibly managed, socially beneficial, environmentally conscious, and economically viable.",
      percentage: 85
    },
    {
      name: "Recycled Metals",
      description: "Metals reclaimed from post-consumer and post-industrial waste, reducing the need for mining raw materials.",
      percentage: 70
    },
    {
      name: "Organic Textiles",
      description: "Fabrics made from organic cotton, linen, and hemp grown without harmful pesticides or synthetic fertilizers.",
      percentage: 60
    },
    {
      name: "Water-Based Finishes",
      description: "Non-toxic finishes that release minimal VOCs (Volatile Organic Compounds) into your home and the environment.",
      percentage: 90
    }
  ];

  // Certifications data
  const certifications = [
    {
      name: "Forest Stewardship Council (FSC)",
      description: "Ensures that products come from responsibly managed forests that provide environmental, social, and economic benefits."
    },
    {
      name: "Global Organic Textile Standard (GOTS)",
      description: "The worldwide leading textile processing standard for organic fibers, including ecological and social criteria."
    },
    {
      name: "GREENGUARD Gold",
      description: "Certifies products with low chemical emissions, contributing to healthier indoor air quality."
    },
    {
      name: "Cradle to Cradle",
      description: "Recognizes products made with materials that can be safely recycled or composted at the end of their useful life."
    }
  ];

  // Environmental impact data
  const environmentalImpact = [
    {
      metric: "Carbon Footprint Reduction",
      value: "32%",
      description: "Reduction in carbon emissions compared to industry standard manufacturing processes."
    },
    {
      metric: "Water Conservation",
      value: "1.2M",
      description: "Liters of water saved annually through efficient manufacturing processes."
    },
    {
      metric: "Waste Reduction",
      value: "85%",
      description: "Of manufacturing waste diverted from landfills through recycling and upcycling programs."
    },
    {
      metric: "Renewable Energy",
      value: "60%",
      description: "Of our energy needs met through renewable sources like solar and wind power."
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section - Zara Style */}
      <section className="relative py-24 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p 
              className="text-xs tracking-[0.3em] mb-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              OUR COMMITMENT
            </motion.p>
            <motion.h1 
              className="text-2xl md:text-3xl font-light uppercase tracking-wide mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Sustainability
            </motion.h1>
            <motion.p 
              className="text-sm text-gray-600"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Our approach to creating beautiful furniture while respecting our planet and its resources.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <div className="max-w-none">
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                {/* Introduction */}
                <p className="text-sm text-gray-600 mb-12">
                  At FurnitureBazaar, sustainability isn't just a buzzword—it's a core value that guides every decision we make. 
                  From the materials we source to the manufacturing processes we employ, we're committed to minimizing our 
                  environmental footprint while creating furniture that stands the test of time.
                </p>

                {/* Our Sustainability Commitment */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Our Sustainability Commitment</h2>
                <p className="text-sm text-gray-600 mb-4">
                  We believe that beautiful furniture shouldn't come at the expense of our planet. That's why we've made a 
                  commitment to sustainability across our entire business, from design and sourcing to manufacturing and delivery.
                </p>
                <p className="text-sm text-gray-600 mb-12">
                  Our sustainability strategy focuses on three key pillars: responsible sourcing, efficient manufacturing, 
                  and circular design. By addressing these areas, we aim to create furniture that not only enhances your 
                  home but also contributes to a healthier planet.
                </p>

                {/* Sustainable Materials */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Sustainable Materials</h2>
                <p className="text-sm text-gray-600 mb-8">
                  We carefully select materials that minimize environmental impact while maintaining the highest quality 
                  standards. Here's how we're incorporating sustainable materials into our products:
                </p>

                <div className="space-y-8 mb-12">
                  {sustainableMaterials.map((material, index) => (
                    <div key={index} className="border-t border-black/5 pt-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-base font-light">{material.name}</h3>
                        <span className="text-sm">{material.percentage}%</span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{material.description}</p>
                      <div className="h-1 bg-gray-100 w-full">
                        <div 
                          className="h-1 bg-black" 
                          style={{ width: `${material.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Manufacturing Process */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Manufacturing Process</h2>
                <p className="text-sm text-gray-600 mb-4">
                  Our manufacturing processes are designed to minimize waste, reduce energy consumption, and limit the use 
                  of harmful chemicals. We continuously innovate to make our production more efficient and environmentally friendly.
                </p>
                <div className="border border-black/10 p-6 mb-12">
                  <h3 className="text-base font-light mb-4">Key Manufacturing Initiatives:</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Zero-Waste Design:</span> Our patterns are optimized to minimize material waste during cutting.
                      </p>
                    </li>
                    <li className="flex items-start">
                      <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Energy Efficiency:</span> Our factories use energy-efficient machinery and lighting to reduce power consumption.
                      </p>
                    </li>
                    <li className="flex items-start">
                      <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Water Conservation:</span> Closed-loop water systems recycle and reuse water in our production facilities.
                      </p>
                    </li>
                    <li className="flex items-start">
                      <FiCheck className="text-black mt-1 mr-3 flex-shrink-0" />
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Waste Management:</span> Manufacturing byproducts are recycled or repurposed whenever possible.
                      </p>
                    </li>
                  </ul>
                </div>

                {/* Certifications */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Certifications</h2>
                <p className="text-sm text-gray-600 mb-8">
                  We work with recognized certification bodies to ensure our products meet the highest standards of 
                  sustainability and social responsibility. These certifications provide third-party verification of our commitment.
                </p>

                <div className="space-y-6 mb-12">
                  {certifications.map((cert, index) => (
                    <div key={index} className="border-t border-black/5 pt-6">
                      <h3 className="text-base font-light mb-2">{cert.name}</h3>
                      <p className="text-sm text-gray-600">{cert.description}</p>
                    </div>
                  ))}
                </div>

                {/* Environmental Impact */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Environmental Impact</h2>
                <p className="text-sm text-gray-600 mb-8">
                  We measure our environmental impact and set ambitious targets for improvement. Here's how our 
                  sustainability initiatives are making a difference:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
                  {environmentalImpact.map((impact, index) => (
                    <div key={index} className="border border-black/10 p-6">
                      <h3 className="text-xs uppercase tracking-wider text-gray-500 mb-2">{impact.metric}</h3>
                      <p className="text-3xl font-light mb-3">{impact.value}</p>
                      <p className="text-sm text-gray-600">{impact.description}</p>
                    </div>
                  ))}
                </div>

                {/* Future Goals */}
                <h2 className="text-xl font-light text-black mb-6 uppercase tracking-wide">Future Goals</h2>
                <p className="text-sm text-gray-600 mb-4">
                  While we're proud of our progress, we recognize that sustainability is a journey, not a destination. 
                  We've set ambitious goals for the future to further reduce our environmental impact:
                </p>
                <ul className="space-y-4 mb-12">
                  <li className="border-t border-black/5 pt-4">
                    <p className="text-sm">
                      <span className="font-medium">2025:</span> Achieve 100% FSC-certified or reclaimed wood across all wooden furniture.
                    </p>
                  </li>
                  <li className="border-t border-black/5 pt-4">
                    <p className="text-sm">
                      <span className="font-medium">2027:</span> Reduce carbon emissions by 50% compared to our 2020 baseline.
                    </p>
                  </li>
                  <li className="border-t border-black/5 pt-4">
                    <p className="text-sm">
                      <span className="font-medium">2030:</span> Transition to 100% renewable energy across all operations and manufacturing facilities.
                    </p>
                  </li>
                  <li className="border-t border-black/5 pt-4">
                    <p className="text-sm">
                      <span className="font-medium">2030:</span> Implement a comprehensive furniture take-back and recycling program.
                    </p>
                  </li>
                </ul>

                {/* Sustainable Collection CTA */}
                <div className="border-t border-black/10 pt-12 mb-12">
                  <h3 className="text-base font-light mb-4">Explore Our Sustainable Collection</h3>
                  <p className="text-sm text-gray-600 mb-6">
                    Discover furniture pieces that combine beautiful design with environmental responsibility. 
                    Our sustainable collection features items crafted from eco-friendly materials using responsible 
                    manufacturing processes.
                  </p>
                  <Link 
                    href="/products?category=sustainable" 
                    className="inline-flex items-center text-sm hover:opacity-70 transition-opacity"
                  >
                    VIEW COLLECTION <FiArrowRight className="ml-2" />
                  </Link>
                </div>
              </motion.div>
            </div>

            {/* Related Links - Zara Style */}
            <div className="mt-16 pt-12 border-t border-black/10">
              <h3 className="text-xs tracking-[0.3em] mb-8">RELATED INFORMATION</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Link 
                  href="/editorial/sustainable-living" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  SUSTAINABLE LIVING EDITORIAL
                </Link>
                <Link 
                  href="/about" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  ABOUT US
                </Link>
                <Link 
                  href="/contact" 
                  className="text-sm hover:opacity-70 transition-opacity"
                >
                  CONTACT US
                </Link>
              </div>
              <div className="mt-12">
                <Link 
                  href="/" 
                  className="text-xs tracking-[0.2em] hover:opacity-70 transition-opacity border-b border-black pb-1"
                >
                  BACK TO HOME
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
