import { NextResponse } from 'next/server';

// Define public and auth routes
const publicRoutes = [
  '/',
  '/products',
  '/about',
  '/contact',
  '/categories',
];

const authRoutes = [
  '/login',
  '/register'
];

const protectedRoutes = [
  '/admin',
  '/profile',
  '/cart',
  '/wishlist',
  '/orders'
];

export async function middleware(request) {
  const { pathname } = request.nextUrl;

  // Allow static files and API routes (except protected ones)
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/images') ||
    pathname.startsWith('/api/products') ||
    pathname.startsWith('/api/categories') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Get session
  const sessionId = request.cookies.get('session')?.value;
  const isAuthenticated = !!sessionId;

  // Check if the path starts with any protected route
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));

  // Check if the path is an auth route (login/register)
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route));

  // If user is authenticated and tries to access auth routes, redirect to home
  if (isAuthenticated && isAuthRoute) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If user is not authenticated and tries to access protected routes
  if (!isAuthenticated && isProtectedRoute) {
    const searchParams = new URLSearchParams({ redirect: pathname });
    return NextResponse.redirect(new URL(`/login?${searchParams}`, request.url));
  }

  // Special handling for admin routes
  if (pathname.startsWith('/admin')) {
    if (!isAuthenticated) {
      return NextResponse.redirect(new URL('/login', request.url));
    }
    // Note: Role-based access is handled in the admin layout component
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};