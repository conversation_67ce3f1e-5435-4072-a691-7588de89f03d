// This script migrates the hardcoded store data to the MongoDB database
// Run with: node src/scripts/migrate-stores.js

import { connectToDB } from '../lib/mongodb.js';
import Store from '../models/Store.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Store data to migrate
const storeData = [
  {
    name: 'FurnitureBazaar Surat',
    city: 'surat',
    address: 'VR Surat Mall, Dumas Road',
    fullAddress: 'VR Surat Mall, Dumas Road, Piplod, Surat, Gujarat 395007',
    phone: '+91 261 4890 5000',
    hours: 'Monday - Sunday: 11:00 AM - 9:00 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service', 'Virtual Shopping'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Flagship Store',
    city: 'mumbai',
    address: 'Phoenix Palladium Mall, Lower Parel',
    fullAddress: '462, Phoenix Palladium, Lower Parel, Mumbai, Maharashtra 400013',
    phone: '+91 22 4971 8000',
    hours: 'Monday - Sunday: 11:00 AM - 9:30 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service', 'Virtual Shopping'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Bandra',
    city: 'mumbai',
    address: 'Linking Road, Bandra West',
    fullAddress: '213, Linking Road, Bandra West, Mumbai, Maharashtra 400050',
    phone: '+91 22 2641 3000',
    hours: 'Monday - Sunday: 10:30 AM - 8:30 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Saket',
    city: 'delhi',
    address: 'Select Citywalk Mall, Saket',
    fullAddress: 'G-25, Select Citywalk, A-3, District Centre, Saket, New Delhi, Delhi 110017',
    phone: '+91 11 4057 9000',
    hours: 'Monday - Sunday: 11:00 AM - 9:00 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service', 'Virtual Shopping'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Gurgaon',
    city: 'delhi',
    address: 'Ambience Mall, Gurgaon',
    fullAddress: 'Ambience Mall, DLF Phase 3, Sector 24, Gurugram, Haryana 122002',
    phone: '+91 124 4665 9000',
    hours: 'Monday - Sunday: 11:00 AM - 9:00 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Indiranagar',
    city: 'bangalore',
    address: '100 Feet Road, Indiranagar',
    fullAddress: '237, 100 Feet Road, Indiranagar, Bengaluru, Karnataka 560038',
    phone: '+91 80 4123 8000',
    hours: 'Monday - Sunday: 10:30 AM - 8:30 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service', 'Virtual Shopping'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  },
  {
    name: 'FurnitureBazaar Whitefield',
    city: 'bangalore',
    address: 'Phoenix Marketcity, Whitefield',
    fullAddress: 'Phoenix Marketcity, Whitefield Main Road, Mahadevapura, Bengaluru, Karnataka 560048',
    phone: '+91 80 6726 6000',
    hours: 'Monday - Sunday: 11:00 AM - 9:00 PM',
    features: ['Design Consultation', 'Home Delivery', 'Assembly Service'],
    image: 'https://cdn.cosmos.so/70f6fa65-c167-4528-90c9-a4e547d8e22e?format=jpeg',
    status: 'active'
  }
];

async function migrateStores() {
  try {
    // Connect to MongoDB
    await connectToDB();
    console.log('Connected to MongoDB');

    // Check if stores already exist
    const existingStores = await Store.countDocuments();
    if (existingStores > 0) {
      console.log(`Found ${existingStores} existing stores. Skipping migration.`);
      console.log('To force migration, please clear the stores collection first.');
      process.exit(0);
    }

    // Insert store data
    const result = await Store.insertMany(storeData);
    console.log(`Successfully migrated ${result.length} stores to the database.`);
    process.exit(0);
  } catch (error) {
    console.error('Error migrating stores:', error);
    process.exit(1);
  }
}

// Run the migration
migrateStores();
