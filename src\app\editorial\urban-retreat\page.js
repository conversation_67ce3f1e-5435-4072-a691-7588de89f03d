'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

export default function UrbanRetreat() {
  const sections = [
    {
      type: 'hero',
      image: '/images/editorial/urban-retreat-hero.jpg',
      title: 'URBAN RETREAT',
      subtitle: 'EDITORIAL 2024'
    },
    {
      type: 'text',
      content: 'In the heart of the city, find your sanctuary. Our Urban Retreat collection transforms metropolitan living spaces into peaceful havens, where contemporary design meets tranquil comfort.'
    },
    {
      type: 'full-width-image',
      image: '/images/editorial/urban-living-room.jpg',
      caption: 'Modern serenity in urban spaces'
    },
    {
      type: 'image-text',
      image: '/images/editorial/urban-materials.jpg',
      title: 'CONTEMPORARY MATERIALS',
      description: "Luxurious textures and modern materials combine to create spaces that feel both sophisticated and welcoming. Each piece is carefully selected to bring calm to city living.",
      products: [
        { name: 'MODULAR LOUNGE SOFA', price: '₹125,999', link: '/products/modular-sofa' },
        { name: 'MARBLE SIDE TABLE', price: '₹45,999', link: '/products/marble-table' }
      ]
    },
    {
      type: 'split-image',
      images: [
        {
          src: '/images/editorial/urban-detail1.jpg',
          caption: 'Textural elements'
        },
        {
          src: '/images/editorial/urban-detail2.jpg',
          caption: 'Modern forms'
        }
      ]
    },
    {
      type: 'text-overlay-image',
      image: '/images/editorial/urban-mood.jpg',
      title: 'URBAN MOOD',
      text: 'Where city energy meets home tranquility'
    },
    {
      type: 'product-showcase',
      title: 'THE COLLECTION',
      products: [
        {
          name: 'SCULPTURAL ACCENT CHAIR',
          price: '₹68,999',
          image: '/images/editorial/urban-chair.jpg',
          link: '/products/accent-chair'
        },
        {
          name: 'FLOATING MEDIA UNIT',
          price: '₹89,999',
          image: '/images/editorial/urban-media.jpg',
          link: '/products/media-unit'
        }
      ]
    },
    {
      type: 'full-bleed-text',
      content: 'CREATING CALM IN THE CHAOS OF CITY LIFE'
    },
    {
      type: 'image-grid',
      images: [
        {
          src: '/images/editorial/urban-detail3.jpg',
          caption: 'Minimalist storage'
        },
        {
          src: '/images/editorial/urban-detail4.jpg',
          caption: 'Ambient lighting'
        },
        {
          src: '/images/editorial/urban-detail5.jpg',
          caption: 'Textural contrasts'
        }
      ]
    }
  ];

  return (
    <div className="bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md">
        <div className="container mx-auto px-4">
          <div className="h-16 flex items-center justify-between">
            <Link
              href="/editorial"
              className="flex items-center text-sm tracking-wider hover:opacity-70"
            >
              <FiArrowLeft className="mr-2" />
              BACK TO EDITORIALS
            </Link>
            <Link
              href="/"
              className="text-sm tracking-wider hover:opacity-70"
            >
              FURNITUREBAAZAR
            </Link>
            <div className="w-[100px]" />
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {sections.map((section, index) => {
          switch (section.type) {
            case 'hero':
              return (
                <section key={index} className="relative h-screen">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-sm tracking-[0.2em] mb-4"
                    >
                      {section.subtitle}
                    </motion.p>
                    <motion.h1
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-4xl md:text-6xl tracking-[0.1em] text-center font-light px-4"
                    >
                      {section.title}
                    </motion.h1>
                  </div>
                </section>
              );

            case 'text':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <motion.p
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="max-w-3xl mx-auto text-center text-lg text-gray-600 leading-relaxed"
                    >
                      {section.content}
                    </motion.p>
                  </div>
                </section>
              );

            case 'split-image':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      {section.images.map((image, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: true }}
                          transition={{ delay: idx * 0.2 }}
                          className="relative aspect-square"
                        >
                          <Image
                            src={image.src}
                            alt={image.caption}
                            fill
                            className="object-cover"
                          />
                          <p className="absolute bottom-4 left-4 text-sm text-white">
                            {image.caption}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </section>
              );

            case 'text-overlay-image':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <div className="relative aspect-[21/9]">
                      <Image
                        src={section.image}
                        alt={section.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 flex items-center justify-center text-white">
                        <div className="text-center">
                          <h2 className="text-3xl md:text-4xl tracking-[0.1em] font-light mb-4">
                            {section.title}
                          </h2>
                          <p className="text-lg tracking-wider">{section.text}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              );

            case 'full-bleed-text':
              return (
                <section key={index} className="py-24 bg-black text-white">
                  <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-5xl tracking-[0.1em] text-center font-light">
                      {section.content}
                    </h2>
                  </div>
                </section>
              );

            // ... other cases remain the same as in sustainable-living page
          }
        })}
      </main>

      {/* Next Editorial Navigation */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <Link
            href="/editorial/minimalist-living"
            className="group block relative aspect-[16/9] overflow-hidden"
          >
            <Image
              src="/images/editorial/minimalist-hero.jpg"
              alt="Next Editorial"
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors" />
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
              <p className="text-sm tracking-[0.2em] mb-4">NEXT EDITORIAL</p>
              <h2 className="text-4xl tracking-[0.1em] font-light mb-8">
                THE ART OF MINIMALIST LIVING
              </h2>
              <FiArrowRight className="text-2xl" />
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}