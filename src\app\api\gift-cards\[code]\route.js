import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';
import mongoose from 'mongoose';

// Get gift card details by code
export async function GET(request, context) {
  try {
    const params = await context.params;
    const { code } = params;

    if (!code) {
      return NextResponse.json(
        { error: 'Gift card code is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await connectToDB();

    // Find gift card
    const giftCard = await GiftCard.findOne({ code: code.toUpperCase() });

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    // Check if user is authorized to view this gift card
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Only allow the purchaser, recipient, or admin to view the gift card
    const isAdmin = user.role === 'admin';
    const isPurchaser = giftCard.purchasedBy.toString() === user._id.toString();
    const isRecipient = giftCard.redeemedBy && giftCard.redeemedBy.toString() === user._id.toString();
    const isRecipientEmail = giftCard.recipientEmail.toLowerCase() === user.email.toLowerCase();

    if (!isAdmin && !isPurchaser && !isRecipient && !isRecipientEmail) {
      return NextResponse.json(
        { error: 'Not authorized to view this gift card' },
        { status: 403 }
      );
    }

    return NextResponse.json(giftCard);
  } catch (error) {
    console.error('Error fetching gift card:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gift card' },
      { status: 500 }
    );
  }
}

// Redeem gift card
export async function PUT(request, context) {
  try {
    const params = await context.params;
    const { code } = params;

    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { amount, orderId } = body;

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    // Find gift card
    const giftCard = await GiftCard.findOne({ code: code.toUpperCase() });

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    // Check if gift card is valid
    if (!giftCard.isValid()) {
      return NextResponse.json(
        { 
          error: giftCard.status !== 'active' 
            ? `Gift card is ${giftCard.status}` 
            : giftCard.balance <= 0 
              ? 'Gift card has no remaining balance' 
              : 'Gift card has expired'
        },
        { status: 400 }
      );
    }

    // Check if amount is valid
    if (amount > giftCard.balance) {
      return NextResponse.json(
        { 
          error: 'Insufficient balance on gift card',
          availableBalance: giftCard.balance
        },
        { status: 400 }
      );
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Redeem gift card
      giftCard.balance -= amount;
      
      if (giftCard.balance === 0) {
        giftCard.status = 'redeemed';
        giftCard.redeemedAt = new Date();
      }
      
      if (!giftCard.redeemedBy) {
        giftCard.redeemedBy = user._id;
      }
      
      if (orderId) {
        giftCard.orderIds.push(orderId);
      }
      
      await giftCard.save({ session });

      // Commit transaction
      await session.commitTransaction();
      session.endSession();

      return NextResponse.json({
        message: 'Gift card redeemed successfully',
        giftCard
      });
    } catch (error) {
      // Abort transaction on error
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error) {
    console.error('Error redeeming gift card:', error);
    return NextResponse.json(
      { error: 'Failed to redeem gift card' },
      { status: 500 }
    );
  }
}

// Admin only: Update gift card status
export async function PATCH(request, context) {
  try {
    const params = await context.params;
    const { code } = params;

    // Check authentication
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { status } = body;

    if (!status || !['active', 'cancelled', 'expired'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required' },
        { status: 400 }
      );
    }

    // Find gift card
    const giftCard = await GiftCard.findOne({ code: code.toUpperCase() });

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    // Update status
    giftCard.status = status;
    await giftCard.save();

    return NextResponse.json({
      message: 'Gift card updated successfully',
      giftCard
    });
  } catch (error) {
    console.error('Error updating gift card:', error);
    return NextResponse.json(
      { error: 'Failed to update gift card' },
      { status: 500 }
    );
  }
}
