'use client';

import { useState, useRef } from 'react';
import { upload } from '@imagekit/next';
import { toast } from 'react-hot-toast';
import Image from 'next/image';

export default function ImageKitUpload({ images = [], onImagesChange }) {
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef(null);

  const authenticator = async () => {
    try {
      const response = await fetch('/api/imagekit/auth');
      if (!response.ok) {
        throw new Error('Failed to authenticate');
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Authentication error:', error);
      toast.error('Failed to authenticate upload');
      throw error;
    }
  };

  const handleUpload = async () => {
    const fileInput = fileInputRef.current;
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
      toast.error('Please select a file to upload');
      return;
    }

    const file = fileInput.files[0];
    setUploading(true);
    toast.loading('Uploading image...', { id: 'upload' });

    try {
      // Get authentication parameters
      const authParams = await authenticator();
      const { signature, expire, token, publicKey } = authParams;

      // Upload the file
      const uploadResponse = await upload({
        file,
        fileName: file.name,
        signature,
        expire,
        token,
        publicKey,
        folder: '/products',
        useUniqueFileName: true,
        isPrivateFile: false,
        onProgress: (event) => {
          const progress = (event.loaded / event.total) * 100;
          console.log('Upload progress:', progress);
        },
      });

      console.log('Upload success:', uploadResponse);

      // Add the uploaded image URL to the images array
      const newImages = [...images, uploadResponse.url];
      onImagesChange(newImages);

      toast.dismiss('upload');
      toast.success('Image uploaded successfully');
    } catch (error) {
      console.error('Upload error:', error);
      toast.dismiss('upload');
      toast.error('Failed to upload image');
    } finally {
      setUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeImage = (index) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  };

  const triggerUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="space-y-8">
      {/* Upload Button */}
      <div className="space-y-4">
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={triggerUpload}
            disabled={uploading}
            className="flex-1 bg-transparent border-b border-black/10 px-0 py-2 text-left text-sm hover:border-black disabled:opacity-50"
          >
            {uploading ? 'UPLOADING...' : 'CLICK TO UPLOAD IMAGE'}
          </button>
          <button
            type="button"
            onClick={handleUpload}
            disabled={uploading}
            className="text-sm tracking-widest hover:opacity-70 disabled:opacity-50"
          >
            UPLOAD
          </button>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleUpload}
          className="hidden"
        />
      </div>

      {/* Image Grid */}
      <div className="grid grid-cols-2 gap-4">
        {images.map((image, index) => (
          <div key={index} className="relative aspect-[3/4] bg-gray-50">
            <Image
              src={image}
              alt={`Product ${index + 1}`}
              fill
              className="object-cover"
            />
            <button
              type="button"
              onClick={() => removeImage(index)}
              className="absolute top-2 right-2 bg-black text-white text-xs tracking-widest px-3 py-1 hover:opacity-70"
            >
              REMOVE
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
