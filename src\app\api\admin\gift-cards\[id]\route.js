import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';
import { sendGiftCardEmail } from '@/lib/email';

// Get gift card by ID (admin only)
export async function GET(request, context) {
  try {
    const params = await context.params;
    const { id } = params;

    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Find gift card
    const giftCard = await GiftCard.findById(id)
      .populate('purchasedBy', 'name email')
      .populate('redeemedBy', 'name email');

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(giftCard);
  } catch (error) {
    console.error('Error fetching gift card:', error);
    return NextResponse.json(
      { error: 'Failed to fetch gift card' },
      { status: 500 }
    );
  }
}

// Update gift card (admin only)
export async function PATCH(request, context) {
  try {
    const params = await context.params;
    const { id } = params;

    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { 
      status, 
      balance, 
      expiresAt,
      sendEmail = false
    } = body;

    // Find gift card
    const giftCard = await GiftCard.findById(id);

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    // Update fields if provided
    if (status) {
      giftCard.status = status;
    }

    if (balance !== undefined && balance >= 0) {
      giftCard.balance = balance;
    }

    if (expiresAt) {
      giftCard.expiresAt = new Date(expiresAt);
    }

    // Save gift card
    const updatedGiftCard = await giftCard.save();

    // Send email if requested
    if (sendEmail && updatedGiftCard.status === 'active') {
      await sendGiftCardEmail(updatedGiftCard);
    }

    return NextResponse.json(updatedGiftCard);
  } catch (error) {
    console.error('Error updating gift card:', error);
    return NextResponse.json(
      { error: 'Failed to update gift card' },
      { status: 500 }
    );
  }
}

// Delete gift card (admin only)
export async function DELETE(request, context) {
  try {
    const params = await context.params;
    const { id } = params;

    // Check authentication and authorization
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    if (user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Not authorized' },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDB();

    // Find and delete gift card
    const giftCard = await GiftCard.findByIdAndDelete(id);

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Gift card not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Gift card deleted successfully' });
  } catch (error) {
    console.error('Error deleting gift card:', error);
    return NextResponse.json(
      { error: 'Failed to delete gift card' },
      { status: 500 }
    );
  }
}
