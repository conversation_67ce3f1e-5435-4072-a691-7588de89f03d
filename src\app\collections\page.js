'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';

export default function CollectionsPage() {
  const [hoveredCollection, setHoveredCollection] = useState(null);

  const collections = [
    {
      id: 'summer-2024',
      name: 'SUMMER 2024',
      title: 'Mediterranean Dreams',
      description: 'Inspired by the sun-drenched coastlines of Southern Europe',
      image: '/images/collections/summer-2024.jpg',
      video: '/videos/summer-2024.mp4', // Optional video for hero collection
      isHero: true
    },
    {
      id: 'minimalist',
      name: 'THE MINIMALIST',
      title: 'Less is More',
      description: 'Clean lines and neutral tones for modern living',
      image: '/images/collections/minimalist.jpg'
    },
    {
      id: 'artisan',
      name: 'ARTISAN COLLECTION',
      title: 'Handcrafted Excellence',
      description: 'Traditional craftsmanship meets contemporary design',
      image: '/images/collections/artisan.jpg'
    },
    {
      id: 'outdoor-living',
      name: 'OUTDOOR LIVING',
      title: '<PERSON> Fres<PERSON>',
      description: 'Transform your outdoor space into a private paradise',
      image: '/images/collections/outdoor.jpg'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Collection */}
      {collections.find(c => c.isHero) && (
        <div className="relative h-screen">
          {/* Video Background (if available) */}
          {collections[0].video ? (
            <video
              autoPlay
              loop
              muted
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src={collections[0].video} type="video/mp4" />
            </video>
          ) : (
            <Image
              src={collections[0].image}
              alt={collections[0].name}
              fill
              className="object-cover"
              priority
            />
          )}
          
          {/* Hero Content */}
          <div className="absolute inset-0 flex flex-col justify-end p-8 md:p-16 text-white bg-gradient-to-t from-black/50 to-transparent">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <h1 className="text-6xl md:text-8xl font-light mb-4">
                {collections[0].title}
              </h1>
              <p className="text-lg md:text-xl mb-8 max-w-2xl">
                {collections[0].description}
              </p>
              <Link
                href={`/collections/${collections[0].id}`}
                className="inline-flex items-center space-x-4 text-lg group"
              >
                <span className="tracking-wider">DISCOVER</span>
                <FiArrowRight className="w-6 h-6 transform group-hover:translate-x-2 transition-transform" />
              </Link>
            </motion.div>
          </div>
        </div>
      )}

      {/* Collections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2">
        {collections.filter(c => !c.isHero).map((collection) => (
          <Link
            key={collection.id}
            href={`/collections/${collection.id}`}
            className="relative group"
            onMouseEnter={() => setHoveredCollection(collection.id)}
            onMouseLeave={() => setHoveredCollection(null)}
          >
            <div className="relative aspect-[3/4] overflow-hidden">
              <Image
                src={collection.image}
                alt={collection.name}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-105"
              />
              
              {/* Hover Overlay */}
              <div 
                className={`absolute inset-0 bg-black transition-opacity duration-500 ${
                  hoveredCollection === collection.id ? 'opacity-30' : 'opacity-0'
                }`}
              />

              {/* Collection Info */}
              <div className="absolute inset-0 flex flex-col justify-between p-8 md:p-12 text-white">
                <h2 className="text-2xl tracking-wider">{collection.name}</h2>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ 
                    opacity: hoveredCollection === collection.id ? 1 : 0,
                    y: hoveredCollection === collection.id ? 0 : 20 
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <h3 className="text-4xl md:text-5xl font-light mb-4">
                    {collection.title}
                  </h3>
                  <p className="text-lg mb-6">
                    {collection.description}
                  </p>
                  <div className="inline-flex items-center space-x-4 group/arrow">
                    <span className="tracking-wider">EXPLORE</span>
                    <FiArrowRight className="w-6 h-6 transform group-hover/arrow:translate-x-2 transition-transform" />
                  </div>
                </motion.div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Newsletter Section */}
      <div className="py-24 px-8 text-center">
        <h2 className="text-2xl tracking-wider mb-8">JOIN OUR NEWSLETTER</h2>
        <div className="max-w-md mx-auto">
          <form className="flex gap-4">
            <input
              type="email"
              placeholder="Your email address"
              className="flex-1 px-4 py-3 border-b border-black focus:outline-none"
            />
            <button
              type="submit"
              className="px-8 py-3 bg-black text-white hover:bg-black/90 transition-colors"
            >
              SUBSCRIBE
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}