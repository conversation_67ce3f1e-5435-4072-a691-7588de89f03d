'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function CategoriesPage() {
  const [hoveredCategory, setHoveredCategory] = useState(null);

  const categories = [
    {
      id: 'living-room',
      name: 'LIVING ROOM',
      image: 'https://cdn.cosmos.so/f34d857e-4e2d-4547-b4bf-f5f7ef452026?format=jpeg',
      subCategories: ['Sofas', 'Coffee Tables', 'TV Units', 'Seating']
    },
    {
      id: 'bedroom',
      name: 'BEDROOM',
      image: 'https://cdn.cosmos.so/7c95c80e-765d-489a-bdea-0a53f81da028?format=jpeg',
      subCategories: ['Beds', 'Wardrobes', 'Nightstands', 'Mattresses']
    },
    {
      id: 'dining-room',
      name: 'DINING ROOM',
      image: 'https://cdn.cosmos.so/99cd2099-fa70-40d9-8807-68f27ef34db8?format=jpeg',
      subCategories: ['Dining Tables', 'Dining Chairs', 'Buffets', 'Bar Furniture']
    },
    {
      id: 'office',
      name: 'OFFICE',
      image: 'https://cdn.cosmos.so/6df276bf-a867-4aa6-8023-4287c456cd6d?format=jpeg',
      subCategories: ['Desks', 'Office Chairs', 'Bookcases', 'Filing Cabinets']
    },
    {
      id: 'outdoor',
      name: 'OUTDOOR',
      image: 'https://cdn.cosmos.so/feca3c52-760e-49e1-8080-8b3d32aa2013?format=jpeg',
      subCategories: ['Garden Sets', 'Loungers', 'Outdoor Dining', 'Planters']
    },
    {
      id: 'decor',
      name: 'DECOR',
      image: 'https://cdn.cosmos.so/3e6f284e-43c6-42d0-a83f-0f520ccaba7c?format=jpeg',
      subCategories: ['Mirrors', 'Rugs', 'Lighting', 'Wall Art']
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Main Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={`/products?category=${category.id}`}
            className="relative group"
            onMouseEnter={() => setHoveredCategory(category.id)}
            onMouseLeave={() => setHoveredCategory(null)}
          >
            {/* Image Container */}
            <div className="relative aspect-[4/5] w-full overflow-hidden">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover transition-transform duration-700 group-hover:scale-105"
                priority
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:opacity-0" />
              
              {/* Category Name */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ 
                  opacity: hoveredCategory === category.id ? 1 : 0,
                  y: hoveredCategory === category.id ? 0 : 20 
                }}
                transition={{ duration: 0.3 }}
                className="absolute inset-0 flex flex-col items-center justify-center text-white"
              >
                <h2 className="text-4xl font-light tracking-wider mb-6">
                  {category.name}
                </h2>
                
                {/* Sub-categories */}
                <div className="flex flex-col items-center gap-2">
                  {category.subCategories.map((subCategory) => (
                    <span
                      key={subCategory}
                      className="text-sm tracking-wider hover:underline cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault();
                        // Navigate to specific sub-category
                        window.location.href = `/products?category=${category.id}&subcategory=${subCategory.toLowerCase()}`;
                      }}
                    >
                      {subCategory}
                    </span>
                  ))}
                </div>
              </motion.div>

              {/* Default Category Name (shown when not hovered) */}
              <motion.div
                initial={{ opacity: 1 }}
                animate={{ 
                  opacity: hoveredCategory === category.id ? 0 : 1
                }}
                transition={{ duration: 0.3 }}
                className="absolute inset-0 flex items-center justify-center text-white"
              >
                <h2 className="text-4xl font-light tracking-wider">
                  {category.name}
                </h2>
              </motion.div>
            </div>
          </Link>
        ))}
      </div>

      {/* View All Button */}
      <div className="py-12 text-center">
        <Link
          href="/products"
          className="inline-block px-12 py-4 border border-black text-sm tracking-wider hover:bg-black hover:text-white transition-colors"
        >
          VIEW ALL PRODUCTS
        </Link>
      </div>
    </div>
  );
}