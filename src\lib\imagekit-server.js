import crypto from 'crypto';

export function getUploadAuthParams({ privateKey, publicKey, expire, token }) {
  // Generate default values if not provided
  const defaultExpire = expire || Math.floor(Date.now() / 1000) + 30 * 60; // 30 minutes from now
  const defaultToken = token || crypto.randomUUID();
  
  // Create signature
  const signature = crypto
    .createHmac('sha1', privateKey)
    .update(defaultToken + defaultExpire)
    .digest('hex');

  return {
    token: defaultToken,
    expire: defaultExpire,
    signature,
    publicKey
  };
}
