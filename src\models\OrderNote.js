import mongoose from 'mongoose';

const orderNoteSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  text: {
    type: String,
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  isInternal: {
    type: Boolean,
    default: true
  }
});

const OrderNote = mongoose.models.OrderNote || mongoose.model('OrderNote', orderNoteSchema);

export default OrderNote;
