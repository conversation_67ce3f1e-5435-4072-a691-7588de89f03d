'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function UnsubscribePage() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUnsubscribed, setIsUnsubscribed] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/newsletter/unsubscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: email.toLowerCase().trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to unsubscribe');
      }

      toast.success(data.message || 'Successfully unsubscribed from newsletter');
      setIsUnsubscribed(true);
      setEmail('');
    } catch (error) {
      toast.error(error.message);
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white py-16">
      <div className="container mx-auto px-4 max-w-md">
        <h1 className="text-2xl font-medium mb-8 text-center">Unsubscribe from Newsletter</h1>
        
        {isUnsubscribed ? (
          <div className="text-center">
            <p className="mb-4">You have been successfully unsubscribed from our newsletter.</p>
            <p className="text-sm text-gray-500">
              We're sorry to see you go. If you change your mind, you can always subscribe again in the future.
            </p>
          </div>
        ) : (
          <>
            <p className="mb-6 text-center text-gray-600">
              Please enter your email address to unsubscribe from our newsletter.
            </p>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    setError('');
                  }}
                  placeholder="Enter your email address"
                  className="w-full bg-transparent border border-gray-300 px-4 py-2 
                    text-sm focus:ring-0 focus:border-black"
                  required
                />
                
                {error && (
                  <p className="text-xs text-red-500">{error}</p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-black text-white py-3 text-xs tracking-widest
                  hover:bg-black/90 disabled:bg-black/70 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'PROCESSING...' : 'UNSUBSCRIBE'}
              </button>
            </form>
          </>
        )}
      </div>
    </div>
  );
}
