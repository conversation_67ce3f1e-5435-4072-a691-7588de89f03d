'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

export default function MinimalistLiving() {
  const sections = [
    {
      type: 'hero',
      image: '/images/editorial/minimalist-hero.jpg',
      title: 'THE ART OF MINIMALIST LIVING',
      subtitle: 'EDITORIAL 2024'
    },
    {
      type: 'text',
      content: 'In an era of excess, minimalism stands as a testament to intentional living. Our latest collection embraces the philosophy that less is more, featuring carefully curated pieces that combine form and function in perfect harmony.'
    },
    {
      type: 'image-text',
      image: '/images/editorial/minimal-living-room.jpg',
      title: 'LIVING ROOM',
      description: 'Clean lines and neutral tones create a sense of calm and order. Each piece is selected for both its aesthetic value and practical purpose.',
      products: [
        { name: 'OSLO SOFA', price: '₹89,999', link: '/products/oslo-sofa' },
        { name: '<PERSON><PERSON> COFFEE TABLE', price: '₹24,999', link: '/products/zen-coffee-table' }
      ]
    },
    {
      type: 'full-width-image',
      image: '/images/editorial/minimal-detail.jpg',
      caption: 'Details matter in minimalist design'
    },
    {
      type: 'image-text',
      image: '/images/editorial/minimal-bedroom.jpg',
      title: 'BEDROOM',
      description: 'The bedroom becomes a sanctuary of peace when furnished with intention. Natural materials and subtle textures create depth without disruption.',
      products: [
        { name: 'LUNA BED', price: '₹64,999', link: '/products/luna-bed' },
        { name: 'DRIFT SIDE TABLE', price: '₹18,999', link: '/products/drift-side-table' }
      ]
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link 
            href="/editorial"
            className="text-sm tracking-[0.2em] hover:opacity-60 transition-opacity flex items-center"
          >
            <FiArrowLeft className="mr-2" />
            BACK TO EDITORIAL
          </Link>
        </div>
      </nav>

      {/* Main Content */}
      <main className="pt-16">
        {sections.map((section, index) => {
          switch (section.type) {
            case 'hero':
              return (
                <section key={index} className="relative h-screen">
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover"
                    priority
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-sm tracking-[0.2em] mb-4"
                    >
                      {section.subtitle}
                    </motion.p>
                    <motion.h1
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-4xl md:text-5xl tracking-[0.1em] text-center font-light"
                    >
                      {section.title}
                    </motion.h1>
                  </div>
                </section>
              );

            case 'text':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <motion.p
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="max-w-2xl mx-auto text-center text-lg leading-relaxed"
                    >
                      {section.content}
                    </motion.p>
                  </div>
                </section>
              );

            case 'image-text':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        className="relative aspect-[4/5]"
                      >
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                        />
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        className="space-y-8"
                      >
                        <h2 className="text-2xl tracking-[0.2em]">{section.title}</h2>
                        <p className="text-gray-600">{section.description}</p>
                        <div className="space-y-4">
                          {section.products.map((product, idx) => (
                            <Link
                              key={idx}
                              href={product.link}
                              className="block group"
                            >
                              <div className="flex justify-between items-center py-2 border-b border-gray-200">
                                <span className="text-sm tracking-[0.1em]">{product.name}</span>
                                <div className="flex items-center">
                                  <span className="text-sm mr-2">{product.price}</span>
                                  <FiArrowRight className="opacity-0 group-hover:opacity-100 transition-opacity" />
                                </div>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </section>
              );

            case 'full-width-image':
              return (
                <section key={index} className="py-24">
                  <div className="container mx-auto px-4">
                    <motion.div
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      className="relative aspect-[16/9]"
                    >
                      <Image
                        src={section.image}
                        alt={section.caption}
                        fill
                        className="object-cover"
                      />
                    </motion.div>
                    {section.caption && (
                      <p className="text-center text-sm text-gray-500 mt-4">
                        {section.caption}
                      </p>
                    )}
                  </div>
                </section>
              );

            default:
              return null;
          }
        })}
      </main>
       {/* Next Editorial Navigation */}
       <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <Link
            href="/editorial/sustainable-living"
            className="group block relative aspect-[16/9] overflow-hidden"
          >
            <Image
              src="/images/editorial/sustainable-hero.jpg'"
              alt="Next Editorial"
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors" />
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
              <p className="text-sm tracking-[0.2em] mb-4">NEXT EDITORIAL</p>
              <h2 className="text-4xl tracking-[0.1em] font-light mb-8"> SUSTAINABLE LIVING</h2>
              <FiArrowRight className="text-2xl" />
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}