import mongoose from 'mongoose';
import crypto from 'crypto';

const couponSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
  },
  type: {
    type: String,
    enum: ['percentage', 'fixed'],
    required: true,
  },
  value: {
    type: Number,
    required: true,
    min: 1,
  },
  minPurchase: {
    type: Number,
    default: 0,
    min: 0,
  },
  maxDiscount: {
    type: Number,
    default: null,
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  expiryDate: {
    type: Date,
    required: true,
  },
  usageLimit: {
    type: Number,
    default: null,
  },
  usedCount: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  description: {
    type: String,
    default: '',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  // Track which users have used this coupon
  usedBy: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'User',
    default: [],
  },
});

// Generate a random coupon code if not provided
couponSchema.pre('save', function(next) {
  if (!this.code) {
    this.code = crypto.randomBytes(3).toString('hex').toUpperCase();
  }
  
  this.updatedAt = new Date();
  next();
});

// Method to check if coupon is valid
couponSchema.methods.isValid = function(userId, purchaseAmount) {
  const now = new Date();
  
  // Check if coupon is active
  if (!this.isActive) {
    return { valid: false, message: 'Coupon is inactive' };
  }
  
  // Check if coupon has expired
  if (now > this.expiryDate) {
    return { valid: false, message: 'Coupon has expired' };
  }
  
  // Check if coupon has not started yet
  if (now < this.startDate) {
    return { valid: false, message: 'Coupon is not active yet' };
  }
  
  // Check if coupon has reached usage limit
  if (this.usageLimit !== null && this.usedCount >= this.usageLimit) {
    return { valid: false, message: 'Coupon usage limit reached' };
  }
  
  // Check if user has already used this coupon
  if (userId && this.usedBy.includes(userId)) {
    return { valid: false, message: 'You have already used this coupon' };
  }
  
  // Check minimum purchase amount
  if (purchaseAmount < this.minPurchase) {
    return { 
      valid: false, 
      message: `Minimum purchase amount of ₹${this.minPurchase.toLocaleString('en-IN')} required` 
    };
  }
  
  return { valid: true };
};

// Method to calculate discount amount
couponSchema.methods.calculateDiscount = function(subtotal) {
  let discountAmount = 0;
  
  if (this.type === 'percentage') {
    discountAmount = (subtotal * this.value) / 100;
    
    // Apply maximum discount cap if set
    if (this.maxDiscount !== null && discountAmount > this.maxDiscount) {
      discountAmount = this.maxDiscount;
    }
  } else if (this.type === 'fixed') {
    discountAmount = this.value;
    
    // Ensure discount doesn't exceed subtotal
    if (discountAmount > subtotal) {
      discountAmount = subtotal;
    }
  }
  
  return discountAmount;
};

// Method to mark coupon as used by a user
couponSchema.methods.markAsUsed = function(userId) {
  if (userId && !this.usedBy.includes(userId)) {
    this.usedBy.push(userId);
  }
  
  this.usedCount += 1;
  return this.save();
};

const Coupon = mongoose.models.Coupon || mongoose.model('Coupon', couponSchema);

export default Coupon;
