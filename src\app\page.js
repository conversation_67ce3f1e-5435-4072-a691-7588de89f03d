'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';

export default function HomePage() {
  // State for tracking scroll position and animations
  const [, setActiveSection] = useState(0);
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [newProducts, setNewProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const sectionsRef = useRef([]);

  // Define sections data
  const sections = [
    {
      content: {
        subtitle: "NEW COLLECTION 2025",
        title: "Modern Living Room Essentials",
        image: "https://cdn.cosmos.so/29c1dadc-c4c6-42bc-b0b5-5fbd31bf75ae?format=jpeg",
        cta: {
          text: "Shop Now",
          link: "/products"
        }
      }
    },

    {
      content: {
        title: "Design Stories",
        articles: [
          {
            title: "The Art of Minimalist Living",
            image: "https://cdn.cosmos.so/e780c435-76de-4de9-9512-d5bc2b258633?format=jpeg",
            link: "/"
          },
          {
            title: "Sustainable Living",
            image: "https://cdn.cosmos.so/e8176de1-2c9b-49cc-90ae-d4061ff6a4a2?format=jpeg",
            link: "/"
          }

        ]
      }
    }
  ];

  useEffect(() => {
    fetchNewProducts();

    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 3;

      sectionsRef.current.forEach((section, index) => {
        if (section && scrollPosition >= section.offsetTop && scrollPosition < section.offsetTop + section.offsetHeight) {
          setActiveSection(index);
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const fetchNewProducts = async () => {
    try {
      const response = await fetch('/api/products');
      if (!response.ok) throw new Error('Failed to fetch products');
      const products = await response.json();

      // Sort by creation date and get the 4 newest products
      const sorted = products
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 4);

      setNewProducts(sorted);
    } catch (error) {
      console.error('Error fetching new products:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return price.toLocaleString('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  };

  return (
    <div className="bg-white">
      {/* OBSESS-style Hero Section - Full Width, Minimal Text */}
      <section
        ref={el => sectionsRef.current[0] = el}
        className="relative md:h-screen aspect-[1/1] md:aspect-auto"
      >
      {/* OBSESS-style Hero Section - Full Width, Minimal Text
      <section
        ref={el => sectionsRef.current[0] = el}
        className="h-screen relative"
      > */}
        <Image
          src={sections[0].content.image}
          alt="Hero"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute bottom-0 left-0 p-8 md:p-16 lg:p-24 text-white">
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-xs tracking-[0.3em] mb-2 md:mb-4"
          >
            {sections[0].content.subtitle}
          </motion.p>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-3xl md:text-5xl lg:text-6xl font-light mb-4 md:mb-6 max-w-xl"
          >
            {sections[0].content.title}
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Link
              href={sections[0].content.cta.link}
              className="inline-flex items-center text-xs tracking-widest uppercase border-b border-white pb-1 hover:border-transparent hover:opacity-80 transition-all"
            >
              <span>{sections[0].content.cta.text}</span>
              <FiArrowRight className="ml-2 h-3 w-3" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* New Products Section - OBSESS Style */}
      <section
        ref={el => sectionsRef.current[1] = el}
        className="py-16 md:py-24 bg-white"
      >
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-start mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl md:text-2xl font-light">New Arrivals</h2>
              <p className="text-xs tracking-wider text-gray-500 mt-1">JUST ARRIVED</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="mt-4 md:mt-0"
            >
              <Link
                href="/products"
                className="text-xs tracking-widest uppercase border-b border-black pb-1 hover:opacity-70 transition-opacity inline-flex items-center"
              >
                <span>View All</span>
                <FiArrowRight className="ml-2 h-3 w-3" />
              </Link>
            </motion.div>
          </div>

          {loading ? (
            // Loading skeleton - OBSESS Style
            <div className="flex space-x-6 overflow-x-auto pb-4 scrollbar-hide">
              {[1, 2, 3, 4].map((n) => (
                <div key={n} className="animate-pulse flex-shrink-0 w-[280px] md:w-[320px]">
                  <div className="aspect-square bg-gray-100 mb-4"></div>
                  <div className="h-4 bg-gray-100 w-1/4 mb-2"></div>
                  <div className="h-5 bg-gray-100 w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-100 w-1/3"></div>
                </div>
              ))}
            </div>
          ) : (
            // Horizontal scrolling products - OBSESS Style
            <div className="flex space-x-6 overflow-x-auto pb-4 scrollbar-hide">
              {newProducts.map((product) => (
                <motion.div
                  key={product._id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  viewport={{ once: true }}
                  className="group flex-shrink-0 w-[280px] md:w-[320px]"
                >
                  <Link href={`/products/${product._id}`}>
                    <div
                      className="relative aspect-square mb-3 overflow-hidden"
                      onMouseEnter={() => setHoveredProduct(product._id)}
                      onMouseLeave={() => setHoveredProduct(null)}
                    >
                      <Image
                        src={hoveredProduct === product._id && product.images[1]
                          ? product.images[1]
                          : product.images[0]}
                        alt={product.name}
                        fill
                        className="object-cover transition-transform duration-700 group-hover:scale-105"
                      />
                      {product.discount > 0 && (
                        <div className="absolute top-3 left-3 bg-black text-white text-xs px-3 py-1.5 uppercase tracking-wider font-light">
                          {product.discount}% Off
                        </div>
                      )}
                      {product.stock === 0 ? (
                        <div className="absolute top-3 right-3 bg-red-500 text-white text-xs px-3 py-1.5 uppercase tracking-wider font-light">
                          Out of Stock
                        </div>
                      ) : product.stock <= 5 && (
                        <div className="absolute top-3 right-3 bg-amber-500 text-white text-xs px-3 py-1.5 uppercase tracking-wider font-light">
                          Low Stock
                        </div>
                      )}
                    </div>
                    <div className="space-y-1">
                      <h3 className="text-sm font-light">{product.name}</h3>
                      <div className="flex justify-between items-center">
                        <p className="text-xs text-gray-500">{product.category}</p>
                        {product.stock === 0 ? (
                          <p className="text-[10px] font-medium text-red-500 bg-red-50 px-1.5 py-0.5 rounded">
                            Out of Stock
                          </p>
                        ) : product.stock <= 5 && (
                          <p className="text-[10px] font-medium text-amber-500 bg-amber-50 px-1.5 py-0.5 rounded">
                            Low Stock: {product.stock}
                          </p>
                        )}
                      </div>
                      <div className="flex items-baseline gap-2 mt-1">
                        {product.discount > 0 ? (
                          <>
                            <p className="text-sm font-light">
                              {formatPrice(product.price * (1 - product.discount / 100))}
                            </p>
                            <p className="text-xs text-gray-500 line-through">
                              {formatPrice(product.price)}
                            </p>
                          </>
                        ) : (
                          <p className="text-sm font-light">
                            {formatPrice(product.price)}
                          </p>
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}

              {/* View All Card - OBSESS Style */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                viewport={{ once: true }}
                className="flex-shrink-0 w-[280px] md:w-[320px] flex items-center justify-center"
              >
                <Link
                  href="/products"
                  className="group flex flex-col items-center justify-center h-full"
                >
                  <div className="relative aspect-square mb-3 bg-gray-50 w-full flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                    <FiArrowRight className="w-8 h-8 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                  <p className="text-sm font-light text-gray-500 group-hover:text-gray-700 transition-colors">View All Products</p>
                </Link>
              </motion.div>
            </div>
          )}
        </div>
      </section>

      {/* Categories Showcase - OBSESS Style */}
      <section
        ref={el => sectionsRef.current[2] = el}
        className="py-16 md:py-24 bg-white border-t border-black/5"
      >
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-start mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl md:text-2xl font-light">Categories</h2>
              <p className="text-xs tracking-wider text-gray-500 mt-1">EXPLORE OUR COLLECTION</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              viewport={{ once: true }}
              className="mt-4 md:mt-0"
            >
              <Link
                href="/products"
                className="text-xs tracking-widest uppercase border-b border-black pb-1 hover:opacity-70 transition-opacity inline-flex items-center"
              >
                <span>View All Categories</span>
                <FiArrowRight className="ml-2 h-3 w-3" />
              </Link>
            </motion.div>
          </div>

          {/* Main Categories - OBSESS Style */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-1">
            {[
              {
                id: 'living-room',
                name: 'LIVING ROOM',
                image: 'https://cdn.cosmos.so/f34d857e-4e2d-4547-b4bf-f5f7ef452026?format=jpeg',
                featured: true
              },
              {
                id: 'bedroom',
                name: 'BEDROOM',
                image: 'https://cdn.cosmos.so/7c95c80e-765d-489a-bdea-0a53f81da028?format=jpeg',
                featured: true
              },
              {
                id: 'dining-room',
                name: 'DINING ROOM',
                image: 'https://cdn.cosmos.so/99cd2099-fa70-40d9-8807-68f27ef34db8?format=jpeg',
                featured: true
              }
            ].map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Link
                  href={`/products?category=${category.id}`}
                  className="group block relative aspect-square overflow-hidden"
                >
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 p-6 md:p-8">
                    <h3 className="text-lg md:text-xl font-light text-white tracking-wider">
                      {category.name}
                    </h3>
                    <div className="mt-2 overflow-hidden h-0 group-hover:h-6 transition-all duration-300">
                      <span className="text-xs text-white/90 uppercase tracking-wider border-b border-white/70 pb-1 inline-flex items-center">
                        Shop Now
                        <FiArrowRight className="ml-2 h-3 w-3" />
                      </span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Secondary Categories - OBSESS Style */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-1 mt-1">
            {[
              {
                id: 'office',
                name: 'OFFICE',
                image: 'https://cdn.cosmos.so/6df276bf-a867-4aa6-8023-4287c456cd6d?format=jpeg'
              },
              {
                id: 'outdoor',
                name: 'OUTDOOR',
                image: 'https://cdn.cosmos.so/feca3c52-760e-49e1-8080-8b3d32aa2013?format=jpeg'
              },
              {
                id: 'decor',
                name: 'DECOR',
                image: 'https://cdn.cosmos.so/3e6f284e-43c6-42d0-a83f-0f520ccaba7c?format=jpeg'
              },
              {
                id: 'all',
                name: 'VIEW ALL',
                image: 'https://cdn.cosmos.so/222acc74-6083-4b5c-9c01-b4d052c90b38?format=jpeg'
              }
            ].map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.3 + (index * 0.1) }}
                viewport={{ once: true }}
              >
                <Link
                  href={category.id === 'all' ? '/products' : `/products?category=${category.id}`}
                  className="group block relative aspect-square overflow-hidden"
                >
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 p-4 md:p-6">
                    <h3 className="text-sm md:text-base font-light text-white tracking-wider">
                      {category.name}
                    </h3>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Editorial Section - OBSESS Style */}
      <section
        ref={el => sectionsRef.current[2] = el}
        className="py-16 md:py-24 bg-white border-t border-black/5"
      >
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-start mb-8 md:mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl md:text-2xl font-light">{sections[1].content.title}</h2>
              <p className="text-xs tracking-wider text-gray-500 mt-1">INSPIRATION & IDEAS</p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {sections[1].content.articles.map((article, i) => (
              <motion.div
                key={article.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.2 }}
                viewport={{ once: true }}
              >
                <Link
                  href={article.link}
                  className="group block relative aspect-[16/9] overflow-hidden"
                >
                  <Image
                    src={article.image}
                    alt={article.title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-colors" />
                  <div className="absolute bottom-0 left-0 p-6 md:p-8">
                    <h3 className="text-xl md:text-2xl font-light text-white">{article.title}</h3>
                    <div className="mt-2 overflow-hidden h-0 group-hover:h-6 transition-all duration-300">
                      <span className="text-xs text-white/90 uppercase tracking-wider border-b border-white/70 pb-1 inline-flex items-center">
                        Read More
                        <FiArrowRight className="ml-2 h-3 w-3" />
                      </span>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}






