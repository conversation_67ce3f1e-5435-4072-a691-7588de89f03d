import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';
import OrderNote from '@/models/OrderNote';
import mongoose from 'mongoose';

// Get all notes for an order
export async function GET(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;

    // Check if order exists
    const orderExists = await Order.exists({ _id: orderId });
    if (!orderExists) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Get all notes for this order
    const notes = await OrderNote.find({ order: orderId })
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    return NextResponse.json(notes);
  } catch (error) {
    console.error('Error fetching order notes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order notes' },
      { status: 500 }
    );
  }
}

// Add a new note to an order
export async function POST(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;
    const { text, isInternal = true } = await request.json();

    // Validate input
    if (!text || typeof text !== 'string' || text.trim() === '') {
      return NextResponse.json(
        { error: 'Note text is required' },
        { status: 400 }
      );
    }

    // Check if order exists
    const orderExists = await Order.exists({ _id: orderId });
    if (!orderExists) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Create new note
    const note = new OrderNote({
      order: orderId,
      text,
      createdBy: user._id,
      isInternal,
      createdAt: new Date()
    });

    // Save note
    await note.save();

    // Return the note with populated user
    const populatedNote = await OrderNote.findById(note._id)
      .populate('createdBy', 'name email');

    return NextResponse.json(populatedNote, { status: 201 });
  } catch (error) {
    console.error('Error creating order note:', error);
    return NextResponse.json(
      { error: 'Failed to create order note' },
      { status: 500 }
    );
  }
}

// Delete a note
export async function DELETE(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;
    const { noteId } = await request.json();

    if (!noteId) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      );
    }

    // Find the note
    const note = await OrderNote.findOne({
      _id: noteId,
      order: orderId
    });

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found' },
        { status: 404 }
      );
    }

    // Delete the note
    await OrderNote.deleteOne({ _id: noteId });

    return NextResponse.json({ message: 'Note deleted successfully' });
  } catch (error) {
    console.error('Error deleting order note:', error);
    return NextResponse.json(
      { error: 'Failed to delete order note' },
      { status: 500 }
    );
  }
}
