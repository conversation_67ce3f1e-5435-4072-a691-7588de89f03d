'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import AdminProductForm from '@/components/AdminProductForm';

export default function NewProduct() {
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
    }
  }, [user, router]);

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white z-50 border-b border-black/10">
        <div className="px-6 py-4 flex justify-between items-center">
          <button
            onClick={() => router.back()}
            className="text-sm tracking-widest hover:opacity-70"
          >
            BACK
          </button>
          <h1 className="text-sm tracking-widest">NEW PRODUCT</h1>
          <div className="w-[52px]" /> {/* Spacer for alignment */}
        </div>
      </header>

      {/* Main Content */}
      <main className="pt-[60px]">
        <AdminProductForm />
      </main>
    </div>
  );
} 
