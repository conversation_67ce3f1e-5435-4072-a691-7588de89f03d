'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import {
  FiSearch, FiFilter, FiCheck, FiMoreVertical,
  FiTrash2, FiEdit2, FiChevronDown, FiUser,
  FiMail, FiPhone, FiMapPin, FiCalendar, FiX,
  FiShoppingBag, FiHeart, FiAlertCircle, FiBell,
  FiArrowLeft, FiArrowUp, FiArrowDown, FiEye
} from 'react-icons/fi';

export default function UsersManagement() {
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [newsletterFilter, setNewsletterFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');

  const [showFilters, setShowFilters] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [sortConfig, setSortConfig] = useState({ field: 'createdAt', direction: 'desc' });
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [userOrdersCount, setUserOrdersCount] = useState(0);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    admin: 0,
    regularUsers: 0,
    newsletterSubscribers: 0
  });
  const dropdownRef = useRef(null);
  const modalRef = useRef(null);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setActiveDropdown(null);
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowUserModal(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Add body class when modal is open to prevent scrolling
  useEffect(() => {
    if (showUserModal) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }

    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [showUserModal]);

  useEffect(() => {
    fetchUsers();
  }, [sortConfig]);

  // Calculate user statistics whenever users data changes
  useEffect(() => {
    if (users.length > 0) {
      const activeUsers = users.filter(user => user.status === 'active').length;
      const inactiveUsers = users.filter(user => user.status === 'inactive').length;
      const adminUsers = users.filter(user => user.role === 'admin').length;
      const regularUsers = users.filter(user => user.role === 'user').length;
      const newsletterUsers = users.filter(user => user.isNewsletterSubscriber).length;

      setStats({
        total: users.length,
        active: activeUsers,
        inactive: inactiveUsers,
        admin: adminUsers,
        regularUsers: regularUsers,
        newsletterSubscribers: newsletterUsers
      });
    }
  }, [users]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // Add a cache-busting parameter to ensure we get fresh data
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/users?sort=${sortConfig.field}&direction=${sortConfig.direction}&_=${timestamp}`);

      if (response.ok) {
        const data = await response.json();
        console.log('Fetched users:', data.users);
        setUsers(data.users);
      } else {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleRoleChange = async (userId, newRole) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ role: newRole })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('User role updated successfully');
        fetchUsers();
      } else {
        throw new Error(data.error || 'Failed to update user role');
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleStatusChange = async (userId, newStatus) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`User ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
        fetchUsers();

        // If we're viewing user details, update them
        if (showUserModal && selectedUser && selectedUser._id === userId) {
          fetchUserDetails(userId);
        }
      } else {
        throw new Error(data.error || `Failed to ${newStatus === 'active' ? 'activate' : 'deactivate'} user`);
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleVerifyEmail = async (userId, verificationStatus) => {
    try {
      setActionLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailVerified: verificationStatus })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`User email ${verificationStatus ? 'verified' : 'unverified'} successfully`);
        fetchUsers();

        // If we're viewing user details, update them
        if (showUserModal && selectedUser && selectedUser._id === userId) {
          fetchUserDetails(userId);
        }
      } else {
        throw new Error(data.error || `Failed to ${verificationStatus ? 'verify' : 'unverify'} user email`);
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleBulkAction = async (action) => {
    try {
      // Only handle delete action
      if (action !== 'delete') return;

      setActionLoading(true);
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userIds: selectedUsers,
          action
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Users deleted successfully');
        setSelectedUsers([]);
        fetchUsers();
      } else {
        throw new Error(data.error || 'Failed to delete users');
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const fetchUserDetails = async (userId) => {
    try {
      setActionLoading(true);

      // Fetch user details
      const userResponse = await fetch(`/api/admin/users/${userId}`);

      if (!userResponse.ok) {
        const errorData = await userResponse.json();
        throw new Error(errorData.error || 'Failed to fetch user details');
      }

      const userData = await userResponse.json();
      setUserDetails(userData);

      // Fetch user orders count
      try {
        const ordersResponse = await fetch(`/api/admin/users/${userId}/orders/count`);
        if (ordersResponse.ok) {
          const ordersData = await ordersResponse.json();
          setUserOrdersCount(ordersData.count);
        }
      } catch (error) {
        console.error('Error fetching user orders count:', error);
        setUserOrdersCount(0);
      }

    } catch (error) {
      console.error('Error fetching user details:', error);
      toast.error(error.message);
    } finally {
      setActionLoading(false);
    }
  };

  const handleViewUserDetails = (user) => {
    // Navigate to the user profile page
    window.location.href = `/admin/users/${user._id}`;
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesNewsletter =
      newsletterFilter === 'all' ||
      (newsletterFilter === 'subscribed' && user.isNewsletterSubscriber) ||
      (newsletterFilter === 'not-subscribed' && !user.isNewsletterSubscriber);
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesVerification =
      verificationFilter === 'all' ||
      (verificationFilter === 'verified' && user.emailVerified) ||
      (verificationFilter === 'not-verified' && !user.emailVerified);
    return matchesSearch && matchesRole && matchesNewsletter && matchesStatus && matchesVerification;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header with Stats */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          {/* Back Button */}
          <div className="mb-4">
            <button
              onClick={() => router.push('/admin')}
              className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
            >
              <FiArrowLeft className="mr-1.5" />
              BACK TO ADMIN
            </button>
          </div>

          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-light tracking-tight">USERS</h1>
            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-2 hover:bg-gray-100 rounded-full"
                title={showFilters ? "Hide filters" : "Show filters"}
              >
                <FiFilter size={20} />
              </button>
              {selectedUsers.length > 0 && (
                <span className="text-sm font-medium">
                  {selectedUsers.length} selected
                </span>
              )}
            </div>
          </div>

          {/* User Statistics - Moved inside header like in orders page */}
          <div className="grid grid-cols-6 gap-4 mb-4">
            <div className="p-4 border border-gray-200 bg-gray-50">
              <p className="text-sm">TOTAL USERS</p>
              <p className="text-2xl font-medium">{stats.total}</p>
            </div>
            <div className="p-4 border border-green-100 bg-green-50">
              <p className="text-sm text-green-700">ACTIVE USERS</p>
              <p className="text-2xl font-medium text-green-700">{stats.active}</p>
            </div>
            <div className="p-4 border border-red-100 bg-red-50">
              <p className="text-sm text-red-700">INACTIVE USERS</p>
              <p className="text-2xl font-medium text-red-700">{stats.inactive}</p>
            </div>
            <div className="p-4 border border-blue-100 bg-blue-50">
              <p className="text-sm text-blue-700">ADMINS</p>
              <p className="text-2xl font-medium text-blue-700">{stats.admin}</p>
            </div>
            <div className="p-4 border border-gray-200 bg-gray-50">
              <p className="text-sm">REGULAR USERS</p>
              <p className="text-2xl font-medium">{stats.regularUsers}</p>
            </div>
            <div className="p-4 border border-purple-100 bg-purple-50">
              <p className="text-sm text-purple-700">NEWSLETTER SUBSCRIBERS</p>
              <p className="text-2xl font-medium text-purple-700">{stats.newsletterSubscribers}</p>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search by name, email, or ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 border-none rounded-none focus:ring-0 text-sm"
            />
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="px-6 py-3 border-t border-gray-100 bg-gray-50">
            <div className="space-y-4">
              {/* Role Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Role:</span>
                <div className="flex flex-wrap gap-2">
                  {['all', 'user', 'admin'].map(role => (
                    <button
                      key={role}
                      onClick={() => setRoleFilter(role)}
                      className={`px-3 py-1 text-xs ${
                        roleFilter === role
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Status Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Status:</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'all', label: 'All' },
                    { value: 'active', label: 'Active' },
                    { value: 'inactive', label: 'Inactive' }
                  ].map(option => (
                    <button
                      key={option.value}
                      onClick={() => setStatusFilter(option.value)}
                      className={`px-3 py-1 text-xs ${
                        statusFilter === option.value
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Email Verification Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Email Status:</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'all', label: 'All' },
                    { value: 'verified', label: 'Verified' },
                    { value: 'not-verified', label: 'Not Verified' }
                  ].map(option => (
                    <button
                      key={option.value}
                      onClick={() => setVerificationFilter(option.value)}
                      className={`px-3 py-1 text-xs ${
                        verificationFilter === option.value
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Newsletter Filter */}
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-gray-500 w-24">Newsletter:</span>
                <div className="flex flex-wrap gap-2">
                  {[
                    { value: 'all', label: 'All' },
                    { value: 'subscribed', label: 'Subscribed' },
                    { value: 'not-subscribed', label: 'Not Subscribed' }
                  ].map(option => (
                    <button
                      key={option.value}
                      onClick={() => setNewsletterFilter(option.value)}
                      className={`px-3 py-1 text-xs ${
                        newsletterFilter === option.value
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

      </div>

      {/* Table Header - Moved outside the header section like in orders page */}
      <div className="grid grid-cols-7 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={selectedUsers.length === filteredUsers.length}
            onChange={() => {
              if (selectedUsers.length === filteredUsers.length) {
                setSelectedUsers([]);
              } else {
                setSelectedUsers(filteredUsers.map(u => u._id));
              }
            }}
            className="rounded border-gray-300"
          />
          <button
            onClick={() => handleSort('name')}
            className="flex items-center gap-1"
          >
            User Info
            {sortConfig.field === 'name' && (
              sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
            )}
          </button>
        </div>
        <div>Email</div>
        <div>Role</div>
        <div>Status</div>
        <div>Email Verified</div>
        <div>Newsletter</div>
        <button
          onClick={() => handleSort('createdAt')}
          className="flex items-center gap-1"
        >
          Joined
          {sortConfig.field === 'createdAt' && (
            sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
          )}
        </button>
      </div>

      {/* Users List */}
      <div className="divide-y divide-gray-100">
        {loading ? (
          <div className="p-6 text-center text-gray-500">Loading users...</div>
        ) : filteredUsers.length === 0 ? (
          <div className="p-6 text-center text-gray-500">No users found</div>
        ) : (
          filteredUsers.map((user) => (
            <div
              key={user._id}
              className={`group hover:bg-gray-50 ${
                selectedUsers.includes(user._id) ? 'bg-gray-50' : ''
              }`}
            >
              <div className="px-6 py-4">
                <div className="grid grid-cols-7 gap-4 items-center">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user._id)}
                      onChange={() => {
                        setSelectedUsers(prev =>
                          prev.includes(user._id)
                            ? prev.filter(id => id !== user._id)
                            : [...prev, user._id]
                        );
                      }}
                      className="rounded border-gray-300"
                    />
                    <div>
                      <div>
                        <a
                          href={`/admin/users/${user._id}`}
                          onClick={(e) => {
                            e.preventDefault();
                            router.push(`/admin/users/${user._id}`);
                          }}
                          className="text-sm font-medium hover:underline"
                        >
                          {user.name || 'N/A'}
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm">{user.email}</p>
                    {user.phone && (
                      <p className="text-xs text-gray-500">{user.phone}</p>
                    )}
                  </div>

                  <div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.role === 'admin'
                        ? 'bg-black text-white'
                        : 'bg-gray-100'
                    }`}>
                      {user.role.toUpperCase()}
                    </span>
                  </div>

                  <div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status.toUpperCase()}
                    </span>
                  </div>

                  <div>
                    {user.emailVerified ? (
                      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        VERIFIED
                      </span>
                    ) : (
                      <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                        NOT VERIFIED
                      </span>
                    )}
                  </div>

                  <div>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      user.isNewsletterSubscriber
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.isNewsletterSubscriber ? 'SUBSCRIBED' : 'NOT SUBSCRIBED'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {formatDate(user.createdAt)}
                    </span>
                    <div className="relative">
                      <button
                        onClick={() => setActiveDropdown(activeDropdown === user._id ? null : user._id)}
                        className="p-2 hover:bg-gray-100 rounded-full"
                      >
                        <FiMoreVertical />
                      </button>
                      {activeDropdown === user._id && (
                        <div ref={dropdownRef} className="absolute right-0 mt-2 w-48 bg-white border border-gray-100 rounded-md shadow-lg z-10">
                          <div className="py-1">
                            <a
                              href={`/admin/users/${user._id}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setActiveDropdown(null);
                              }}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiUser className="mr-2" />
                              View Profile
                            </a>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRoleChange(user._id, user.role === 'admin' ? 'user' : 'admin');
                                setActiveDropdown(null);
                              }}
                              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiEdit2 className="mr-2" />
                              Change Role
                            </button>
                            {user.status === 'active' ? (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStatusChange(user._id, 'inactive');
                                  setActiveDropdown(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                              >
                                <FiAlertCircle className="mr-2" />
                                Deactivate User
                              </button>
                            ) : (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStatusChange(user._id, 'active');
                                  setActiveDropdown(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-gray-100"
                              >
                                <FiCheck className="mr-2" />
                                Activate User
                              </button>
                            )}

                            {user.emailVerified ? (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleVerifyEmail(user._id, false);
                                  setActiveDropdown(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <FiAlertCircle className="mr-2" />
                                Mark as Unverified
                              </button>
                            ) : (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleVerifyEmail(user._id, true);
                                  setActiveDropdown(null);
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-gray-100"
                              >
                                <FiCheck className="mr-2" />
                                Verify Email
                              </button>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>



      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
          <div className="max-w-screen-xl mx-auto flex items-center justify-between">
            <span className="text-sm">
              {selectedUsers.length} users selected
            </span>
            <div className="flex gap-4">
              <select
                onChange={(e) => {
                  const action = e.target.value;
                  if (action === 'role-admin') {
                    // Implement bulk role change to admin
                    alert('Bulk role change to admin will be implemented soon');
                  } else if (action === 'role-user') {
                    // Implement bulk role change to user
                    alert('Bulk role change to user will be implemented soon');
                  }
                  e.target.value = '';
                }}
                className="text-sm border-gray-200"
                defaultValue=""
              >
                <option value="" disabled>Change Role</option>
                <option value="role-admin">Make Admin</option>
                <option value="role-user">Make Regular User</option>
              </select>

              <select
                onChange={(e) => {
                  const action = e.target.value;
                  if (action === 'status-active') {
                    // Implement bulk status change to active
                    alert('Bulk activation will be implemented soon');
                  } else if (action === 'status-inactive') {
                    // Implement bulk status change to inactive
                    alert('Bulk deactivation will be implemented soon');
                  }
                  e.target.value = '';
                }}
                className="text-sm border-gray-200"
                defaultValue=""
              >
                <option value="" disabled>Update Status</option>
                <option value="status-active">Activate Users</option>
                <option value="status-inactive">Deactivate Users</option>
              </select>

              <select
                onChange={(e) => {
                  const action = e.target.value;
                  if (action === 'email') {
                    alert('Email functionality will be implemented soon');
                  } else if (action === 'export') {
                    alert('Export functionality will be implemented soon');
                  } else if (action === 'delete') {
                    if (confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
                      handleBulkAction('delete');
                    }
                  }
                  e.target.value = '';
                }}
                className="text-sm border-gray-200"
                defaultValue=""
              >
                <option value="" disabled>More Actions</option>
                <option value="email">Email Users</option>
                <option value="export">Export CSV</option>
                <option value="delete">Delete Users</option>
              </select>

              <button
                onClick={() => setSelectedUsers([])}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* User Details Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
          <div
            ref={modalRef}
            className="bg-white w-full max-w-3xl max-h-[90vh] overflow-y-auto"
          >
            <div className="sticky top-0 bg-white z-10 flex justify-between items-center border-b border-gray-200 p-6">
              <h2 className="text-xl font-light tracking-tight">USER DETAILS</h2>
              <button
                onClick={() => setShowUserModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FiX size={24} />
              </button>
            </div>

            <div className="p-6">
              {actionLoading ? (
                <div className="py-12 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
                  <p className="mt-2 text-gray-500">Loading user details...</p>
                </div>
              ) : userDetails ? (
                <div className="space-y-8">
                  {/* Basic Information */}
                  <div>
                    <h3 className="text-sm font-medium tracking-wider text-gray-500 mb-4">BASIC INFORMATION</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <p className="text-xs text-gray-500">Name</p>
                          <p className="font-medium">{userDetails.name}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Email</p>
                          <p className="font-medium">{userDetails.email}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Phone</p>
                          <p className="font-medium">{userDetails.phone || 'Not provided'}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <p className="text-xs text-gray-500">Role</p>
                          <p className="font-medium">
                            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                              userDetails.role === 'admin'
                                ? 'bg-black text-white'
                                : 'bg-gray-100'
                            }`}>
                              {userDetails.role.toUpperCase()}
                            </span>
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Status</p>
                          <p className="font-medium">
                            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                              userDetails.status === 'active'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {userDetails.status.toUpperCase()}
                            </span>
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Email Verified</p>
                          <p className="font-medium">
                            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                              userDetails.emailVerified
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {userDetails.emailVerified ? 'VERIFIED' : 'NOT VERIFIED'}
                            </span>
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Newsletter</p>
                          <p className="font-medium">
                            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                              userDetails.isNewsletterSubscriber
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {userDetails.isNewsletterSubscriber ? 'SUBSCRIBED' : 'NOT SUBSCRIBED'}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Address Information */}
                  <div>
                    <h3 className="text-sm font-medium tracking-wider text-gray-500 mb-4">ADDRESS</h3>
                    <p className="text-gray-700">{userDetails.address || 'No address provided'}</p>
                  </div>

                  {/* Account Information */}
                  <div>
                    <h3 className="text-sm font-medium tracking-wider text-gray-500 mb-4">ACCOUNT INFORMATION</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <p className="text-xs text-gray-500">Joined</p>
                        <p className="font-medium">{formatDate(userDetails.createdAt)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Last Updated</p>
                        <p className="font-medium">{formatDate(userDetails.updatedAt)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Activity Information */}
                  <div>
                    <h3 className="text-sm font-medium tracking-wider text-gray-500 mb-4">ACTIVITY</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="border border-gray-200 p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <FiShoppingBag className="text-gray-400" />
                          <p className="text-sm text-gray-500">Orders</p>
                        </div>
                        <p className="text-2xl font-light">{userOrdersCount}</p>
                      </div>
                      <div className="border border-gray-200 p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <FiHeart className="text-gray-400" />
                          <p className="text-sm text-gray-500">Wishlist Items</p>
                        </div>
                        <p className="text-2xl font-light">{userDetails.wishlist?.length || 0}</p>
                      </div>
                      <div className="border border-gray-200 p-4">
                        <div className="flex items-center gap-3 mb-2">
                          <FiShoppingBag className="text-gray-400" />
                          <p className="text-sm text-gray-500">Cart Items</p>
                        </div>
                        <p className="text-2xl font-light">{userDetails.cart?.length || 0}</p>
                      </div>
                    </div>
                  </div>

                  {/* Notification Preferences */}
                  <div>
                    <h3 className="text-sm font-medium tracking-wider text-gray-500 mb-4">NOTIFICATION PREFERENCES</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${userDetails.notifications?.orderUpdates ? 'bg-green-100' : 'bg-gray-100'}`}>
                          <FiBell className={userDetails.notifications?.orderUpdates ? 'text-green-600' : 'text-gray-400'} />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Order Updates</p>
                          <p className="text-xs text-gray-500">{userDetails.notifications?.orderUpdates ? 'Enabled' : 'Disabled'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${userDetails.notifications?.promotions ? 'bg-green-100' : 'bg-gray-100'}`}>
                          <FiBell className={userDetails.notifications?.promotions ? 'text-green-600' : 'text-gray-400'} />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Promotions</p>
                          <p className="text-xs text-gray-500">{userDetails.notifications?.promotions ? 'Enabled' : 'Disabled'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${userDetails.notifications?.recommendations ? 'bg-green-100' : 'bg-gray-100'}`}>
                          <FiBell className={userDetails.notifications?.recommendations ? 'text-green-600' : 'text-gray-400'} />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Recommendations</p>
                          <p className="text-xs text-gray-500">{userDetails.notifications?.recommendations ? 'Enabled' : 'Disabled'}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex flex-wrap gap-4">
                      <a
                        href={`/admin/users/${userDetails._id}`}
                        className="px-4 py-2 bg-black text-white hover:bg-gray-800 text-sm"
                      >
                        View Full Profile
                      </a>
                      <button
                        onClick={() => {
                          handleRoleChange(userDetails._id, userDetails.role === 'admin' ? 'user' : 'admin');
                          setShowUserModal(false);
                        }}
                        className="px-4 py-2 border border-gray-300 hover:bg-gray-50 text-sm"
                      >
                        Change to {userDetails.role === 'admin' ? 'User' : 'Admin'}
                      </button>

                      {userDetails.status === 'active' ? (
                        <button
                          onClick={() => {
                            handleStatusChange(userDetails._id, 'inactive');
                          }}
                          className="px-4 py-2 border border-red-300 text-red-600 hover:bg-red-50 text-sm"
                        >
                          Deactivate User
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            handleStatusChange(userDetails._id, 'active');
                          }}
                          className="px-4 py-2 border border-green-300 text-green-600 hover:bg-green-50 text-sm"
                        >
                          Activate User
                        </button>
                      )}

                      {userDetails.emailVerified ? (
                        <button
                          onClick={() => {
                            handleVerifyEmail(userDetails._id, false);
                          }}
                          className="px-4 py-2 border border-gray-300 text-gray-600 hover:bg-gray-50 text-sm"
                        >
                          Mark Email as Unverified
                        </button>
                      ) : (
                        <button
                          onClick={() => {
                            handleVerifyEmail(userDetails._id, true);
                          }}
                          className="px-4 py-2 border border-green-300 text-green-600 hover:bg-green-50 text-sm"
                        >
                          Verify Email
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="py-12 text-center text-gray-500">
                  <FiAlertCircle className="mx-auto mb-4" size={24} />
                  <p>Failed to load user details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
