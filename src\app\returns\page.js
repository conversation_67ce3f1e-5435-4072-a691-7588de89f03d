'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiArrowLeft, FiCheckCircle, FiAlertCircle, FiHelpCircle, FiPackage } from 'react-icons/fi';

export default function ReturnsPage() {

  // Return process steps
  const returnSteps = [
    {
      title: "Initiate Return",
      description: "Log into your account, go to 'My Orders', and select the item you wish to return. Fill out the return form with your reason for return.",
      icon: FiPackage
    },
    {
      title: "Return Approval",
      description: "Our team will review your return request within 24-48 hours and send you an email confirmation with return instructions.",
      icon: FiCheckCircle
    },
    {
      title: "Package Your Item",
      description: "Pack the item in its original packaging with all accessories, manuals, and tags attached. Include the return form in your package.",
      icon: FiPackage
    },
    {
      title: "Schedule Pickup or Drop-off",
      description: "For large items, we'll arrange a pickup. For smaller items, you can drop them off at our partner courier locations or schedule a pickup.",
      icon: FiArrowLeft
    },
    {
      title: "Refund Processing",
      description: "Once we receive and inspect your return, we'll process your refund within 7-10 business days to your original payment method.",
      icon: FiCheckCircle
    }
  ];

  return (
    <div className="bg-white min-h-screen pt-10 md:pt-16">
      {/* Hero Section - Zara Style */}
      <section className="relative py-8 md:py-12 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p
              className="text-xs uppercase tracking-wider text-gray-500 mb-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              Customer Service
            </motion.p>
            <motion.h1
              className="text-2xl md:text-3xl font-light text-black mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              Returns & Exchanges
            </motion.h1>
            <motion.p
              className="text-sm md:text-base text-gray-600 max-w-2xl"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              Our hassle-free return policy to ensure your complete satisfaction with every purchase.
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            {/* Return Policy Overview - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Return Policy Overview</h2>

              <div className="space-y-6">
                <p className="text-sm text-gray-600">
                  At FurnitureBazaar, we want you to be completely satisfied with your purchase. If you're not happy with your order for any reason, we offer a simple return and exchange policy.
                </p>

                <div className="border-t border-black/5 pt-6">
                  <p className="text-base font-light">
                    You can return most items within 7 days of delivery for a full refund or exchange.
                  </p>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <p className="text-xs uppercase tracking-wider text-gray-500 mb-3">Return Eligibility Requirements</p>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">In the same condition that you received it</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">In the original packaging with all tags attached</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Unused and unassembled (unless the item arrived damaged)</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Accompanied by the original receipt or proof of purchase</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Return Process - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Return Process</h2>

              <div className="space-y-12">
                {returnSteps.map((step, index) => (
                  <motion.div
                    key={index}
                    className="border-t border-black/5 pt-6"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="flex items-start">
                      <div className="mr-6">
                        <step.icon className="w-5 h-5 text-black" />
                      </div>
                      <div>
                        <h3 className="text-base font-light mb-3">
                          {index + 1}. {step.title}
                        </h3>
                        <p className="text-sm text-gray-600">{step.description}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Return Exceptions - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Return Exceptions</h2>

              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiAlertCircle className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Custom-Made Furniture</h3>
                      <p className="text-sm text-gray-600">
                        Custom-made or personalized furniture items cannot be returned unless they have manufacturing defects.
                        This includes items with custom dimensions, fabrics, colors, or any other customizations.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiAlertCircle className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Clearance & Sale Items</h3>
                      <p className="text-sm text-gray-600">
                        Items purchased on clearance or during special sales may have different return policies.
                        These exceptions will be clearly mentioned on the product page at the time of purchase.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <div className="flex items-start">
                    <div className="mr-6">
                      <FiAlertCircle className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-base font-light mb-3">Mattresses & Bedding</h3>
                      <p className="text-sm text-gray-600">
                        For hygiene reasons, mattresses, pillows, and bedding can only be returned if they are unopened
                        and in their original sealed packaging. Once opened, these items cannot be returned unless they
                        have manufacturing defects.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Refunds & Exchanges - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Refunds & Exchanges</h2>

              <div className="space-y-12">
                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Refunds</h3>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      Once we receive and inspect your return, we'll send you an email to notify you that we have received your returned item.
                      We will also notify you of the approval or rejection of your refund.
                    </p>
                    <p className="text-sm text-gray-600">
                      If approved, your refund will be processed, and a credit will automatically be applied to your original method of payment within 7-10 business days.
                      Please note that depending on your credit card company, it may take an additional 2-10 business days for the refund to appear on your statement.
                    </p>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Exchanges</h3>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      If you'd like to exchange your item for a different color, size, or model, please indicate this on your return form.
                      Exchanges are subject to product availability.
                    </p>
                    <p className="text-sm text-gray-600">
                      If the item you wish to exchange for is of higher value, you will need to pay the price difference.
                      If it's of lower value, we'll refund the difference to your original payment method.
                    </p>
                  </div>
                </div>

                <div className="border-t border-black/5 pt-6">
                  <h3 className="text-base font-light mb-3">Return Shipping Costs</h3>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      For standard returns, you will be responsible for paying the return shipping costs.
                      The shipping cost will be deducted from your refund.
                    </p>
                    <p className="text-sm text-gray-600">
                      However, if you received a damaged or defective item, we will cover the return shipping costs.
                      Please contact our customer service team to arrange this.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Damaged or Defective Items - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Damaged or Defective Items</h2>

              <div className="border-t border-black/5 pt-6">
                <div className="flex items-start">
                  <div className="mr-6">
                    <FiHelpCircle className="w-5 h-5 text-black" />
                  </div>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">
                      If you receive a damaged or defective item, please contact our customer service team within 48 hours of delivery.
                      Please provide photos of the damage along with your order number.
                    </p>
                    <p className="text-sm text-gray-600">
                      For damaged items, we offer the following options:
                    </p>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                        <p className="text-sm">Full replacement of the item</p>
                      </div>
                      <div className="flex items-start">
                        <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                        <p className="text-sm">Repair of the damaged part (if applicable)</p>
                      </div>
                      <div className="flex items-start">
                        <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                        <p className="text-sm">Full refund including shipping costs</p>
                      </div>
                      <div className="flex items-start">
                        <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                        <p className="text-sm">Partial refund if you wish to keep the item with minor damage</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">
                      Our customer service team will work with you to find the best solution based on the nature and extent of the damage.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Warranty Information - Zara Style */}
            <motion.div
              className="mb-20"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-xl font-light text-black mb-8 uppercase tracking-wide">Warranty Information</h2>

              <div className="border-t border-black/5 pt-6 space-y-8">
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    All our furniture comes with a warranty against manufacturing defects:
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Standard Warranty:</span> 1 year on all furniture items</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Premium Furniture:</span> Up to 5 years on select premium collections</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Mattresses:</span> 5-10 years depending on the model</p>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Our warranty covers:
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Structural defects in frames and supports</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Defective mechanisms (recliners, sofa beds, etc.)</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Premature deterioration of materials under normal use</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Defects in upholstery and stitching</p>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Our warranty does not cover:
                  </p>

                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Normal wear and tear</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Damage due to improper use or accidents</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Changes in wood color due to exposure to sunlight</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Damage from improper cleaning or maintenance</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Variations in natural materials (wood grain, leather texture, etc.)</p>
                    </div>
                  </div>
                </div>

                <p className="text-sm text-gray-600 pt-2">
                  To claim warranty, please contact our customer service with your order details and photos of the issue.
                  Specific warranty information for each product can be found on the product page.
                </p>
              </div>
            </motion.div>

            {/* Contact for Return Questions - Zara Style */}
            <motion.div
              className="mt-16 border-t border-black/5 pt-12"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-light mb-4">Need help with a return?</h3>
              <p className="text-sm text-gray-600 mb-8 max-w-lg">
                Our customer support team is here to assist you with any questions about returns or exchanges.
                Feel free to reach out to us for assistance.
              </p>
              <div className="space-x-6">
                <Link
                  href="/contact"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                >
                  Contact Us
                </Link>
                <Link
                  href="/"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black/50 pb-1 hover:opacity-70 transition-opacity"
                >
                  Return to Home
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}