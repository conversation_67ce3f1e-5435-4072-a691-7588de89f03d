import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';
import User from '@/models/User';
import Product from '@/models/Product';
import OrderHistory from '@/models/OrderHistory';
import { sendOrderStatusUpdateEmail } from '@/lib/email';
import mongoose from 'mongoose';

export async function GET(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;

    // Find the order with populated user and product details
    const order = await Order.findById(orderId)
      .populate('user', 'name email phone')
      .populate('items.product', 'name images price stock');

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}

export async function PATCH(request, context) {
  try {
    const params = await context.params;
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { orderId } = params;
    const { status } = await request.json();

    // Validate status
    const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Find order and update status
    const order = await Order.findById(orderId);
    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Store the previous status before updating
    const previousStatus = order.status;

    // Only proceed if the status is actually changing
    if (previousStatus === status) {
      return NextResponse.json({
        message: 'Order status unchanged',
        order: {
          _id: order._id,
          status: order.status,
          updatedAt: order.updatedAt
        }
      });
    }

    // Start a transaction if we're cancelling the order (to restore stock)
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Store previous status
      const previousStatus = order.status;

      // Update order status
      order.status = status;
      await order.save({ session });

      // Create order history entry
      const orderHistory = new OrderHistory({
        order: order._id,
        status,
        previousStatus,
        updatedBy: user._id,
        updatedAt: new Date()
      });
      await orderHistory.save({ session });

      // If order is being cancelled, restore stock
      if (status === 'cancelled' && previousStatus !== 'cancelled') {
        // Populate the order items with product references
        await order.populate('items.product');

        // Restore stock for each item
        for (const item of order.items) {
          if (item.product) {
            const product = await Product.findById(item.product).session(session);
            if (product) {
              product.stock += item.quantity;
              await product.save({ session });
              console.log(`Restored ${item.quantity} units to product ${product._id} stock`);
            }
          }
        }
      }

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      // Send notification email to user
      try {
        // Get user details
        const customer = await User.findById(order.user);

        if (customer && customer.email) {
          await sendOrderStatusUpdateEmail(
            customer.email,
            customer.name || 'Valued Customer',
            order,
            previousStatus
          );
          console.log(`Order status update email sent to ${customer.email} for order ${order._id}`);
        } else {
          console.warn(`Could not send order status update email: User details not found for order ${order._id}`);
        }
      } catch (emailError) {
        // Log the error but don't fail the status update
        console.error('Failed to send order status update email:', emailError);
      }

      return NextResponse.json({
        message: 'Order status updated successfully',
        order: {
          _id: order._id,
          orderNumber: order.orderNumber,
          status: order.status,
          updatedAt: order.updatedAt
        }
      });
    } catch (transactionError) {
      // If there's an error, abort the transaction
      await session.abortTransaction();
      session.endSession();
      console.error('Transaction error during order status update:', transactionError);
      return NextResponse.json(
        { error: 'Failed to update order status' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error updating order status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}