// Test script to verify zero-total orders with gift cards
const fetch = require('node-fetch');

async function testZeroTotalOrder() {
  try {
    console.log('Testing zero-total order creation with gift card...');
    
    // Mock order data with zero total and gift card payment
    const orderData = {
      items: [
        {
          _id: '60d21b4667d0d8992e610c85', // Replace with a valid product ID
          quantity: 1,
          price: 1000,
          originalPrice: 1000,
          discount: 0,
          name: 'Test Product',
          images: ['https://example.com/image.jpg']
        }
      ],
      total: 0, // Zero total
      shippingAddress: 'Test Address',
      paymentMethod: 'giftcard',
      giftCardCode: 'TEST123',
      giftCardAmount: 1000
    };
    
    // Make the API request
    const response = await fetch('http://localhost:3000/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'your_auth_cookie_here' // Replace with a valid auth cookie
      },
      body: JSON.stringify(orderData),
    });
    
    // Log the response
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', data);
    
    if (response.ok) {
      console.log('✅ Test passed: Zero-total order with gift card was created successfully');
    } else {
      console.log('❌ Test failed: Zero-total order with gift card was rejected');
      console.log('Error:', data.error);
    }
  } catch (error) {
    console.error('Test error:', error);
  }
}

testZeroTotalOrder();
