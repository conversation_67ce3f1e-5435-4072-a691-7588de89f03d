import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Newsletter from '@/models/Newsletter';

export async function GET(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const sortField = searchParams.get('sort') || 'subscribedAt';
    const sortDirection = searchParams.get('direction') === 'asc' ? 1 : -1;
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const includeStats = searchParams.get('includeStats') === 'true';

    // Build query
    let query = {};

    // Filter by status if provided
    if (status && ['active', 'unsubscribed'].includes(status)) {
      query.status = status;
    }

    // Search by email if provided
    if (search) {
      query.email = { $regex: search, $options: 'i' };
    }

    // Build sort object
    const sort = { [sortField]: sortDirection };

    // Count total documents for pagination
    const total = await Newsletter.countDocuments(query);

    // Fetch paginated subscribers
    const subscribers = await Newsletter.find(query)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    // Calculate statistics
    let stats = null;
    if (includeStats) {
      // Get current date and date ranges
      const now = new Date();
      const today = new Date(now);
      today.setHours(0, 0, 0, 0);

      const weekAgo = new Date(now);
      weekAgo.setDate(weekAgo.getDate() - 7);

      const monthAgo = new Date(now);
      monthAgo.setDate(monthAgo.getDate() - 30);

      // Count active subscribers
      const activeCount = await Newsletter.countDocuments({ status: 'active' });

      // Count unsubscribed
      const unsubscribedCount = await Newsletter.countDocuments({ status: 'unsubscribed' });

      // Count recent subscribers (last 7 days)
      const recentCount = await Newsletter.countDocuments({
        subscribedAt: { $gte: weekAgo }
      });

      // Count today's subscribers
      const todayCount = await Newsletter.countDocuments({
        subscribedAt: { $gte: today }
      });

      stats = {
        total: total,
        active: activeCount,
        unsubscribed: unsubscribedCount,
        recent: recentCount,
        today: todayCount
      };
    }

    return NextResponse.json({
      subscribers,
      total,
      pages: Math.ceil(total / limit),
      currentPage: page,
      stats
    });
  } catch (error) {
    console.error('Error fetching newsletter subscribers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch newsletter subscribers' },
      { status: 500 }
    );
  }
}

// Delete subscribers
export async function DELETE(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No subscriber IDs provided' },
        { status: 400 }
      );
    }

    // Delete subscribers
    const result = await Newsletter.deleteMany({ _id: { $in: ids } });

    return NextResponse.json({
      message: `${result.deletedCount} subscribers deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting subscribers:', error);
    return NextResponse.json(
      { error: 'Failed to delete subscribers' },
      { status: 500 }
    );
  }
}

// Update subscriber status (active/unsubscribed)
export async function PATCH(request) {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    const { ids, status } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No subscriber IDs provided' },
        { status: 400 }
      );
    }

    if (!status || !['active', 'unsubscribed'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status provided' },
        { status: 400 }
      );
    }

    // Update subscribers
    const result = await Newsletter.updateMany(
      { _id: { $in: ids } },
      { $set: { status } }
    );

    return NextResponse.json({
      message: `${result.modifiedCount} subscribers updated successfully`
    });
  } catch (error) {
    console.error('Error updating subscribers:', error);
    return NextResponse.json(
      { error: 'Failed to update subscribers' },
      { status: 500 }
    );
  }
}
