'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    try {
      const response = await fetch('/api/auth/check');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error('Error checking user:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        setUser(data.user);

        // Clear existing cart, wishlist, and coupon data from localStorage
        // This ensures we don't mix anonymous user data with logged-in user data
        if (typeof window !== 'undefined') {
          localStorage.removeItem('cart');
          localStorage.removeItem('wishlist');
          localStorage.removeItem('coupon');
        }

        // Redirect to home page and refresh the page
        router.push('/');

        // Use a small timeout to ensure navigation starts before refreshing
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      return { success: false, error: 'An error occurred during login' };
    }
  };

  const register = async (name, email, password) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, email, password }),
      });

      const data = await response.json();

      if (response.ok) {
        setUser(data.user);

        // Clear existing cart, wishlist, and coupon data from localStorage
        // This ensures we don't mix anonymous user data with logged-in user data
        if (typeof window !== 'undefined') {
          localStorage.removeItem('cart');
          localStorage.removeItem('wishlist');
          localStorage.removeItem('coupon');
        }

        // Redirect to home page and refresh the page
        router.push('/');

        // Use a small timeout to ensure navigation starts before refreshing
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true };
      } else {
        return { success: false, error: data.error };
      }
    } catch (error) {
      return { success: false, error: 'An error occurred during registration' };
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      setUser(null);

      // Clear cart, wishlist, and coupon data from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('cart');
        localStorage.removeItem('wishlist');
        localStorage.removeItem('coupon');
      }

      // Redirect to login page and refresh the page
      router.push('/login');
      // Use a small timeout to ensure navigation starts before refreshing
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}