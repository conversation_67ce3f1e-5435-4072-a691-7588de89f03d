'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import { FiArrowLeft, FiLoader } from 'react-icons/fi';

export default function CreateGiftCardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    amount: 1000,
    recipientName: '',
    recipientEmail: '',
    senderName: '',
    senderEmail: '',
    message: '',
    expiresAt: getDefaultExpiryDate()
  });

  // Set default expiry date to 1 year from now
  function getDefaultExpiryDate() {
    const date = new Date();
    date.setFullYear(date.getFullYear() + 1);
    return date.toISOString().split('T')[0];
  }

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? Number(value) : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      // Validate form
      if (!formData.amount || formData.amount < 100) {
        throw new Error('Gift card amount must be at least ₹100');
      }

      if (!formData.recipientName) {
        throw new Error('Recipient name is required');
      }

      if (!formData.recipientEmail) {
        throw new Error('Recipient email is required');
      }

      if (!formData.senderName) {
        throw new Error('Sender name is required');
      }

      if (!formData.senderEmail) {
        throw new Error('Sender email is required');
      }

      // Create gift card
      const response = await fetch('/api/admin/gift-cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create gift card');
      }

      toast.success('Gift card created successfully');
      router.push('/admin/gift-cards');
    } catch (error) {
      console.error('Error creating gift card:', error);
      toast.error(error.message || 'Failed to create gift card');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center">
          <button
            onClick={() => router.back()}
            className="mr-4 text-gray-500 hover:text-black"
          >
            <FiArrowLeft size={20} />
          </button>
          <h1 className="text-xs tracking-[0.2em] text-gray-400">CREATE GIFT CARD</h1>
        </div>

        <div className="max-w-2xl mx-auto bg-white border border-black/10">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Amount */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                AMOUNT (₹)
              </label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                min="100"
                step="100"
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                required
              />
              <p className="mt-1 text-xs text-gray-500">Minimum amount: ₹100</p>
            </div>

            {/* Recipient Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  RECIPIENT NAME
                </label>
                <input
                  type="text"
                  name="recipientName"
                  value={formData.recipientName}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  RECIPIENT EMAIL
                </label>
                <input
                  type="email"
                  name="recipientEmail"
                  value={formData.recipientEmail}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
            </div>

            {/* Sender Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  SENDER NAME
                </label>
                <input
                  type="text"
                  name="senderName"
                  value={formData.senderName}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
              <div>
                <label className="block text-xs tracking-[0.2em] mb-2">
                  SENDER EMAIL
                </label>
                <input
                  type="email"
                  name="senderEmail"
                  value={formData.senderEmail}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  required
                />
              </div>
            </div>

            {/* Message */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                MESSAGE (OPTIONAL)
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                rows="3"
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
              ></textarea>
            </div>

            {/* Expiration Date */}
            <div>
              <label className="block text-xs tracking-[0.2em] mb-2">
                EXPIRATION DATE
              </label>
              <input
                type="date"
                name="expiresAt"
                value={formData.expiresAt}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                required
              />
              <p className="mt-1 text-xs text-gray-500">Default: 1 year from today</p>
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <button
                type="submit"
                className="w-full bg-black text-white py-3 px-6 hover:bg-gray-800 transition-colors flex items-center justify-center text-xs tracking-[0.2em]"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <FiLoader className="animate-spin mr-2" />
                    CREATING...
                  </>
                ) : (
                  'CREATE GIFT CARD'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
