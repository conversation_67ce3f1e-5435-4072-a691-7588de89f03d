'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import {
  FiSearch,
  FiEdit,
  FiTrash2,
  FiCheck,
  FiX,
  FiPlus,
  FiFilter,
  FiArrowLeft,
  FiRefreshCw,
  FiClock,
  FiTag,
  FiCalendar
} from 'react-icons/fi';
import Pagination from '@/components/admin/Pagination';

export default function AdminCouponsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [couponToDelete, setCouponToDelete] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [sortConfig, setSortConfig] = useState({
    field: 'createdAt',
    direction: 'desc'
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    expiringSoon: 0,
    expired: 0
  });
  const itemsPerPage = 10;

  useEffect(() => {
    // Check if user is admin
    if (!authLoading && user) {
      if (user.role !== 'admin') {
        toast.error('You do not have permission to access this page');
        router.push('/');
      } else {
        fetchCoupons();
      }
    } else if (!authLoading && !user) {
      toast.error('Please login to continue');
      router.push('/login');
    }
  }, [user, authLoading, router, currentPage, searchQuery, selectedStatus]);

  const fetchCoupons = async () => {
    try {
      setLoading(true);

      // Build query parameters
      let url = '/api/admin/coupons';
      const params = new URLSearchParams();

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      if (selectedStatus !== 'all') {
        params.append('status', selectedStatus);
      }

      params.append('page', currentPage);
      params.append('limit', itemsPerPage);
      params.append('includeStats', 'true');
      params.append('sort', sortConfig.field);
      params.append('direction', sortConfig.direction);

      // Add timestamp to prevent caching
      params.append('_', Date.now());

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch coupons');
      }

      const data = await response.json();

      setCoupons(data.coupons || []);
      setTotalPages(data.pagination?.pages || 1);

      // Update statistics if available
      if (data.stats) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching coupons:', error);
      toast.error('Failed to load coupons');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCoupons();
  };

  const handleStatusChange = async (couponId, isActive) => {
    try {
      const response = await fetch(`/api/admin/coupons/${couponId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update coupon status');
      }

      toast.success(`Coupon ${isActive ? 'activated' : 'deactivated'} successfully`);
      fetchCoupons();
    } catch (error) {
      console.error('Error updating coupon status:', error);
      toast.error(error.message || 'Failed to update coupon status');
    }
  };

  const handleDeleteCoupon = (id) => {
    setCouponToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDeleteCoupon = async () => {
    if (!couponToDelete) return;

    try {
      const response = await fetch(`/api/admin/coupons/${couponToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete coupon');
      }

      toast.success('Coupon deleted successfully');
      setShowDeleteModal(false);
      setCouponToDelete(null);
      fetchCoupons();
    } catch (error) {
      console.error('Error deleting coupon:', error);
      toast.error(error.message || 'Failed to delete coupon');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount) => {
    return `₹${Number(amount).toLocaleString('en-IN')}`;
  };

  const getStatusBadgeClass = (isActive) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const handleSort = (field) => {
    setSortConfig(prevConfig => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
    setCurrentPage(1);
    fetchCoupons();
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <div className="mb-4">
          <button
            onClick={() => router.push('/admin')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO ADMIN
          </button>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xs tracking-[0.2em] text-gray-400">COUPONS</h1>
          <div className="flex gap-2">
            <button
              onClick={() => fetchCoupons()}
              className="bg-gray-800 text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
            >
              <FiRefreshCw className="mr-2" />
              REFRESH
            </button>
            <button
              onClick={() => router.push('/admin/coupons/create')}
              className="bg-black text-white px-4 py-2 text-xs tracking-[0.2em] flex items-center"
            >
              <FiPlus className="mr-2" />
              CREATE COUPON
            </button>
          </div>
        </div>

        {/* Coupon Statistics */}
        <div className="grid grid-cols-5 gap-4 mb-6">
          <div className="p-4 border border-gray-200 bg-gray-50">
            <p className="text-sm">TOTAL COUPONS</p>
            <p className="text-2xl font-medium">{stats.total}</p>
          </div>
          <div className="p-4 border border-green-100 bg-green-50">
            <p className="text-sm text-green-700">ACTIVE</p>
            <p className="text-2xl font-medium text-green-700">{stats.active}</p>
          </div>
          <div className="p-4 border border-red-100 bg-red-50">
            <p className="text-sm text-red-700">INACTIVE</p>
            <p className="text-2xl font-medium text-red-700">{stats.inactive}</p>
          </div>
          <div className="p-4 border border-yellow-100 bg-yellow-50">
            <p className="text-sm text-yellow-700">EXPIRING SOON</p>
            <p className="text-2xl font-medium text-yellow-700">{stats.expiringSoon}</p>
          </div>
          <div className="p-4 border border-purple-100 bg-purple-50">
            <p className="text-sm text-purple-700">EXPIRED</p>
            <p className="text-2xl font-medium text-purple-700">{stats.expired}</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6 bg-white border border-black/10">
          <div className="p-4">
            <form onSubmit={handleSearch} className="flex items-center">
              <div className="relative flex-grow">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by code or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-transparent border-none focus:ring-0 text-sm"
                />
              </div>
              <button
                type="submit"
                className="ml-2 px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
              >
                SEARCH
              </button>
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="ml-2 px-4 py-2 border border-black/10 text-xs tracking-[0.2em] flex items-center"
              >
                <FiFilter className="mr-2" />
                FILTER
              </button>
            </form>
          </div>

          {showFilters && (
            <div className="p-4 border-t border-black/10 bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs tracking-[0.2em] mb-2">STATUS</label>
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  >
                    <option value="all">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="expiring">Expiring Soon</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs tracking-[0.2em] mb-2">SORT BY</label>
                  <select
                    value={`${sortConfig.field}-${sortConfig.direction}`}
                    onChange={(e) => {
                      const [field, direction] = e.target.value.split('-');
                      setSortConfig({ field, direction });
                    }}
                    className="w-full p-2 border border-gray-300 focus:ring-0 focus:border-black"
                  >
                    <option value="createdAt-desc">Date Created (Newest First)</option>
                    <option value="createdAt-asc">Date Created (Oldest First)</option>
                    <option value="expiryDate-asc">Expiry Date (Soonest First)</option>
                    <option value="expiryDate-desc">Expiry Date (Latest First)</option>
                    <option value="code-asc">Code (A-Z)</option>
                    <option value="code-desc">Code (Z-A)</option>
                    <option value="value-desc">Value (Highest First)</option>
                    <option value="value-asc">Value (Lowest First)</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    setShowFilters(false);
                    fetchCoupons();
                  }}
                  className="px-4 py-2 bg-black text-white text-xs tracking-[0.2em]"
                >
                  APPLY FILTERS
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Coupons Table */}
        <div className="overflow-x-auto bg-white border border-black/10">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('code')}
                >
                  Code
                </th>
                <th className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Type
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('value')}
                >
                  Value
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('minPurchase')}
                >
                  Min Purchase
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('usedCount')}
                >
                  Usage
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('isActive')}
                >
                  Status
                </th>
                <th
                  className="px-6 py-3 text-left text-xs tracking-[0.2em] text-gray-500 uppercase cursor-pointer"
                  onClick={() => handleSort('expiryDate')}
                >
                  Expires
                </th>
                <th className="px-6 py-3 text-right text-xs tracking-[0.2em] text-gray-500 uppercase">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading coupons...
                  </td>
                </tr>
              ) : coupons.length === 0 ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4 text-center text-sm text-gray-500">
                    No coupons found
                  </td>
                </tr>
              ) : (
                coupons.map((coupon) => (
                  <tr key={coupon._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {coupon.code}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                      {coupon.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {coupon.type === 'percentage' ? `${coupon.value}%` : formatCurrency(coupon.value)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {coupon.minPurchase > 0 ? formatCurrency(coupon.minPurchase) : 'None'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {coupon.usedCount} / {coupon.usageLimit || '∞'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(coupon.isActive)}`}>
                        {coupon.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(coupon.expiryDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => router.push(`/admin/coupons/${coupon._id}`)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Edit"
                        >
                          <FiEdit size={18} />
                        </button>
                        <button
                          onClick={() => handleDeleteCoupon(coupon._id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <FiTrash2 size={18} />
                        </button>
                        {coupon.isActive && (
                          <button
                            onClick={() => handleStatusChange(coupon._id, false)}
                            className="text-red-600 hover:text-red-900"
                            title="Deactivate"
                          >
                            <FiX size={18} />
                          </button>
                        )}
                        {!coupon.isActive && (
                          <button
                            onClick={() => handleStatusChange(coupon._id, true)}
                            className="text-green-600 hover:text-green-900"
                            title="Activate"
                          >
                            <FiCheck size={18} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center p-4 z-50">
            <div className="bg-white w-full max-w-md rounded-lg overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xs tracking-[0.2em] text-gray-400">CONFIRM DELETION</h2>
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="text-xs tracking-[0.2em] hover:opacity-50"
                  >
                    CLOSE
                  </button>
                </div>

                <div className="mb-6">
                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mx-auto mb-4">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <p className="text-center text-sm mb-2">
                    Are you sure you want to delete this coupon?
                  </p>
                  <p className="text-center text-xs text-gray-500">
                    This action cannot be undone.
                  </p>
                </div>

                <div className="flex justify-end gap-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setShowDeleteModal(false)}
                    className="px-4 py-2 text-xs tracking-[0.2em] border border-gray-200 hover:bg-gray-50"
                  >
                    CANCEL
                  </button>
                  <button
                    onClick={confirmDeleteCoupon}
                    className="px-4 py-2 text-xs tracking-[0.2em] bg-red-600 text-white hover:bg-red-700"
                  >
                    DELETE
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
