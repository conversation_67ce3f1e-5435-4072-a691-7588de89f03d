'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FiAlertCircle, FiInfo } from 'react-icons/fi';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailStatus, setEmailStatus] = useState({
    checking: false,
    exists: false,
    checked: false
  });
  const emailCheckTimeoutRef = useRef(null);
  const router = useRouter();

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const checkEmailExists = async (email) => {
    if (!validateEmail(email)) return;

    setEmailStatus(prev => ({ ...prev, checking: true }));

    try {
      const response = await fetch('/api/auth/check-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (response.ok) {
        setEmailStatus({
          checking: false,
          exists: data.exists,
          checked: true
        });
      } else {
        throw new Error(data.error || 'Failed to check email');
      }
    } catch (error) {
      console.error('Error checking email:', error);
      setEmailStatus({
        checking: false,
        exists: false,
        checked: false
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');

    // If email is valid but hasn't been checked yet, check it now
    if (validateEmail(email) && !emailStatus.checked) {
      await checkEmailExists(email);
    }

    // Validate that the email exists before proceeding
    if (validateEmail(email) && emailStatus.checked && !emailStatus.exists) {
      // We'll show the error message through the UI instead of setting the error state
      // This allows us to show a more helpful message with a link
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong');
      }

      setMessage('Password reset instructions have been sent to your email.');
      setEmail('');
      setEmailStatus({
        checking: false,
        exists: false,
        checked: false
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmail(value);
    setError('');
    setMessage('');

    // Check email existence with debounce
    if (validateEmail(value)) {
      // Clear any existing timeout
      if (emailCheckTimeoutRef.current) {
        clearTimeout(emailCheckTimeoutRef.current);
      }

      // Set a new timeout
      emailCheckTimeoutRef.current = setTimeout(() => {
        checkEmailExists(value);
      }, 500); // 500ms debounce
    } else if (!value) {
      // Reset email status when field is cleared
      setEmailStatus({
        checking: false,
        exists: false,
        checked: false
      });
    }
  };

  return (
    <div className="min-h-screen px-4 py-20">
      <div className="max-w-[460px] mx-auto">
        <div className="mb-12">
          <h1 className="text-xs tracking-widest mb-6">HAVE YOU FORGOTTEN YOUR PASSWORD?</h1>
          <p className="text-xs text-black/60 leading-relaxed">
            Enter your email address and we will send you an email with instructions on how to reset your password.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="space-y-6">
            <div>
              <label htmlFor="email" className="text-xs">
                E-MAIL
              </label>
              <div className="relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  className={`w-full bg-transparent border-0 border-b ${emailStatus.checked && !emailStatus.exists ? 'border-red-500' : emailStatus.checked && emailStatus.exists ? 'border-green-500' : 'border-black/20'} px-0 py-2
                    text-sm placeholder:text-black/40 focus:ring-0 focus:border-black pr-10`}
                  value={email}
                  onChange={handleEmailChange}
                  onBlur={() => {
                    if (validateEmail(email)) {
                      checkEmailExists(email);
                    }
                  }}
                />
                {emailStatus.checking && (
                  <span className="absolute right-0 top-2 text-gray-400">
                    <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                )}
                {emailStatus.checked && !emailStatus.checking && !emailStatus.exists && (
                  <span className="absolute right-0 top-2 text-red-500">
                    <FiAlertCircle className="h-5 w-5" />
                  </span>
                )}
                {emailStatus.checked && !emailStatus.checking && emailStatus.exists && (
                  <span className="absolute right-0 top-2 text-green-500">
                    <FiInfo className="h-5 w-5" />
                  </span>
                )}
              </div>
              {emailStatus.checked && !emailStatus.exists && !error && (
                <div className="text-xs text-red-500 mt-1">
                  <p>No account found with this email.</p>
                  <p className="mt-1">
                    <Link href="/login" className="underline hover:text-red-700">
                      Click here to create an account
                    </Link>
                  </p>
                </div>
              )}
            </div>

            {error && (
              <p className="text-xs text-red-500">{error}</p>
            )}

            {message && (
              <p className="text-xs text-green-600">{message}</p>
            )}
          </div>

          <div className="space-y-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-black text-white py-3 text-xs tracking-widest
                hover:bg-black/90 disabled:bg-black/70 disabled:cursor-not-allowed"
            >
              {isLoading ? 'SENDING...' : 'CONTINUE'}
            </button>

            <Link
              href="/login"
              className="block w-full border border-black py-3 text-xs tracking-widest text-center
                hover:bg-black hover:text-white transition-colors duration-200"
            >
              BACK TO LOGIN
            </Link>
          </div>
        </form>

        <div className="mt-12">
          <h2 className="text-xs tracking-widest mb-4">NEED HELP?</h2>
          <Link
            href="/contact"
            className="text-xs text-black/60 hover:text-black hover:underline"
          >
            Contact Customer Service
          </Link>
        </div>
      </div>
    </div>
  );
}
