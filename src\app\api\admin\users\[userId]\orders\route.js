import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';

export async function GET(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')) : null;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')) : 1;
    const pageSize = searchParams.get('pageSize') ? parseInt(searchParams.get('pageSize')) : 10;
    const status = searchParams.get('status');

    // Build query
    const query = { user: userId };
    if (status && status !== 'all') {
      query.status = status;
    }

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Create base query
    let ordersQuery = Order.find(query)
      .populate('items.product', 'name images price')
      .sort({ createdAt: -1 });

    // Apply limit if specified
    if (limit) {
      ordersQuery = ordersQuery.limit(limit);
    } else {
      // Apply pagination
      ordersQuery = ordersQuery.skip(skip).limit(pageSize);
    }

    // Execute query
    const orders = await ordersQuery;

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(query);

    return NextResponse.json(orders, {
      headers: {
        'X-Total-Count': totalOrders.toString(),
        'X-Total-Pages': Math.ceil(totalOrders / pageSize).toString()
      }
    });
  } catch (error) {
    console.error('Error fetching user orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
