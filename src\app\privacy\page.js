'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';

export default function PrivacyPolicyPage() {
  return (
    <div className="bg-white min-h-screen pt-10 md:pt-16">
      {/* Hero Section - Zara Style */}
      <section className="relative py-8 md:py-12 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.p 
              className="text-xs uppercase tracking-wider text-gray-500 mb-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              Legal Information
            </motion.p>
            <motion.h1 
              className="text-2xl md:text-3xl font-light text-black mb-6"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              Privacy Policy
            </motion.h1>
            <motion.p 
              className="text-sm md:text-base text-gray-600 max-w-2xl"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              Last Updated: March 16, 2024
            </motion.p>
          </div>
        </div>
      </section>

      {/* Content Section - Zara Style */}
      <section className="py-12 md:py-16">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="max-w-3xl">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <p className="text-sm text-gray-600 mb-12">
                At FurnitureBazaar, we value your privacy and are committed to protecting your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website or make a purchase.
              </p>

              <div className="space-y-12">
                {/* Information We Collect */}
                <div className="border-t border-black/5 pt-6">
                  <h2 className="text-xl font-light text-black mb-6">Information We Collect</h2>
                  <p className="text-sm text-gray-600 mb-4">We collect information that you provide directly to us, such as when you:</p>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Create an account</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Make a purchase</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Sign up for our newsletter</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Contact our customer service</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Participate in surveys or promotions</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Post reviews or comments</p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4">The types of information we may collect include:</p>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Personal Information:</span> Name, email address, postal address, phone number, and payment information.</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Account Information:</span> Username, password, and purchase history.</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Transaction Information:</span> Details about purchases made through our website.</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm"><span className="text-black">Communication Information:</span> Records of your interactions with our customer service team.</p>
                    </div>
                  </div>
                </div>

                {/* How We Use Your Information */}
                <div className="border-t border-black/5 pt-6">
                  <h2 className="text-xl font-light text-black mb-6">How We Use Your Information</h2>
                  <p className="text-sm text-gray-600 mb-4">We use the information we collect for various purposes, including to:</p>
                  <div className="space-y-3 mb-6">
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Process and fulfill your orders</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Create and manage your account</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Provide customer service and respond to inquiries</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Send transactional emails and order confirmations</p>
                    </div>
                    <div className="flex items-start">
                      <div className="w-1 h-1 rounded-full bg-black mt-2 mr-3"></div>
                      <p className="text-sm">Send marketing communications (with your consent)</p>
                    </div>
                  </div>
                </div>

                {/* Contact Us */}
                <div className="border-t border-black/5 pt-6">
                  <h2 className="text-xl font-light text-black mb-6">Contact Us</h2>
                  <p className="text-sm text-gray-600 mb-6">If you have any questions or concerns about this Privacy Policy or our privacy practices, please contact us at:</p>
                  <div className="border-t border-black/5 pt-6 space-y-2 text-sm">
                    <p><span className="text-black">FurnitureBazaar</span></p>
                    <p>123 Furniture Street</p>
                    <p>Surat, Gujarat 395007</p>
                    <p>India</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +91 80 1234 5678</p>
                  </div>
                </div>
              </div>

              <div className="mt-16 border-t border-black/5 pt-8">
                <Link
                  href="/"
                  className="inline-flex items-center text-xs uppercase tracking-wider border-b border-black pb-1 hover:opacity-70 transition-opacity"
                >
                  Return to Home
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
