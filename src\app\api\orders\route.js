import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Order from '@/models/Order';
import User from '@/models/User';
import Product from '@/models/Product';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';
import { sendOrderConfirmationEmail } from '@/lib/email';
import { generateOrderNumber } from '@/lib/orderUtils';
import mongoose from 'mongoose';

export async function POST(request) {
  try {
    // Ensure proper headers
    const contentType = request.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: 'Content-Type must be application/json' },
        { status: 400 }
      );
    }

    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    try {
      await connectToDB();
    } catch (dbError) {
      console.error('Database connection error:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Parse request body with error handling
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    const {
      items,
      total,
      subtotal,
      shippingAddress,
      paymentMethod = 'cod',
      paymentId = null,
      giftCardCode = null,
      giftCardAmount = 0,
      couponCode = null,
      couponDiscount = 0
    } = body;

    // Detailed validation
    if (!Array.isArray(items)) {
      return NextResponse.json(
        { error: 'Items must be an array' },
        { status: 400 }
      );
    }

    if (items.length === 0) {
      return NextResponse.json(
        { error: 'Order must contain at least one item' },
        { status: 400 }
      );
    }

    // Allow zero total only when using gift card payment
    if (typeof total !== 'number' || (total <= 0 && paymentMethod !== 'giftcard')) {
      return NextResponse.json(
        { error: 'Invalid total amount' },
        { status: 400 }
      );
    }

    // Validate subtotal
    if (typeof subtotal !== 'number' || subtotal < 0) {
      return NextResponse.json(
        { error: 'Invalid subtotal amount' },
        { status: 400 }
      );
    }

    if (!shippingAddress || typeof shippingAddress !== 'string') {
      return NextResponse.json(
        { error: 'Shipping address is required' },
        { status: 400 }
      );
    }

    // Validate each item
    for (const item of items) {
      if (!item._id) {
        console.error('Missing item ID:', item);
        return NextResponse.json(
          { error: 'Missing item ID' },
          { status: 400 }
        );
      }
      if (!item.quantity || typeof item.quantity !== 'number' || item.quantity <= 0) {
        console.error('Invalid quantity:', item);
        return NextResponse.json(
          { error: 'Invalid item quantity' },
          { status: 400 }
        );
      }
      if (!item.price || typeof item.price !== 'number' || item.price <= 0) {
        console.error('Invalid price:', item);
        return NextResponse.json(
          { error: 'Invalid item price' },
          { status: 400 }
        );
      }
      if (!item.name || typeof item.name !== 'string') {
        console.error('Invalid name:', item);
        return NextResponse.json(
          { error: 'Invalid item name' },
          { status: 400 }
        );
      }
      if (!Array.isArray(item.images) || item.images.length === 0) {
        console.error('Invalid images:', item);
        return NextResponse.json(
          { error: 'Invalid item images' },
          { status: 400 }
        );
      }
    }

    // Check stock availability and update product stock
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Check stock availability for all items
      for (const item of items) {
        const product = await Product.findById(item._id).session(session);

        if (!product) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: `Product not found: ${item.name}` },
            { status: 404 }
          );
        }

        if (product.stock < item.quantity) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            {
              error: `Insufficient stock for ${item.name}. Available: ${product.stock}, Requested: ${item.quantity}`
            },
            { status: 400 }
          );
        }

        // Update product stock
        product.stock -= item.quantity;
        await product.save({ session });
      }

      // Handle gift card redemption if applicable
      let giftCardData = null;
      if (giftCardCode && giftCardAmount > 0) {
        // Find the gift card
        const giftCard = await GiftCard.findOne({ code: giftCardCode }).session(session);

        if (!giftCard) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: 'Gift card not found' },
            { status: 404 }
          );
        }

        // Validate gift card
        if (!giftCard.isValid()) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: 'Gift card is not valid' },
            { status: 400 }
          );
        }

        // Check if gift card has enough balance
        if (giftCard.balance < giftCardAmount) {
          await session.abortTransaction();
          session.endSession();
          return NextResponse.json(
            { error: 'Insufficient gift card balance' },
            { status: 400 }
          );
        }

        // Store gift card data for later use
        giftCardData = giftCard;
      }

      // Generate order number
      const orderNumber = await generateOrderNumber();

      // Create order
      const order = new Order({
        user: user._id,
        items: items.map(item => ({
          product: item._id,
          quantity: item.quantity,
          price: item.price,
          originalPrice: item.originalPrice || item.price,
          discount: item.discount || 0,
          name: item.name,
          image: item.images[0]
        })),
        total,
        subtotal: subtotal || total, // Use provided subtotal or fallback to total
        shippingAddress,
        orderNumber,
        status: 'pending',
        paymentMethod,
        paymentStatus: paymentId ? 'paid' : 'pending',
        paymentId,
        giftCardCode,
        giftCardAmount,
        couponCode,
        couponDiscount,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Save order
      const savedOrder = await order.save({ session });

      // Redeem gift card if applicable
      if (giftCardData && giftCardAmount > 0) {
        // Update gift card balance
        giftCardData.balance -= giftCardAmount;

        if (giftCardData.balance === 0) {
          giftCardData.status = 'redeemed';
          giftCardData.redeemedAt = new Date();
        }

        if (!giftCardData.redeemedBy) {
          giftCardData.redeemedBy = user._id;
        }

        giftCardData.orderIds.push(savedOrder._id);

        await giftCardData.save({ session });
      }

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      console.log('Order created successfully and stock updated:', savedOrder._id);

      // Send order confirmation email
      try {
        // Get user's full details
        const userDetails = await User.findById(user._id);

        if (userDetails && userDetails.email) {
          await sendOrderConfirmationEmail(
            userDetails.email,
            userDetails.name || 'Valued Customer',
            savedOrder
          );
          console.log('Order confirmation email sent to:', userDetails.email);
        } else {
          console.warn('Could not send order confirmation email: User details not found');
        }
      } catch (emailError) {
        // Log the error but don't fail the order creation
        console.error('Failed to send order confirmation email:', emailError);
      }

      return new NextResponse(JSON.stringify(savedOrder), {
        status: 201,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (saveError) {
      // Abort the transaction if it's still active
      if (session.inTransaction()) {
        await session.abortTransaction();
      }
      session.endSession();

      console.error('Error saving order:', {
        error: saveError.message,
        name: saveError.name,
        validationErrors: saveError.errors ? Object.keys(saveError.errors).map(key => ({
          field: key,
          message: saveError.errors[key].message
        })) : undefined,
        orderData: {
          user: order.user,
          items: order.items.length,
          total: order.total,
          subtotal: order.subtotal,
          paymentMethod: order.paymentMethod
        }
      });
      throw saveError;
    }
  } catch (error) {
    console.error('Order creation error:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      errors: error.errors ? Object.keys(error.errors).map(key => ({
        field: key,
        message: error.errors[key].message
      })) : undefined
    });

    // Handle specific error types
    if (error.name === 'ValidationError') {
      return new NextResponse(
        JSON.stringify({
          error: 'Validation error: ' + Object.values(error.errors).map(err => err.message).join(', ')
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    return new NextResponse(
      JSON.stringify({
        error: 'Failed to create order. Please try again.'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}