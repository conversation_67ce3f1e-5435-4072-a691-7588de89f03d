import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';

export async function GET(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;

    // Get total orders count
    const total = await Order.countDocuments({ user: userId });

    // Get counts by status
    const pending = await Order.countDocuments({ user: userId, status: 'pending' });
    const processing = await Order.countDocuments({ user: userId, status: 'processing' });
    const shipped = await Order.countDocuments({ user: userId, status: 'shipped' });
    const delivered = await Order.countDocuments({ user: userId, status: 'delivered' });
    const cancelled = await Order.countDocuments({ user: userId, status: 'cancelled' });

    // Calculate total spent
    const orders = await Order.find({ user: userId, status: { $ne: 'cancelled' } });
    const totalSpent = orders.reduce((sum, order) => sum + (order.total || 0), 0);

    return NextResponse.json({
      total,
      pending,
      processing,
      shipped,
      delivered,
      cancelled,
      totalSpent
    });
  } catch (error) {
    console.error('Error fetching user order stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
