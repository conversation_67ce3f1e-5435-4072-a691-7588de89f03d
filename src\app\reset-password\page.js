'use client';

import { useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { FiAlertCircle, FiCheckCircle, FiLock, FiEye, FiEyeOff } from 'react-icons/fi';

function ResetPasswordContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Password validation
  const validatePassword = (password) => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Validate password
    const passwordError = validatePassword(password);
    if (passwordError) {
      setError(passwordError);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong');
      }

      setSuccess(true);
      toast.success('Password reset successful');

      // Redirect to login page after 3 seconds
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    } catch (error) {
      setError(error.message);
      toast.error(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // If no token is provided, show an error
  if (!token) {
    return (
      <div className="min-h-screen px-4 py-20">
        <div className="max-w-[460px] mx-auto">
          <div className="mb-8">
            <h1 className="text-xs tracking-widest mb-6">RESET PASSWORD</h1>
          </div>

          <div className="bg-red-50 border border-red-100 p-4 rounded-md mb-8">
            <div className="flex items-center mb-2">
              <FiAlertCircle className="text-red-500 mr-2" />
              <p className="text-sm font-medium text-red-800">Invalid Reset Link</p>
            </div>
            <p className="text-xs text-red-700">
              The password reset link is invalid or has expired. Please request a new password reset link.
            </p>
          </div>

          <Link
            href="/forgot-password"
            className="block w-full border border-black py-3 text-xs tracking-widest text-center
              hover:bg-black hover:text-white transition-colors duration-200"
          >
            REQUEST NEW RESET LINK
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-4 py-20">
      <div className="max-w-[460px] mx-auto">
        <div className="mb-8">
          <h1 className="text-xs tracking-widest mb-6">RESET PASSWORD</h1>
          <p className="text-xs text-black/60 leading-relaxed">
            Please enter your new password below.
          </p>
        </div>

        {success ? (
          <div className="bg-green-50 border border-green-100 p-6 rounded-md">
            <div className="flex items-center mb-4">
              <FiCheckCircle className="text-green-500 text-xl mr-2" />
              <h3 className="text-sm font-medium text-green-800">Password Reset Successful</h3>
            </div>
            <p className="text-xs text-green-700 mb-4">
              Your password has been reset successfully. You will be redirected to the login page shortly.
            </p>
            <Link
              href="/login"
              className="text-xs font-medium text-blue-600 hover:text-blue-500"
            >
              Click here if you are not redirected automatically
            </Link>
          </div>
        ) : (
          <form className="space-y-8" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-100 p-4 rounded-md flex items-start">
                <FiAlertCircle className="text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <p className="text-xs text-red-700">{error}</p>
              </div>
            )}

            <div className="space-y-6">
              <div>
                <label htmlFor="password" className="text-xs">
                  NEW PASSWORD
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiLock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    minLength={8}
                    className="w-full bg-transparent border-0 border-b border-black/20 pl-10 pr-10 py-2
                      text-sm placeholder:text-black/40 focus:ring-0 focus:border-black"
                    placeholder="Enter new password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <FiEyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <FiEye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirm-password" className="text-xs">
                  CONFIRM PASSWORD
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiLock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="confirm-password"
                    name="confirm-password"
                    type={showConfirmPassword ? "text" : "password"}
                    required
                    className="w-full bg-transparent border-0 border-b border-black/20 pl-10 pr-10 py-2
                      text-sm placeholder:text-black/40 focus:ring-0 focus:border-black"
                    placeholder="Confirm new password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <FiEyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <FiEye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-black text-white py-3 text-xs tracking-widest
                  hover:bg-black/90 disabled:bg-black/70 disabled:cursor-not-allowed"
              >
                {isLoading ? 'RESETTING...' : 'RESET PASSWORD'}
              </button>

              <Link
                href="/login"
                className="block w-full border border-black py-3 text-xs tracking-widest text-center
                  hover:bg-black hover:text-white transition-colors duration-200"
              >
                BACK TO LOGIN
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

export default function ResetPassword() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}
