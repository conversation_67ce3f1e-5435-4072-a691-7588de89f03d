import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Order from '@/models/Order';
import crypto from 'crypto';

export async function POST(request) {
  try {
    // Get the raw request body for signature verification
    const rawBody = await request.text();
    const jsonBody = JSON.parse(rawBody);
    
    // Verify webhook signature
    const razorpaySignature = request.headers.get('x-razorpay-signature');
    if (!razorpaySignature) {
      console.error('Missing Razorpay signature');
      return NextResponse.json({ error: 'Invalid webhook request' }, { status: 400 });
    }

    // Verify the webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || 'your_webhook_secret')
      .update(rawBody)
      .digest('hex');

    if (expectedSignature !== razorpaySignature) {
      console.error('Invalid webhook signature');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Connect to database
    await connectToDB();

    // Handle different event types
    const event = jsonBody.event;
    
    if (event === 'payment.authorized' || event === 'payment.captured') {
      const paymentId = jsonBody.payload.payment.entity.id;
      const orderId = jsonBody.payload.payment.entity.notes?.orderId;

      if (orderId) {
        // Update order payment status
        const order = await Order.findById(orderId);
        if (order) {
          order.paymentStatus = 'paid';
          order.paymentId = paymentId;
          await order.save();
          console.log(`Order ${orderId} payment status updated to paid`);
        }
      }
    } else if (event === 'payment.failed') {
      const paymentId = jsonBody.payload.payment.entity.id;
      const orderId = jsonBody.payload.payment.entity.notes?.orderId;

      if (orderId) {
        // Update order payment status
        const order = await Order.findById(orderId);
        if (order) {
          order.paymentStatus = 'failed';
          order.paymentId = paymentId;
          await order.save();
          console.log(`Order ${orderId} payment status updated to failed`);
        }
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Razorpay webhook error:', error);
    return NextResponse.json(
      { error: error.message || 'Webhook processing failed' },
      { status: 500 }
    );
  }
}
