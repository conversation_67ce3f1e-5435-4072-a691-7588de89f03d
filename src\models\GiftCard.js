import mongoose from 'mongoose';
import crypto from 'crypto';

const giftCardSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    default: () => crypto.randomBytes(8).toString('hex').toUpperCase()
  },
  amount: {
    type: Number,
    required: true,
    min: 100, // Minimum gift card value (₹100)
  },
  balance: {
    type: Number,
    required: true,
    min: 0,
  },
  status: {
    type: String,
    enum: ['active', 'redeemed', 'expired', 'cancelled'],
    default: 'active',
  },
  recipientEmail: {
    type: String,
    required: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address'],
  },
  recipientName: {
    type: String,
    required: true,
  },
  senderName: {
    type: String,
    required: true,
  },
  senderEmail: {
    type: String,
    required: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address'],
  },
  message: {
    type: String,
    default: '',
  },
  purchasedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  redeemedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  expiresAt: {
    type: Date,
    required: true,
  },
  redeemedAt: {
    type: Date,
    default: null,
  },
  paymentId: {
    type: String,
    default: null,
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed'],
    default: 'pending',
  },
  orderIds: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'Order',
    default: [],
  }
});

// Generate a unique code before saving if one doesn't exist
giftCardSchema.pre('save', function(next) {
  if (!this.code) {
    this.code = crypto.randomBytes(8).toString('hex').toUpperCase();
  }
  
  // Set initial balance equal to amount
  if (this.isNew) {
    this.balance = this.amount;
  }
  
  next();
});

// Set default expiration date to 1 year from creation
giftCardSchema.pre('validate', function(next) {
  if (!this.expiresAt) {
    const oneYearFromNow = new Date();
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
    this.expiresAt = oneYearFromNow;
  }
  next();
});

// Method to check if gift card is valid
giftCardSchema.methods.isValid = function() {
  return this.status === 'active' && this.balance > 0 && new Date() < this.expiresAt;
};

// Method to redeem gift card
giftCardSchema.methods.redeem = function(userId, amount, orderId) {
  if (!this.isValid()) {
    throw new Error('Gift card is not valid');
  }
  
  if (amount > this.balance) {
    throw new Error('Insufficient balance on gift card');
  }
  
  this.balance -= amount;
  
  if (this.balance === 0) {
    this.status = 'redeemed';
    this.redeemedAt = new Date();
  }
  
  if (!this.redeemedBy) {
    this.redeemedBy = userId;
  }
  
  if (orderId) {
    this.orderIds.push(orderId);
  }
  
  return this.save();
};

const GiftCard = mongoose.models.GiftCard || mongoose.model('GiftCard', giftCardSchema);

export default GiftCard;
