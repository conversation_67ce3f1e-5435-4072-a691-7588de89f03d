'use client';

export default function ProductsLoading() {
  return (
    <div className="min-h-screen bg-white pt-0">
      {/* Header Skeleton */}
      <div className="sticky top-16 bg-white z-20 border-b border-black/5">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          {/* Main Header Skeleton */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4 gap-4">
            <div className="h-6 bg-gray-200 w-32 rounded animate-pulse" />

            {/* Search and Filter Controls Skeleton */}
            <div className="flex items-center gap-4 order-first sm:order-none">
              {/* Search Bar Skeleton */}
              <div className="flex-1 sm:w-[200px] relative">
                <div className="h-8 bg-gray-200 w-full rounded animate-pulse" />
              </div>

              {/* <PERSON>lter Button Skeleton */}
              <div className="h-8 bg-gray-200 w-24 rounded animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid Skeleton */}
      <div className="max-w-screen-xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-8">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-x-2 gap-y-4 sm:gap-x-6 sm:gap-y-16">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((n) => (
            <div key={n} className="animate-pulse">
              {/* Product Image Skeleton */}
              <div className="bg-gray-200 aspect-square mb-2 sm:mb-4 rounded" />

              {/* Product Info Skeleton */}
              <div className="space-y-2 px-0.5">
                <div className="h-4 bg-gray-200 w-3/4 rounded" />
                <div className="h-3 bg-gray-200 w-1/2 rounded" />
                <div className="h-4 bg-gray-200 w-1/4 rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
