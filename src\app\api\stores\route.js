import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import Store from '@/models/Store';

// Get all active stores (public access)
export async function GET() {
  try {
    // Connect to database
    await connectToDB();

    // Fetch only active stores
    const stores = await Store.find({ status: 'active' }).sort({ city: 1, name: 1 });
    
    return NextResponse.json(stores);
  } catch (error) {
    console.error('Error fetching stores:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stores' },
      { status: 500 }
    );
  }
}
