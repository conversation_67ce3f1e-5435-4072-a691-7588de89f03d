'use client';

import { useState } from 'react';
import Link from 'next/link';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import NewsletterSignup from './NewsletterSignup';

const Footer = () => {
  const [expandedSection, setExpandedSection] = useState(null);
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'HELP & SUPPORT',
      links: [
        { name: 'SHIPPING', href: '/shipping' },
        { name: 'RETURNS', href: '/returns' },
        { name: 'GIFT CARD', href: '/gift-cards' },
        { name: 'CONTACT US', href: '/contact' },
      ]
    },
    {
      title: 'FOLLOW US',
      links: [
        { name: 'INSTAGRAM', href: 'https://instagram.com/furniturebazaarstore' },
        { name: 'FACEBOOK', href: 'https://facebook.com' },
        { name: 'PINTEREST', href: 'https://pinterest.com' },
        { name: 'TWITTER', href: 'https://twitter.com' },
      ]
    },
    {
      title: 'COMPANY',
      links: [
        { name: 'ABOUT US', href: '/about' },
        { name: 'SUSTAINABILITY', href: '/sustainability' },
        { name: 'STORES', href: '/stores' },
        { name: 'WORK WITH US', href: '/careers' },
      ]
    },
    {
      title: 'POLICIES',
      links: [
        { name: 'PRIVACY POLICY', href: '/privacy' },
        { name: 'TERMS OF USE', href: '/terms' },
        { name: 'COOKIES SETTINGS', href: '/cookies' },
      ]
    }
  ];

  const toggleSection = (index) => {
    setExpandedSection(expandedSection === index ? null : index);
  };

  return (
    <footer className="bg-white text-[10px] tracking-[0.15em] mt-20">
      {/* Zara-style Footer */}
      <div className="border-t border-black/10">
        {/* Newsletter Section - Zara Style */}
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Left Side - Newsletter */}
            <div>
              <div className="max-w-md">
                <h3 className="text-xs mb-6">JOIN OUR NEWSLETTER</h3>
                <NewsletterSignup />
              </div>
            </div>

            {/* Right Side - Links */}
            <div className="grid grid-cols-2 gap-8 md:gap-16">
              {/* Mobile Footer Accordion */}
              <div className="col-span-2 lg:hidden">
                {footerSections.map((section, index) => (
                  <div key={section.title} className="border-b border-black/5 last:border-b-0">
                    <button
                      onClick={() => toggleSection(index)}
                      className="w-full py-4 flex justify-between items-center text-[10px] uppercase"
                    >
                      {section.title}
                      {expandedSection === index ? (
                        <FiChevronUp className="h-3 w-3" />
                      ) : (
                        <FiChevronDown className="h-3 w-3" />
                      )}
                    </button>
                    {expandedSection === index && (
                      <div className="pb-4">
                        <ul className="space-y-3">
                          {section.links.map((link) => (
                            <li key={link.name}>
                              <Link
                                href={link.href}
                                className="text-black/60 hover:text-black transition-colors"
                              >
                                {link.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Desktop Footer Links */}
              <div className="hidden lg:block">
                {footerSections.slice(0, 2).map((section) => (
                  <div key={section.title} className="mb-10">
                    <h4 className="mb-4 text-[10px] font-normal">{section.title}</h4>
                    <ul className="space-y-2">
                      {section.links.map((link) => (
                        <li key={link.name}>
                          <Link
                            href={link.href}
                            className="text-black/60 hover:text-black transition-colors text-[9px]"
                          >
                            {link.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>

              <div className="hidden lg:block">
                {footerSections.slice(2, 4).map((section) => (
                  <div key={section.title} className="mb-10">
                    <h4 className="mb-4 text-[10px] font-normal">{section.title}</h4>
                    <ul className="space-y-2">
                      {section.links.map((link) => (
                        <li key={link.name}>
                          <Link
                            href={link.href}
                            className="text-black/60 hover:text-black transition-colors text-[9px]"
                          >
                            {link.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar - Zara Style */}
        <div className="border-t border-black/5">
          <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
            <div className="py-6 flex flex-col md:flex-row justify-between items-center gap-4 text-[9px]">
              <div className="flex items-center gap-6">
                <button className="text-black/60 hover:text-black">
                  INDIA
                </button>
                <span className="text-black/60">|</span>
                <button className="text-black/60 hover:text-black">
                  ENGLISH
                </button>
              </div>
              <div className="text-black/60 text-center md:text-right">
                <p>© {currentYear} FURNITUREBAZAAR</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
