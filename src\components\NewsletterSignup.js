'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';

export default function NewsletterSignup({ className = '', style = {} }) {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const disposableDomains = [
      'tempmail.com',
      'throwawaymail.com',
      'temp-mail.org',
      'fakeinbox.com',
      'guerrillamail.com'
    ];

    if (!email) return 'Email is required';
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    if (email.length > 254) return 'Email address is too long';

    const domain = email.split('@')[1].toLowerCase();
    if (disposableDomains.includes(domain)) {
      return 'Please use a valid non-disposable email address';
    }

    return '';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationError = validateEmail(email);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: email.toLowerCase().trim() }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe');
      }

      toast.success(data.message || 'Successfully subscribed to newsletter!');
      setEmail('');
    } catch (error) {
      toast.error(error.message);
      setError(error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`w-full ${className}`} style={style}>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="relative">
          <input
            type="email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              setError('');
            }}
            placeholder="ENTER YOUR EMAIL"
            className="w-full bg-transparent border-0 border-b border-black/10 px-0 py-2
              text-[10px] placeholder:text-black/40 focus:ring-0 focus:border-black"
            required
          />
        </div>

        {error && (
          <p className="text-[9px] text-red-500">{error}</p>
        )}

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-black text-white py-2.5 text-[10px] tracking-widest
            hover:bg-black/90 disabled:bg-black/70 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
        </button>

        <p className="text-[9px] text-black/60 leading-relaxed">
          By subscribing to our newsletter you agree to receive personalized marketing communications from FurnitureBazaar.
          For more information, please consult our{' '}
          <a href="/privacy" className="underline hover:text-black">
            Privacy Policy
          </a>.
        </p>
      </form>
    </div>
  );
}
