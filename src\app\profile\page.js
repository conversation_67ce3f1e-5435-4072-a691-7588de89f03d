'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useCart } from '@/context/CartContext';
import { toast } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiArrowRight,
  FiEdit2,
  FiLogOut,
  FiChevronRight,
  FiX,
  FiEye,
  FiEyeOff,
  FiAlertCircle,
  FiCheck,
  FiTrash2,
  FiBell,
  FiShield,
  FiGift
} from 'react-icons/fi';
import Link from 'next/link';
import Image from 'next/image';



// Privacy Settings Section Component
function PrivacySettingsSection() {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [privacySettings, setPrivacySettings] = useState({
    shareDataWithPartners: false,
    allowProfileVisibility: true,
    enableLocationTracking: false
  });

  const handleToggle = (setting) => {
    setPrivacySettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/privacy', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ privacySettings })
      });

      if (response.ok) {
        toast.success('Privacy settings updated');
        setIsOpen(false);
      } else {
        toast.error('Failed to update privacy settings');
      }
    } catch (error) {
      toast.error('An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="border-b border-black/5 pb-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full text-left group"
      >
        <div className="flex items-center">
          <FiShield className="mr-3 text-black/70" />
          <span className="text-[10px] md:text-xs tracking-[0.2em]">PRIVACY SETTINGS</span>
        </div>
        {isOpen ? (
          <FiX className="transform transition-transform" />
        ) : (
          <FiChevronRight className="transform group-hover:translate-x-1 transition-transform" />
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-6 space-y-4">
              {Object.entries(privacySettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between py-2">
                  <span className="text-xs">
                    {key === 'shareDataWithPartners' && 'Share my data with trusted partners'}
                    {key === 'allowProfileVisibility' && 'Allow others to see my profile'}
                    {key === 'enableLocationTracking' && 'Enable location tracking for personalized offers'}
                  </span>
                  <button
                    onClick={() => handleToggle(key)}
                    className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 ${value ? 'bg-black' : 'bg-gray-300'}`}
                  >
                    <span
                      className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${value ? 'translate-x-6' : 'translate-x-1'}`}
                    />
                  </button>
                </div>
              ))}

              <div className="pt-4">
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="w-full py-3 bg-black text-white text-[10px] tracking-[0.2em] hover:bg-black/80 transition-colors disabled:bg-black/50"
                >
                  {loading ? 'SAVING...' : 'SAVE PREFERENCES'}
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Notification Preferences Section Component
function NotificationPreferencesSection() {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState({
    orderUpdates: true,
    promotions: true,
    recommendations: true
  });

  useEffect(() => {
    if (isOpen) {
      fetchNotificationSettings();
    }
  }, [isOpen]);

  const fetchNotificationSettings = async () => {
    setLoadingData(true);
    try {
      const response = await fetch('/api/user/notifications');
      if (response.ok) {
        const data = await response.json();
        setNotificationSettings(data.notifications);
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const handleToggle = (setting) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/notifications', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notifications: notificationSettings })
      });

      if (response.ok) {
        toast.success('Notification preferences updated');
        setIsOpen(false);
      } else {
        toast.error('Failed to update notification preferences');
      }
    } catch (error) {
      toast.error('An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="border-b border-black/5 pb-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full text-left group"
      >
        <div className="flex items-center">
          <FiBell className="mr-3 text-black/70" />
          <span className="text-[10px] md:text-xs tracking-[0.2em]">NOTIFICATION PREFERENCES</span>
        </div>
        {isOpen ? (
          <FiX className="transform transition-transform" />
        ) : (
          <FiChevronRight className="transform group-hover:translate-x-1 transition-transform" />
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {loadingData ? (
              <div className="mt-6 flex justify-center">
                <div className="animate-pulse text-center">
                  <p className="text-xs text-black/50">Loading preferences...</p>
                </div>
              </div>
            ) : (
              <div className="mt-6 space-y-4">
                {Object.entries(notificationSettings).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-2">
                    <span className="text-xs">
                      {key === 'orderUpdates' && 'Order status updates'}
                      {key === 'promotions' && 'Promotions and sales'}
                      {key === 'recommendations' && 'Product recommendations'}
                    </span>
                    <button
                      onClick={() => handleToggle(key)}
                      className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 ${value ? 'bg-black' : 'bg-gray-300'}`}
                    >
                      <span
                        className={`w-5 h-5 rounded-full bg-white shadow-md transform transition-transform duration-300 ${value ? 'translate-x-6' : 'translate-x-1'}`}
                      />
                    </button>
                  </div>
                ))}

                <div className="pt-4">
                  <button
                    onClick={handleSave}
                    disabled={loading}
                    className="w-full py-3 bg-black text-white text-[10px] tracking-[0.2em] hover:bg-black/80 transition-colors disabled:bg-black/50"
                  >
                    {loading ? 'SAVING...' : 'SAVE PREFERENCES'}
                  </button>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Delete Account Section Component
function DeleteAccountSection() {
  const [isOpen, setIsOpen] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleDeleteRequest = () => {
    setShowConfirmation(true);
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    setPassword('');
    setError('');
  };

  const handleDeleteAccount = async () => {
    if (!password) {
      setError('Password is required to delete your account');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/user/delete-account', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Your account has been deleted');
        router.push('/login');
      } else {
        setError(data.error || 'Failed to delete account');
      }
    } catch (error) {
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="border-b border-black/5 pb-4">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex justify-between items-center w-full text-left group"
      >
        <div className="flex items-center">
          <FiTrash2 className="mr-3 text-red-500" />
          <span className="text-[10px] md:text-xs tracking-[0.2em] text-red-500">DELETE ACCOUNT</span>
        </div>
        {isOpen ? (
          <FiX className="transform transition-transform" />
        ) : (
          <FiChevronRight className="transform group-hover:translate-x-1 transition-transform" />
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="mt-6 space-y-4">
              {!showConfirmation ? (
                <>
                  <div className="bg-red-50 border border-red-200 p-4 rounded-sm">
                    <h3 className="text-sm font-medium text-red-800 mb-2">Warning: This action cannot be undone</h3>
                    <p className="text-xs text-red-700">
                      Deleting your account will permanently remove all your data, including order history, saved addresses, and preferences.
                    </p>
                  </div>
                  <button
                    onClick={handleDeleteRequest}
                    className="w-full py-3 bg-red-500 text-white text-[10px] tracking-[0.2em] hover:bg-red-600 transition-colors"
                  >
                    DELETE MY ACCOUNT
                  </button>
                </>
              ) : (
                <>
                  <div className="bg-red-50 border border-red-200 p-4 rounded-sm">
                    <h3 className="text-sm font-medium text-red-800 mb-2">Confirm account deletion</h3>
                    <p className="text-xs text-red-700 mb-4">
                      Please enter your password to confirm that you want to delete your account.
                    </p>

                    {error && (
                      <div className="mb-4 bg-red-100 border border-red-300 p-2 rounded-sm">
                        <p className="text-xs text-red-700">{error}</p>
                      </div>
                    )}

                    <div className="mb-4">
                      <label className="block text-[10px] tracking-[0.2em] text-red-700 mb-1">
                        PASSWORD
                      </label>
                      <input
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full bg-white border border-red-300 rounded-sm py-2 px-3 text-sm focus:outline-none focus:border-red-500 transition-colors"
                      />
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <button
                      onClick={handleCancel}
                      className="flex-1 py-3 bg-gray-200 text-gray-800 text-[10px] tracking-[0.2em] hover:bg-gray-300 transition-colors"
                    >
                      CANCEL
                    </button>
                    <button
                      onClick={handleDeleteAccount}
                      disabled={loading}
                      className="flex-1 py-3 bg-red-500 text-white text-[10px] tracking-[0.2em] hover:bg-red-600 transition-colors disabled:bg-red-300"
                    >
                      {loading ? 'PROCESSING...' : 'CONFIRM DELETE'}
                    </button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, loading: authLoading, logout } = useAuth();
  const { wishlist } = useCart();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [giftCards, setGiftCards] = useState([]);
  const [loadingGiftCards, setLoadingGiftCards] = useState(true);
  const [activeSection, setActiveSection] = useState('profile');
  const [userInfo, setUserInfo] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    avatar: '',
  });

  useEffect(() => {
    // Only redirect if authentication check is complete and user is not logged in
    if (!authLoading && !user) {
      router.push('/login');
      return;
    }

    // Only proceed with initialization if user is available
    if (user) {
      setUserInfo({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        avatar: user.avatar || '',
      });

      fetchRecentOrders();
      fetchGiftCards();
    }
  }, [user, authLoading, router]);

  const fetchRecentOrders = async () => {
    try {
      const response = await fetch('/api/orders/user');
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGiftCards = async () => {
    try {
      setLoadingGiftCards(true);
      const response = await fetch('/api/gift-cards');
      if (response.ok) {
        const data = await response.json();
        setGiftCards(data);
      }
    } catch (error) {
      console.error('Error fetching gift cards:', error);
    } finally {
      setLoadingGiftCards(false);
    }
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userInfo),
      });

      if (response.ok) {
        toast.success('Profile updated');
        setIsEditing(false);
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUserInfo(prev => ({ ...prev, [name]: value }));
  };

  const getStatusStyle = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-50 text-yellow-700';
      case 'processing':
        return 'bg-blue-50 text-blue-700';
      case 'shipped':
        return 'bg-indigo-50 text-indigo-700';
      case 'delivered':
        return 'bg-green-50 text-green-700';
      case 'cancelled':
        return 'bg-red-50 text-red-700';
      default:
        return 'bg-gray-50 text-gray-700';
    }
  };

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-sm tracking-[0.2em]">LOADING...</div>
      </div>
    );
  }

  // Don't render anything if user is not authenticated
  if (!user) return null;

  const sections = [
    { id: 'profile', label: 'PERSONAL DETAILS' },
    { id: 'orders', label: 'MY ORDERS' },
    { id: 'wishlist', label: 'SAVED ITEMS' },
    { id: 'gift-cards', label: 'GIFT CARDS' },
    { id: 'settings', label: 'ACCOUNT SETTINGS' },
  ];

  return (
    <div className="min-h-screen bg-white pt-16">
      {/* Navigation - Zara Style */}
      <div className="fixed top-16 left-0 w-full bg-white border-b border-black/5 z-30">
        <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex items-center justify-between h-12 md:h-14">
            <nav className="flex overflow-x-auto hide-scrollbar space-x-6 md:space-x-8">
              {sections.map(section => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`text-[10px] md:text-xs tracking-[0.2em] whitespace-nowrap hover:opacity-50 transition-opacity ${
                    activeSection === section.id ? 'font-medium' : 'font-light'
                  }`}
                >
                  {section.label}
                </button>
              ))}
            </nav>
            <div className="flex items-center space-x-4 md:space-x-6">
              {user?.role === 'admin' && (
                <Link
                  href="/admin"
                  className="text-[10px] md:text-xs tracking-[0.2em] bg-black text-white px-3 py-1.5 hover:bg-black/80 transition-colors"
                >
                  ADMIN
                </Link>
              )}
              <button
                onClick={async () => {
                  await logout();
                  // No need to push to login here as the logout function already handles it
                }}
                className="text-[10px] md:text-xs tracking-[0.2em] bg-red-500 hover:bg-red-600 text-white px-3 py-1.5 transition-colors flex items-center"
              >
                SIGN OUT
                <FiLogOut className="ml-1.5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Zara Style */}
      <div className="max-w-screen-xl mx-auto px-4 md:px-6 lg:px-8 py-8 md:py-12 lg:py-16 mt-4">
        <AnimatePresence mode="wait">
          {activeSection === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="max-w-2xl"
            >
              <div className="flex justify-between items-center mb-8 md:mb-12">
                <h1 className="text-xl md:text-2xl font-light tracking-wide">MY ACCOUNT</h1>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
                >
                  {isEditing ? 'CANCEL' : 'EDIT'}
                  {isEditing ? <FiX className="ml-1.5" /> : <FiEdit2 className="ml-1.5" />}
                </button>
              </div>

              <form onSubmit={handleUpdateProfile} className="space-y-8 md:space-y-12">
                <div className="space-y-6 md:space-y-8">
                  {[
                    { name: 'name', label: 'FULL NAME', type: 'text' },
                    { name: 'email', label: 'EMAIL', type: 'email' },
                    { name: 'phone', label: 'PHONE', type: 'tel' },
                    { name: 'address', label: 'ADDRESS', type: 'textarea' },
                  ].map((field) => (
                    <div key={field.name} className="border-b border-black/5 pb-2">
                      <label className="block text-[10px] md:text-xs tracking-[0.2em] mb-2 text-black/70">
                        {field.label}
                      </label>
                      {field.type === 'textarea' ? (
                        <textarea
                          name={field.name}
                          value={userInfo[field.name]}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          rows={3}
                          className="w-full bg-transparent border-0 focus:ring-0 p-0 disabled:text-black text-sm md:text-base font-light"
                        />
                      ) : (
                        <input
                          type={field.type}
                          name={field.name}
                          value={userInfo[field.name]}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          className="w-full bg-transparent border-0 focus:ring-0 p-0 disabled:text-black text-sm md:text-base font-light"
                        />
                      )}
                    </div>
                  ))}
                </div>

                <AnimatePresence>
                  {isEditing && (
                    <motion.button
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      type="submit"
                      className="w-full py-3 bg-black text-white text-[10px] md:text-xs tracking-[0.2em] hover:bg-black/80 transition-colors"
                    >
                      SAVE CHANGES
                    </motion.button>
                  )}
                </AnimatePresence>
              </form>
            </motion.div>
          )}

        {activeSection === 'orders' && (
          <motion.div
            key="orders"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center mb-8 md:mb-12">
              <h1 className="text-xl md:text-2xl font-light tracking-wide">ORDER HISTORY</h1>
              <Link
                href="/orders"
                className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
              >
                VIEW ALL
                <FiArrowRight className="ml-1.5" />
              </Link>
            </div>

            {loading ? (
              <div className="space-y-4 md:space-y-6">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-24 md:h-28 bg-gray-50 animate-pulse" />
                ))}
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12 md:py-16">
                <p className="text-sm md:text-base font-light mb-6 md:mb-8">You haven't placed any orders yet</p>
                <Link
                  href="/products"
                  className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
                >
                  START SHOPPING
                  <FiArrowRight className="ml-1.5" />
                </Link>
              </div>
            ) : (
              <div className="space-y-4 md:space-y-6">
                {orders.map(order => (
                  <Link
                    key={order._id}
                    href={`/orders/${order._id}`}
                    className="block border-b border-black/5 pb-4 md:pb-6 group"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex-grow">
                        <div className="flex items-center justify-between mb-1 md:mb-2">
                          <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70">
                            ORDER #{order._id.slice(-6)}
                          </p>
                          <div className={`text-[10px] px-2 py-0.5 rounded-sm ${getStatusStyle(order.status)}`}>
                            {order.status.toUpperCase()}
                          </div>
                        </div>
                        <p className="text-sm md:text-base font-light">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <FiChevronRight className="ml-4 transform group-hover:translate-x-1 transition-transform" />
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </motion.div>
        )}

        {activeSection === 'wishlist' && (
          <motion.div
            key="wishlist"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center mb-8 md:mb-12">
              <h1 className="text-xl md:text-2xl font-light tracking-wide">SAVED ITEMS</h1>
              <Link
                href="/wishlist"
                className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
              >
                VIEW ALL
                <FiArrowRight className="ml-1.5" />
              </Link>
            </div>

            {wishlist && wishlist.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 md:gap-4">
                {wishlist.slice(0, 4).map(item => (
                  <Link
                    key={item._id}
                    href={`/products/${item._id}`}
                    className="group"
                  >
                    <div className="relative aspect-square mb-2 overflow-hidden border border-black/5">
                      {item.images && item.images[0] && (
                        <Image
                          src={item.images[0]}
                          alt={item.name}
                          fill
                          className="object-cover transition-transform duration-700 group-hover:scale-105"
                        />
                      )}
                    </div>
                    <p className="text-xs font-light truncate">{item.name}</p>
                    <p className="text-xs text-black/70">₹{item.price?.toLocaleString('en-IN')}</p>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-sm md:text-base font-light mb-6">Your wishlist is empty</p>
                <Link
                  href="/products"
                  className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
                >
                  DISCOVER PRODUCTS
                  <FiArrowRight className="ml-1.5" />
                </Link>
              </div>
            )}
          </motion.div>
        )}

        {activeSection === 'gift-cards' && (
          <motion.div
            key="gift-cards"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center mb-8 md:mb-12">
              <h1 className="text-xl md:text-2xl font-light tracking-wide">MY GIFT CARDS</h1>
              <Link
                href="/gift-cards"
                className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
              >
                BUY GIFT CARD
                <FiGift className="ml-1.5" />
              </Link>
            </div>

            {loadingGiftCards ? (
              <div className="space-y-4 md:space-y-6">
                {[1, 2].map(i => (
                  <div key={i} className="h-24 md:h-28 bg-gray-50 animate-pulse" />
                ))}
              </div>
            ) : giftCards.length === 0 ? (
              <div className="text-center py-12 md:py-16">
                <p className="text-sm md:text-base font-light mb-6 md:mb-8">You don't have any gift cards</p>
                <Link
                  href="/gift-cards"
                  className="inline-flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
                >
                  BUY GIFT CARD
                  <FiGift className="ml-1.5" />
                </Link>
              </div>
            ) : (
              <div className="space-y-4 md:space-y-6">
                {giftCards.map(card => (
                  <div
                    key={card._id}
                    className="border border-black/10 p-4 md:p-6"
                  >
                    <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                      <div>
                        <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70 mb-1">
                          GIFT CARD CODE
                        </p>
                        <p className="text-sm md:text-base font-medium tracking-wider">
                          {card.code}
                        </p>
                      </div>
                      <div className="mt-2 md:mt-0">
                        <span className={`text-[10px] px-2 py-0.5 rounded-sm ${
                          card.status === 'active'
                            ? 'bg-green-50 text-green-700'
                            : card.status === 'redeemed'
                              ? 'bg-blue-50 text-blue-700'
                              : 'bg-gray-50 text-gray-700'
                        }`}>
                          {card.status.toUpperCase()}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70 mb-1">
                          ORIGINAL AMOUNT
                        </p>
                        <p className="text-sm md:text-base">
                          ₹{card.amount.toLocaleString('en-IN')}
                        </p>
                      </div>
                      <div>
                        <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70 mb-1">
                          CURRENT BALANCE
                        </p>
                        <p className="text-sm md:text-base">
                          ₹{card.balance.toLocaleString('en-IN')}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70 mb-1">
                          RECIPIENT
                        </p>
                        <p className="text-sm md:text-base">
                          {card.recipientName}
                        </p>
                      </div>
                      <div>
                        <p className="text-[10px] md:text-xs tracking-[0.2em] text-black/70 mb-1">
                          EXPIRES
                        </p>
                        <p className="text-sm md:text-base">
                          {new Date(card.expiresAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}

        {activeSection === 'settings' && (
          <motion.div
            key="settings"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <h1 className="text-xl md:text-2xl font-light tracking-wide mb-8 md:mb-12">ACCOUNT SETTINGS</h1>
            <div className="space-y-4 md:space-y-6">
              <PrivacySettingsSection />
              <NotificationPreferencesSection />
              <DeleteAccountSection />
            </div>
          </motion.div>
        )}
        </AnimatePresence>
      </div>
    </div>
  );
}




