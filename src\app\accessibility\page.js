'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { FiMonitor, FiEye, FiNavigation2, FiMessageSquare } from 'react-icons/fi';

export default function AccessibilityPage() {
  // Accessibility features cards
  const accessibilityFeatures = [
    {
      icon: FiMonitor,
      title: 'Website Design',
      description: 'Our website is designed with accessibility in mind, using clear navigation, readable fonts, and proper color contrast.',
      color: 'bg-blue-50 text-blue-600',
    },
    {
      icon: FiEye,
      title: 'Visual Accessibility',
      description: 'We use high contrast text, resizable fonts, and alt text for images to support users with visual impairments.',
      color: 'bg-emerald-50 text-emerald-600',
    },
    {
      icon: FiNavigation2,
      title: 'Keyboard Navigation',
      description: 'Our website can be navigated using keyboard controls for users who cannot use a mouse or touchscreen.',
      color: 'bg-amber-50 text-amber-600',
    },
    {
      icon: FiMessageSquare,
      title: 'Assistance Available',
      description: 'Our customer service team is trained to assist customers with disabilities in navigating our website and making purchases.',
      color: 'bg-purple-50 text-purple-600',
    },
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-b from-amber-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Accessibility Statement
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-600 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Our commitment to making FurnitureBazaar accessible to everyone
            </motion.p>
          </div>
        </div>
      </section>

      {/* Accessibility Features Cards */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {accessibilityFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100"
                >
                  <div className="p-6">
                    <div className={`${feature.color} w-14 h-14 rounded-full flex items-center justify-center mb-6`}>
                      <feature.icon className="h-7 w-7" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                <p className="lead text-xl text-gray-600 mb-8">
                  At FurnitureBazaar, we are committed to ensuring that our website is accessible to all users, including those with disabilities. We strive to meet the Web Content Accessibility Guidelines (WCAG) 2.1 Level AA standards.
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Our Accessibility Commitment</h2>
                <p>We believe that everyone should have equal access to our products and services. We are continuously working to improve the accessibility and usability of our website for all users, including those with disabilities.</p>
                <p>Our accessibility efforts include:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Designing our website to be compatible with screen readers and other assistive technologies</li>
                  <li>Providing keyboard navigation options throughout our website</li>
                  <li>Using sufficient color contrast for text and important graphics</li>
                  <li>Providing alternative text for images</li>
                  <li>Using clear and consistent navigation</li>
                  <li>Providing visible focus indicators for keyboard users</li>
                  <li>Ensuring forms are properly labeled and accessible</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Accessibility Features</h2>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">1. Navigation</h3>
                <p>Our website is designed with a consistent and predictable navigation structure. The main navigation menu is available at the top of each page, and a breadcrumb trail is provided on product pages to help users understand their location within the website.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">2. Keyboard Accessibility</h3>
                <p>All interactive elements on our website can be accessed using keyboard navigation. Users can navigate through links, buttons, and form controls using the Tab key, and can activate elements using the Enter or Space key.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">3. Screen Reader Compatibility</h3>
                <p>Our website is designed to be compatible with screen readers such as JAWS, NVDA, and VoiceOver. We use proper heading structure, descriptive link text, and ARIA landmarks to improve the experience for screen reader users.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">4. Text Resizing</h3>
                <p>Users can resize text on our website using their browser's zoom function without loss of content or functionality. Our website is designed to be responsive and adapt to different screen sizes and zoom levels.</p>
                
                <h3 className="text-xl font-semibold text-gray-900 mt-8 mb-3">5. Color and Contrast</h3>
                <p>We strive to maintain sufficient color contrast between text and background colors to ensure readability for users with low vision or color blindness. We also avoid using color as the only means of conveying information.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Accessibility in Our Physical Stores</h2>
                <p>Our commitment to accessibility extends beyond our website to our physical showrooms. Our stores feature:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Wheelchair-accessible entrances and pathways</li>
                  <li>Accessible restrooms</li>
                  <li>Designated accessible parking spaces</li>
                  <li>Staff trained to assist customers with disabilities</li>
                  <li>Service animals are welcome</li>
                </ul>
                <p>If you require specific accommodations when visiting our showroom, please contact us in advance so we can better assist you.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Known Limitations</h2>
                <p>While we strive to ensure that our website is accessible to all users, we recognize that there may be some limitations or areas for improvement. We are actively working to address these issues:</p>
                <ul className="list-disc pl-6 mb-6">
                  <li>Some older PDF documents may not be fully accessible to screen readers</li>
                  <li>Some third-party content or applications on our website may not be fully accessible</li>
                  <li>Some interactive elements may require further optimization for keyboard-only users</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Feedback and Assistance</h2>
                <p>We welcome your feedback on the accessibility of our website. If you encounter any barriers or have suggestions for improvement, please contact us. We are committed to addressing accessibility issues and continuously improving the user experience for all visitors.</p>
                <p>If you need assistance with any part of our website, please contact our customer service team:</p>
                <div className="bg-gray-50 p-6 rounded-lg mt-4 mb-8">
                  <p className="mb-2"><strong>FurnitureBazaar Customer Service</strong></p>
                  <p className="mb-2">Email: <EMAIL></p>
                  <p>Phone: +91 80 1234 5678</p>
                </div>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Compliance Status</h2>
                <p>We are committed to ensuring our website complies with level AA of the World Wide Web Consortium (W3C) Web Content Accessibility Guidelines 2.1. These guidelines explain how to make web content more accessible for people with disabilities.</p>
                <p>We conduct regular accessibility audits and testing to identify and address any issues. Our development team follows accessibility best practices when building and updating our website.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Third-Party Applications</h2>
                <p>Our website may include third-party applications or content that is not within our control. We cannot guarantee the accessibility of these third-party features, but we make every effort to select partners and tools that share our commitment to accessibility.</p>

                <h2 className="text-2xl font-bold text-gray-900 mt-12 mb-4">Continuous Improvement</h2>
                <p>We are committed to ongoing improvement of our website's accessibility and usability. We regularly review our website and content to identify and address accessibility issues, and we incorporate accessibility considerations into our design and development processes.</p>
                <p>This accessibility statement was last updated on March 16, 2024.</p>
              </motion.div>
            </div>

            <div className="mt-12 text-center">
              <Link 
                href="/" 
                className="inline-flex items-center px-6 py-3 border border-amber-600 text-amber-600 hover:bg-amber-50 font-medium rounded-md transition duration-300"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 