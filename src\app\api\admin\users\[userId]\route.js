import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import User from '@/models/User';
import Newsletter from '@/models/Newsletter';

export async function GET(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;

    // Fetch user
    const user = await User.findById(userId).select('-password');

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is subscribed to newsletter
    const newsletterSubscription = await Newsletter.findOne({
      email: user.email,
      status: 'active'
    });

    // Convert to plain object and add newsletter status
    const userObj = user.toObject();
    userObj.isSubscribedToNewsletter = !!newsletterSubscription;

    return NextResponse.json(userObj);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request, { params }) {
  try {
    // Check if user is authenticated and is an admin
    const currentUser = await getSessionUser();
    if (!currentUser || currentUser.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Await params before accessing properties
    const resolvedParams = await params;
    const { userId } = resolvedParams;
    const updates = await request.json();

    // Prevent changing own role/status
    if (userId === currentUser._id) {
      return NextResponse.json(
        { error: 'Cannot modify own account' },
        { status: 400 }
      );
    }

    // Validate role if it's being updated
    if (updates.role && !['user', 'admin'].includes(updates.role)) {
      return NextResponse.json(
        { error: 'Invalid role' },
        { status: 400 }
      );
    }

    // Validate status if it's being updated
    if (updates.status && !['active', 'inactive'].includes(updates.status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update user
    const user = await User.findByIdAndUpdate(
      userId,
      { $set: updates },
      { new: true }
    ).select('-password');

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}