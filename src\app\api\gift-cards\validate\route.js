import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import GiftCard from '@/models/GiftCard';
import { getSessionUser } from '@/lib/auth';

// Validate a gift card code
export async function POST(request) {
  try {
    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Parse request body
    const body = await request.json();
    const { code } = body;

    if (!code) {
      return NextResponse.json(
        { error: 'Gift card code is required' },
        { status: 400 }
      );
    }

    // Find gift card
    const giftCard = await GiftCard.findOne({ code: code.toUpperCase() });

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Invalid gift card code' },
        { status: 404 }
      );
    }

    // Check if gift card is valid
    if (giftCard.status !== 'active') {
      return NextResponse.json(
        { error: `Gift card is ${giftCard.status}` },
        { status: 400 }
      );
    }

    if (giftCard.balance <= 0) {
      return NextResponse.json(
        { error: 'Gift card has no remaining balance' },
        { status: 400 }
      );
    }

    if (new Date() > giftCard.expiresAt) {
      // Update status to expired
      giftCard.status = 'expired';
      await giftCard.save();
      
      return NextResponse.json(
        { error: 'Gift card has expired' },
        { status: 400 }
      );
    }

    // Return gift card details
    return NextResponse.json({
      code: giftCard.code,
      balance: giftCard.balance,
      expiresAt: giftCard.expiresAt
    });
  } catch (error) {
    console.error('Error validating gift card:', error);
    return NextResponse.json(
      { error: 'Failed to validate gift card' },
      { status: 500 }
    );
  }
}
