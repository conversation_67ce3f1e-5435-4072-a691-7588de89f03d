import { NextResponse } from 'next/server';
import Contact from '@/models/Contact';
import { connectToDB } from '@/lib/mongodb';

export async function POST(request) {
  try {
    await connectToDB();
    const data = await request.json();
    
    // Create a new contact submission
    const contact = await Contact.create(data);

    return NextResponse.json({ 
      message: 'Message sent successfully',
      contact: contact
    });
  } catch (error) {
    console.error('Contact submission error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to send message' },
      { status: 500 }
    );
  }
}