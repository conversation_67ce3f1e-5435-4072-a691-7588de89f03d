import { format } from 'date-fns';
import Order from '@/models/Order';

// Function to generate a unique order number
export const generateOrderNumber = async () => {
  const today = new Date();
  const datePrefix = format(today, 'yyyyMMdd');
  
  // Find the highest counter for today in existing order numbers
  const regex = new RegExp(`^FB-${datePrefix}-\\d{4}$`);
  const existingOrder = await Order.findOne({ orderNumber: regex }).sort({ orderNumber: -1 });
  
  let counter = 1;
  if (existingOrder && existingOrder.orderNumber) {
    const counterStr = existingOrder.orderNumber.split('-')[2];
    counter = parseInt(counterStr, 10) + 1;
  }
  
  // Generate the order number
  const paddedCounter = String(counter).padStart(4, '0');
  return `FB-${datePrefix}-${paddedCounter}`;
};
