import { NextResponse } from 'next/server';
import { connectToDB } from '@/lib/mongodb';
import { getSessionUser } from '@/lib/auth';
import Order from '@/models/Order';
import User from '@/models/User';
import Product from '@/models/Product';
import Newsletter from '@/models/Newsletter';
import GiftCard from '@/models/GiftCard';
import Coupon from '@/models/Coupon';
import Store from '@/models/Store';

export async function GET() {
  try {
    // Check if user is authenticated and is an admin
    const user = await getSessionUser();
    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();

    // Get current date and date 30 days ago
    const now = new Date();
    const thirtyDaysAgo = new Date(now.setDate(now.getDate() - 30));

    // Get current month stats
    const currentMonthStats = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: null,
          revenue: { $sum: '$total' },
          orderCount: { $sum: 1 }
        }
      }
    ]);

    // Get previous month stats
    const previousMonthStart = new Date(thirtyDaysAgo);
    previousMonthStart.setDate(previousMonthStart.getDate() - 30);
    const previousMonthStats = await Order.aggregate([
      {
        $match: {
          createdAt: {
            $gte: previousMonthStart,
            $lt: thirtyDaysAgo
          }
        }
      },
      {
        $group: {
          _id: null,
          revenue: { $sum: '$total' },
          orderCount: { $sum: 1 }
        }
      }
    ]);

    // Get user stats
    const currentUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });
    const previousUsers = await User.countDocuments({
      createdAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      }
    });

    // Get product stats
    const currentProducts = await Product.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });
    const previousProducts = await Product.countDocuments({
      createdAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      }
    });

    // Get newsletter stats
    const currentNewsletterSubscribers = await Newsletter.countDocuments({
      subscribedAt: { $gte: thirtyDaysAgo },
      status: 'active'
    });
    const previousNewsletterSubscribers = await Newsletter.countDocuments({
      subscribedAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      },
      status: 'active'
    });
    const totalActiveSubscribers = await Newsletter.countDocuments({ status: 'active' });

    // Get gift card stats
    const currentGiftCards = await GiftCard.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });
    const previousGiftCards = await GiftCard.countDocuments({
      createdAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      }
    });
    const totalGiftCards = await GiftCard.countDocuments();

    // Get coupon stats
    const currentCoupons = await Coupon.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });
    const previousCoupons = await Coupon.countDocuments({
      createdAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      }
    });
    const totalCoupons = await Coupon.countDocuments();

    // Get store stats
    const currentStores = await Store.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });
    const previousStores = await Store.countDocuments({
      createdAt: {
        $gte: previousMonthStart,
        $lt: thirtyDaysAgo
      }
    });
    const totalStores = await Store.countDocuments();

    // Calculate percentage changes
    const calculateChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const currentMonthRevenue = currentMonthStats[0]?.revenue || 0;
    const previousMonthRevenue = previousMonthStats[0]?.revenue || 0;
    const currentMonthOrders = currentMonthStats[0]?.orderCount || 0;
    const previousMonthOrders = previousMonthStats[0]?.orderCount || 0;

    const stats = {
      revenue: {
        value: `₹${currentMonthRevenue.toFixed(2)}`,
        change: calculateChange(currentMonthRevenue, previousMonthRevenue)
      },
      orders: {
        value: currentMonthOrders,
        change: calculateChange(currentMonthOrders, previousMonthOrders)
      },
      users: {
        value: await User.countDocuments(),
        change: calculateChange(currentUsers, previousUsers)
      },
      products: {
        value: await Product.countDocuments(),
        change: calculateChange(currentProducts, previousProducts)
      },
      newsletter: {
        value: totalActiveSubscribers,
        change: calculateChange(currentNewsletterSubscribers, previousNewsletterSubscribers)
      },
      giftCards: {
        value: totalGiftCards,
        change: calculateChange(currentGiftCards, previousGiftCards)
      },
      coupons: {
        value: totalCoupons,
        change: calculateChange(currentCoupons, previousCoupons)
      },
      stores: {
        value: totalStores,
        change: calculateChange(currentStores, previousStores)
      }
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}