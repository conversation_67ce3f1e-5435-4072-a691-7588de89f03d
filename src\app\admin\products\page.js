'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import {
  FiSearch,
  FiFilter,
  FiCheck,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiArrowUp,
  FiArrowDown,
  FiX,
  FiArrowLeft
} from 'react-icons/fi';
import Image from 'next/image';
import Link from 'next/link';
export default function ProductsManagement() {
  const router = useRouter();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [stockFilter, setStockFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [sortConfig, setSortConfig] = useState({ field: 'createdAt', direction: 'desc' });
  const [stats, setStats] = useState({
    total: 0,
    inStock: 0,
    lowStock: 0,
    outOfStock: 0
  });

  const categories = [
    'Living Room',
    'Bedroom',
    'Dining Room',
    'Office',
    'Outdoor',
    'Kitchen',
    'Bathroom',
    'Storage',
    'Decor'
  ];

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [products]);

  const calculateStats = () => {
    const newStats = products.reduce((acc, product) => {
      acc.total++;
      if (product.stock === 0) acc.outOfStock++;
      else if (product.stock <= 5) acc.lowStock++;
      else acc.inStock++;
      return acc;
    }, { total: 0, inStock: 0, lowStock: 0, outOfStock: 0 });
    setStats(newStats);
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data);
      } else {
        throw new Error('Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!productToDelete) return;

    try {
      const response = await fetch(`/api/products`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: productToDelete._id })
      });

      if (response.ok) {
        toast.success('Product deleted successfully');
        setProducts(products.filter(p => p._id !== productToDelete._id));
        setSelectedProducts(selectedProducts.filter(id => id !== productToDelete._id));
      } else {
        const data = await response.json();
        throw new Error(data.error || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error(error.message || 'Failed to delete product');
    } finally {
      setShowDeleteModal(false);
      setProductToDelete(null);
    }
  };

  const toggleProductSelection = (productId) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleBulkDelete = async () => {
    if (!selectedProducts.length) return;

    try {
      const response = await fetch('/api/products/bulk-delete', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: selectedProducts })
      });

      if (response.ok) {
        toast.success('Selected products deleted');
        setProducts(products.filter(p => !selectedProducts.includes(p._id)));
        setSelectedProducts([]);
      } else {
        throw new Error('Failed to delete selected products');
      }
    } catch (error) {
      toast.error(error.message);
    }
  };



  const handleSort = (field) => {
    setSortConfig(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };



  const filteredProducts = () => {
    let filtered = [...products];

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(product =>
        product.category.toLowerCase() === categoryFilter.toLowerCase()
      );
    }

    // Apply stock filter
    if (stockFilter === 'out-of-stock') {
      filtered = filtered.filter(product => product.stock === 0);
    } else if (stockFilter === 'low-stock') {
      filtered = filtered.filter(product => product.stock > 0 && product.stock <= 5);
    } else if (stockFilter === 'in-stock') {
      filtered = filtered.filter(product => product.stock > 5);
    }

    // Advanced search with relevance scoring
    if (searchQuery.trim()) {
      const searchTerm = searchQuery.toLowerCase().trim();
      const searchTerms = searchTerm.split(' ');

      filtered = filtered.filter(product => {
        const productName = product.name.toLowerCase();
        const productCategory = product.category.toLowerCase();
        const productDescription = product.description.toLowerCase();

        // Calculate relevance score for sorting
        let score = 0;

        // Exact category match (highest priority)
        if (productCategory === searchTerm) score += 100;
        if (productCategory.includes(searchTerm)) score += 50;

        // Exact product name match (high priority)
        if (productName === searchTerm) score += 80;
        if (productName.includes(searchTerm)) score += 40;

        // Individual terms matching (lower priority)
        const matchesAllTerms = searchTerms.every(term =>
          productName.includes(term) ||
          productCategory.includes(term) ||
          productDescription.includes(term)
        );

        if (matchesAllTerms) score += 20;

        // Store the score on the product object
        product._searchScore = score;

        return score > 0;
      });

      // Sort by search relevance score
      filtered.sort((a, b) => b._searchScore - a._searchScore);

      return filtered;
    }

    // Apply sorting if no search query
    const direction = sortConfig.direction === 'asc' ? 1 : -1;
    filtered.sort((a, b) => {
      if (sortConfig.field === 'price') {
        return (a.price - b.price) * direction;
      }
      if (sortConfig.field === 'stock') {
        return (a.stock - b.stock) * direction;
      }
      if (sortConfig.field === 'createdAt') {
        return (new Date(a.createdAt) - new Date(b.createdAt)) * direction;
      }
      return 0;
    });

    return filtered;
  };

  const formatPrice = (price) => {
    return `₹${Number(price).toLocaleString('en-IN')}`;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="px-6 py-4">
          {/* Back Button */}
          <div className="mb-4">
            <button
              onClick={() => router.push('/admin')}
              className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
            >
              <FiArrowLeft className="mr-1.5" />
              BACK TO ADMIN
            </button>
          </div>

          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-light tracking-tight">PRODUCTS</h1>
            <div className="flex items-center gap-4">

              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <FiFilter size={20} />
              </button>
              <Link
                href="/admin/products/new"
                className="flex items-center px-4 py-2 bg-black text-white hover:bg-gray-900"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                New Product
              </Link>
            </div>
          </div>

          {/* Product Stats */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="p-4 bg-gray-50">
              <p className="text-sm text-gray-500">Total Products</p>
              <p className="text-2xl font-medium">{stats.total}</p>
            </div>
            <div className="p-4 bg-gray-50">
              <p className="text-sm text-gray-500">In Stock</p>
              <p className="text-2xl font-medium">{stats.inStock}</p>
            </div>
            <div className="p-4 bg-orange-50">
              <p className="text-sm text-orange-500">Low Stock</p>
              <p className="text-2xl font-medium text-orange-500">{stats.lowStock}</p>
            </div>
            <div className="p-4 bg-red-50">
              <p className="text-sm text-red-500">Out of Stock</p>
              <p className="text-2xl font-medium text-red-500">{stats.outOfStock}</p>
            </div>

          </div>

          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-10 py-2 bg-gray-50 border-none rounded-none focus:ring-0 text-sm"
            />
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="px-6 py-3 border-t border-gray-100 bg-gray-50">
            <div className="space-y-4">
              {/* Category Filter */}
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500 w-24">Category:</span>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setCategoryFilter('all')}
                    className={`px-3 py-1 text-sm ${
                      categoryFilter === 'all'
                        ? 'bg-black text-white'
                        : 'bg-white text-black border border-gray-200'
                    }`}
                  >
                    All
                  </button>
                  {categories.map(category => (
                    <button
                      key={category}
                      onClick={() => setCategoryFilter(category)}
                      className={`px-3 py-1 text-sm ${
                        categoryFilter === category
                          ? 'bg-black text-white'
                          : 'bg-white text-black border border-gray-200'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Stock Filter */}
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500 w-24">Stock Status:</span>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setStockFilter('all')}
                    className={`px-3 py-1 text-sm ${
                      stockFilter === 'all'
                        ? 'bg-black text-white'
                        : 'bg-white text-black border border-gray-200'
                    }`}
                  >
                    All
                  </button>
                  <button
                    onClick={() => setStockFilter('in-stock')}
                    className={`px-3 py-1 text-sm ${
                      stockFilter === 'in-stock'
                        ? 'bg-black text-white'
                        : 'bg-white text-black border border-gray-200'
                    }`}
                  >
                    In Stock
                  </button>
                  <button
                    onClick={() => setStockFilter('low-stock')}
                    className={`px-3 py-1 text-sm ${
                      stockFilter === 'low-stock'
                        ? 'bg-orange-500 text-white'
                        : 'bg-white text-orange-500 border border-orange-200'
                    }`}
                  >
                    Low Stock
                  </button>
                  <button
                    onClick={() => setStockFilter('out-of-stock')}
                    className={`px-3 py-1 text-sm ${
                      stockFilter === 'out-of-stock'
                        ? 'bg-red-500 text-white'
                        : 'bg-white text-red-500 border border-red-200'
                    }`}
                  >
                    Out of Stock
                  </button>
                </div>
              </div>

              {/* Sort Options */}
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500 w-24">Sort By:</span>
                <div className="flex gap-4">
                  <button
                    onClick={() => handleSort('createdAt')}
                    className="flex items-center gap-1 text-sm hover:text-gray-900"
                  >
                    Date Added
                    {sortConfig.field === 'createdAt' && (
                      sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
                    )}
                  </button>
                  <button
                    onClick={() => handleSort('price')}
                    className="flex items-center gap-1 text-sm hover:text-gray-900"
                  >
                    Price
                    {sortConfig.field === 'price' && (
                      sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
                    )}
                  </button>
                  <button
                    onClick={() => handleSort('stock')}
                    className="flex items-center gap-1 text-sm hover:text-gray-900"
                  >
                    Stock
                    {sortConfig.field === 'stock' && (
                      sortConfig.direction === 'asc' ? <FiArrowUp /> : <FiArrowDown />
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Products Grid */}
      <div className="p-6">
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((n) => (
              <div key={n} className="bg-gray-50 aspect-square animate-pulse" />
            ))}
          </div>
        ) : filteredProducts().length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No products found</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts().map((product) => (
              <div
                key={product._id}
                className="group relative bg-gray-50"
              >
                {/* Checkbox */}
                <div
                  onClick={() => toggleProductSelection(product._id)}
                  className={`absolute top-4 left-4 z-10 w-5 h-5 border ${
                    selectedProducts.includes(product._id)
                      ? 'bg-black border-black'
                      : 'border-gray-200 bg-white'
                  } cursor-pointer flex items-center justify-center`}
                >
                  {selectedProducts.includes(product._id) && (
                    <FiCheck className="text-white" />
                  )}
                </div>



                {/* Image */}
                <div className="aspect-square relative overflow-hidden">
                  <Image
                    src={product.images[0] || '/placeholder.jpg'}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />

                  {/* Hover Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-4">
                    <Link
                      href={`/admin/products/${product._id}/edit`}
                      className="p-2 bg-white text-black hover:bg-gray-100"
                    >
                      <FiEdit2 size={20} />
                    </Link>
                    <button
                      onClick={() => handleDeleteClick(product)}
                      className="p-2 bg-white text-black hover:bg-gray-100"
                    >
                      <FiTrash2 size={20} />
                    </button>
                  </div>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <h3 className="text-sm font-medium">{product.name}</h3>
                  <p className="text-sm text-gray-500 mt-1">{product.category}</p>
                  <div className="mt-2 flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {formatPrice(product.price)}
                      {product.discount > 0 && (
                        <span className="ml-2 text-xs text-green-600">-{product.discount}%</span>
                      )}
                    </span>
                    <span className={`text-sm ${
                      product.stock === 0
                        ? 'text-red-500 font-medium'
                        : product.stock <= 5
                          ? 'text-orange-500 font-medium'
                          : 'text-gray-500'
                    }`}>
                      Stock: {product.stock}
                      {product.stock === 0
                        ? ' (Out)'
                        : product.stock <= 5
                          ? ' (Low)'
                          : ''}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {selectedProducts.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
          <div className="max-w-screen-xl mx-auto flex items-center justify-between">
            <span className="text-sm">
              {selectedProducts.length} products selected
            </span>
            <div className="flex gap-4">

              <button
                onClick={() => setSelectedProducts([])}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkDelete}
                className="px-4 py-2 text-sm bg-black text-white hover:bg-gray-900"
              >
                Delete Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-medium mb-4">Delete Product</h3>
            <p className="text-gray-500 mb-6">
              Are you sure you want to delete this product? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 text-sm border border-gray-200 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 text-sm bg-red-600 text-white hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
