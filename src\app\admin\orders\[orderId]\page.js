'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import {
  FiArrowLeft,
  FiPackage,
  FiTruck,
  FiCheckCircle,
  FiClock,
  FiAlertCircle,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiDollarSign,
  FiCreditCard,
  FiPrinter,
  FiDownload,
  FiEdit,
  FiMessageSquare
} from 'react-icons/fi';

export default function AdminOrderDetailPage({ params }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { user } = useAuth();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [note, setNote] = useState('');
  const [notes, setNotes] = useState([]);
  const [notesLoading, setNotesLoading] = useState(false);
  const [addingNote, setAddingNote] = useState(false);
  const [trackingInfo, setTrackingInfo] = useState({
    trackingNumber: '',
    trackingCompany: '',
    trackingUrl: ''
  });
  const [trackingUpdateLoading, setTrackingUpdateLoading] = useState(false);
  const [history, setHistory] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  useEffect(() => {
    if (!user || user.role !== 'admin') {
      router.push('/login');
      return;
    }

    fetchOrder();
  }, [user, resolvedParams.orderId, router]);

  useEffect(() => {
    if (order) {
      fetchOrderNotes();
      fetchOrderHistory();
    }
  }, [order?._id]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/orders/${resolvedParams.orderId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Order not found');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to view this order');
        } else {
          throw new Error('Failed to fetch order');
        }
      }

      const data = await response.json();
      setOrder(data);

      // Initialize tracking info from order data
      if (data.trackingNumber || data.trackingCompany || data.trackingUrl) {
        setTrackingInfo({
          trackingNumber: data.trackingNumber || '',
          trackingCompany: data.trackingCompany || '',
          trackingUrl: data.trackingUrl || ''
        });
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (newStatus) => {
    try {
      setStatusUpdateLoading(true);
      const response = await fetch(`/api/admin/orders/${order._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      toast.success(`Order status updated to ${newStatus}`);
      fetchOrder(); // Refresh order data
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error(error.message);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  const handleTrackingUpdate = async () => {
    try {
      setTrackingUpdateLoading(true);
      const response = await fetch(`/api/admin/orders/${order._id}/tracking`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(trackingInfo)
      });

      if (!response.ok) {
        throw new Error('Failed to update tracking information');
      }

      toast.success('Tracking information updated');
      fetchOrder(); // Refresh order data
    } catch (error) {
      console.error('Error updating tracking information:', error);
      toast.error(error.message);
    } finally {
      setTrackingUpdateLoading(false);
    }
  };

  const fetchOrderNotes = async () => {
    try {
      setNotesLoading(true);
      const response = await fetch(`/api/admin/orders/${order._id}/notes`);

      if (!response.ok) {
        throw new Error('Failed to fetch order notes');
      }

      const data = await response.json();
      setNotes(data);
    } catch (error) {
      console.error('Error fetching order notes:', error);
      toast.error('Failed to load order notes');
    } finally {
      setNotesLoading(false);
    }
  };

  const handleAddNote = async () => {
    if (!note.trim()) return;

    try {
      setAddingNote(true);
      const response = await fetch(`/api/admin/orders/${order._id}/notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: note,
          isInternal: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to add note');
      }

      const newNote = await response.json();
      setNotes(prevNotes => [newNote, ...prevNotes]);
      setNote('');
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error(error.message);
    } finally {
      setAddingNote(false);
    }
  };

  const handleDeleteNote = async (noteId) => {
    if (!confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/orders/${order._id}/notes`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ noteId })
      });

      if (!response.ok) {
        throw new Error('Failed to delete note');
      }

      setNotes(prevNotes => prevNotes.filter(note => note._id !== noteId));
      toast.success('Note deleted successfully');
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error(error.message);
    }
  };

  const fetchOrderHistory = async () => {
    try {
      setHistoryLoading(true);
      const response = await fetch(`/api/admin/orders/${order._id}/history`);

      if (!response.ok) {
        throw new Error('Failed to fetch order history');
      }

      const data = await response.json();
      setHistory(data);
    } catch (error) {
      console.error('Error fetching order history:', error);
      toast.error('Failed to load order history');
    } finally {
      setHistoryLoading(false);
    }
  };

  const formatPrice = (price) => {
    // Handle undefined, null, or NaN values
    if (price === undefined || price === null || isNaN(price)) {
      return '0';
    }
    return Number(price).toLocaleString('en-IN');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusBadgeClass = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDotColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-400';
      case 'processing':
        return 'bg-blue-400';
      case 'shipped':
        return 'bg-indigo-400';
      case 'delivered':
        return 'bg-green-400';
      case 'cancelled':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto"></div>
          <p className="mt-4 text-sm text-gray-500">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <h1 className="mt-4 text-xl font-medium text-gray-900">Error</h1>
          <p className="mt-2 text-sm text-gray-500">{error}</p>
          <button
            onClick={() => router.push('/admin/orders')}
            className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <h1 className="mt-4 text-xl font-medium text-gray-900">Order Not Found</h1>
          <p className="mt-2 text-sm text-gray-500">The order you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => router.push('/admin/orders')}
            className="mt-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-20 px-4 md:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/admin/orders')}
            className="flex items-center text-[10px] md:text-xs tracking-[0.2em] hover:opacity-50 transition-opacity"
          >
            <FiArrowLeft className="mr-1.5" />
            BACK TO ORDERS
          </button>
        </div>

        {/* Order Header */}
        <div className="mb-8 md:mb-12 flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-xl md:text-2xl font-light tracking-wide mb-2">ORDER DETAILS</h1>
            <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 text-sm text-black/70">
              <p>Order ID: {order._id}</p>
              {order.orderNumber && <p>Order #: {order.orderNumber}</p>}
              <p>Placed on {formatDate(order.createdAt)}</p>
            </div>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <button
              onClick={() => window.print()}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              <FiPrinter className="mr-1.5" />
              Print
            </button>
            <button
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
            >
              <FiDownload className="mr-1.5" />
              Download
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Order Details */}
          <div className="lg:col-span-2 space-y-8">
            {/* Status Section */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order Status</h2>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(order.status)}`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                  <span className="text-sm text-gray-500">Last updated: {formatDate(order.updatedAt)}</span>
                </div>

                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleStatusUpdate('pending')}
                    disabled={order.status === 'pending' || statusUpdateLoading}
                    className={`px-3 py-1.5 text-xs font-medium rounded ${
                      order.status === 'pending'
                        ? 'bg-yellow-100 text-yellow-800 cursor-not-allowed'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Pending
                  </button>
                  <button
                    onClick={() => handleStatusUpdate('processing')}
                    disabled={order.status === 'processing' || statusUpdateLoading}
                    className={`px-3 py-1.5 text-xs font-medium rounded ${
                      order.status === 'processing'
                        ? 'bg-blue-100 text-blue-800 cursor-not-allowed'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Processing
                  </button>
                  <button
                    onClick={() => handleStatusUpdate('shipped')}
                    disabled={order.status === 'shipped' || statusUpdateLoading}
                    className={`px-3 py-1.5 text-xs font-medium rounded ${
                      order.status === 'shipped'
                        ? 'bg-indigo-100 text-indigo-800 cursor-not-allowed'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Shipped
                  </button>
                  <button
                    onClick={() => handleStatusUpdate('delivered')}
                    disabled={order.status === 'delivered' || statusUpdateLoading}
                    className={`px-3 py-1.5 text-xs font-medium rounded ${
                      order.status === 'delivered'
                        ? 'bg-green-100 text-green-800 cursor-not-allowed'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Delivered
                  </button>
                  <button
                    onClick={() => handleStatusUpdate('cancelled')}
                    disabled={order.status === 'cancelled' || statusUpdateLoading}
                    className={`px-3 py-1.5 text-xs font-medium rounded ${
                      order.status === 'cancelled'
                        ? 'bg-red-100 text-red-800 cursor-not-allowed'
                        : 'border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Cancelled
                  </button>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order Items</h2>
              </div>
              <div className="divide-y divide-gray-200">
                {order.items.map((item, index) => (
                  <div key={index} className="p-6 flex items-start gap-4">
                    <div className="w-20 h-20 relative flex-shrink-0 bg-gray-50">
                      {item.image && (
                        <Image
                          src={item.image}
                          alt={item.name}
                          fill
                          className="object-cover object-center"
                        />
                      )}
                    </div>
                    <div className="flex-grow">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                        <div>
                          <h3 className="text-sm font-medium">{item.name}</h3>
                          <p className="text-xs text-gray-500 mt-1">Quantity: {item.quantity}</p>
                          {item.product && (
                            <p className="text-xs text-gray-500">
                              Product ID: {typeof item.product === 'object' ? item.product._id : item.product}
                            </p>
                          )}
                          {item.product && typeof item.product === 'object' && (
                            <p className="text-xs text-gray-500">
                              Current Stock: {item.product.stock}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">₹{formatPrice(item.price * item.quantity)}</p>
                          <p className="text-xs text-gray-500">₹{formatPrice(item.price)} each</p>
                          {item.discount > 0 && (
                            <p className="text-xs text-green-600">{item.discount}% OFF</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Shipping Information */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Shipping Information</h2>
              </div>
              <div className="p-6">
                <div className="mb-6">
                  <p className="text-sm text-gray-500 mb-1">Shipping Address</p>
                  <p className="text-sm font-medium whitespace-pre-line">{order.shippingAddress}</p>
                </div>

                <div className="border-t border-gray-100 pt-6">
                  <h3 className="text-sm font-medium mb-4">Tracking Information</h3>

                  {order.trackingNumber ? (
                    <div className="mb-4 space-y-2">
                      <div className="flex items-start gap-2">
                        <p className="text-sm text-gray-500 w-32">Tracking Number:</p>
                        <p className="text-sm font-medium">{order.trackingNumber}</p>
                      </div>
                      {order.trackingCompany && (
                        <div className="flex items-start gap-2">
                          <p className="text-sm text-gray-500 w-32">Carrier:</p>
                          <p className="text-sm font-medium">{order.trackingCompany}</p>
                        </div>
                      )}
                      {order.trackingUrl && (
                        <div className="flex items-start gap-2">
                          <p className="text-sm text-gray-500 w-32">Track Package:</p>
                          <a
                            href={order.trackingUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline"
                          >
                            View Tracking
                          </a>
                        </div>
                      )}
                      {order.shippedAt && (
                        <div className="flex items-start gap-2">
                          <p className="text-sm text-gray-500 w-32">Shipped Date:</p>
                          <p className="text-sm">{formatDate(order.shippedAt)}</p>
                        </div>
                      )}
                      {order.deliveredAt && (
                        <div className="flex items-start gap-2">
                          <p className="text-sm text-gray-500 w-32">Delivered Date:</p>
                          <p className="text-sm">{formatDate(order.deliveredAt)}</p>
                        </div>
                      )}

                      <button
                        onClick={() => {
                          setTrackingInfo({
                            trackingNumber: order.trackingNumber || '',
                            trackingCompany: order.trackingCompany || '',
                            trackingUrl: order.trackingUrl || ''
                          });
                          document.getElementById('edit-tracking-form').classList.remove('hidden');
                        }}
                        className="mt-2 text-xs text-black hover:underline"
                      >
                        Edit Tracking Information
                      </button>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic mb-4">No tracking information available</p>
                  )}

                  <div id="edit-tracking-form" className={order.trackingNumber ? 'hidden' : ''}>
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="trackingNumber" className="block text-xs text-gray-500 mb-1">
                          Tracking Number
                        </label>
                        <input
                          id="trackingNumber"
                          type="text"
                          value={trackingInfo.trackingNumber}
                          onChange={(e) => setTrackingInfo(prev => ({ ...prev, trackingNumber: e.target.value }))}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-black"
                          placeholder="Enter tracking number"
                        />
                      </div>

                      <div>
                        <label htmlFor="trackingCompany" className="block text-xs text-gray-500 mb-1">
                          Shipping Carrier
                        </label>
                        <input
                          id="trackingCompany"
                          type="text"
                          value={trackingInfo.trackingCompany}
                          onChange={(e) => setTrackingInfo(prev => ({ ...prev, trackingCompany: e.target.value }))}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-black"
                          placeholder="e.g., FedEx, UPS, USPS"
                        />
                      </div>

                      <div>
                        <label htmlFor="trackingUrl" className="block text-xs text-gray-500 mb-1">
                          Tracking URL (optional)
                        </label>
                        <input
                          id="trackingUrl"
                          type="text"
                          value={trackingInfo.trackingUrl}
                          onChange={(e) => setTrackingInfo(prev => ({ ...prev, trackingUrl: e.target.value }))}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-1 focus:ring-black"
                          placeholder="https://..."
                        />
                      </div>

                      <div className="flex gap-2">
                        <button
                          onClick={handleTrackingUpdate}
                          disabled={!trackingInfo.trackingNumber || trackingUpdateLoading}
                          className="px-4 py-2 bg-black text-white text-sm hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed"
                        >
                          {trackingUpdateLoading ? 'Saving...' : 'Save Tracking Info'}
                        </button>

                        {order.trackingNumber && (
                          <button
                            onClick={() => {
                              document.getElementById('edit-tracking-form').classList.add('hidden');
                            }}
                            className="px-4 py-2 border border-gray-300 text-sm hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Information */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Payment Information</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Payment Method</p>
                    <p className="text-sm font-medium">
                      {order.paymentMethod === 'cod' ? 'Cash on Delivery' : order.paymentMethod.toUpperCase()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Payment Status</p>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getPaymentStatusBadgeClass(order.paymentStatus)}`}>
                      {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                    </span>
                  </div>
                  {order.paymentId && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Payment ID</p>
                      <p className="text-sm font-medium">{order.paymentId}</p>
                    </div>
                  )}
                  {order.giftCardCode && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Gift Card</p>
                      <p className="text-sm font-medium">{order.giftCardCode}</p>
                      <p className="text-xs text-gray-500">Amount: ₹{formatPrice(order.giftCardAmount || 0)}</p>
                    </div>
                  )}
                  {order.couponCode && (
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Coupon</p>
                      <p className="text-sm font-medium">{order.couponCode}</p>
                      <p className="text-xs text-gray-500">Discount: ₹{formatPrice(order.couponDiscount || 0)}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Order History */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order History</h2>
              </div>
              <div className="p-6">
                {historyLoading ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black"></div>
                  </div>
                ) : history.length === 0 ? (
                  <p className="text-sm text-gray-500 italic">No history available for this order.</p>
                ) : (
                  <div className="relative">
                    {/* Timeline line */}
                    <div className="absolute left-2.5 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                    <div className="space-y-6 relative">
                      {history.map((item) => (
                        <div key={item._id} className="ml-10 relative">
                          {/* Timeline dot */}
                          <div className={`absolute -left-10 mt-1.5 w-5 h-5 rounded-full border-2 border-white ${getStatusDotColor(item.status)}`}></div>

                          <div>
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-0.5 text-xs rounded-full ${getStatusBadgeClass(item.status)}`}>
                                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                              </span>
                              {item.previousStatus && item.previousStatus !== item.status && (
                                <span className="text-xs text-gray-500">
                                  from {item.previousStatus.charAt(0).toUpperCase() + item.previousStatus.slice(1)}
                                </span>
                              )}
                            </div>

                            {item.note && (
                              <p className="text-sm mt-1">{item.note}</p>
                            )}

                            <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                              <span>{formatDate(item.updatedAt)}</span>
                              <span>•</span>
                              <span>by {item.updatedBy?.name || 'Admin'}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Order Notes */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order Notes</h2>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <div className="flex">
                    <input
                      type="text"
                      value={note}
                      onChange={(e) => setNote(e.target.value)}
                      placeholder="Add a note about this order..."
                      className="flex-grow px-4 py-2 border border-gray-300 rounded-l-sm focus:outline-none focus:ring-1 focus:ring-black text-sm"
                    />
                    <button
                      onClick={handleAddNote}
                      disabled={!note.trim() || addingNote}
                      className="px-4 py-2 bg-black text-white rounded-r-sm hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-sm"
                    >
                      {addingNote ? 'Adding...' : 'Add Note'}
                    </button>
                  </div>
                </div>

                {notesLoading ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black"></div>
                  </div>
                ) : notes.length === 0 ? (
                  <p className="text-sm text-gray-500 italic">No notes for this order yet.</p>
                ) : (
                  <div className="space-y-4">
                    {notes.map((note) => (
                      <div key={note._id} className="bg-gray-50 p-4 rounded-sm">
                        <div className="flex justify-between items-start">
                          <p className="text-sm">{note.text}</p>
                          <button
                            onClick={() => handleDeleteNote(note._id)}
                            className="text-gray-400 hover:text-red-500 ml-2"
                            title="Delete note"
                          >
                            <FiAlertCircle size={16} />
                          </button>
                        </div>
                        <div className="flex justify-between mt-2 text-xs text-gray-500">
                          <span>{note.createdBy?.name || 'Admin'}</span>
                          <span>{formatDate(note.createdAt)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Customer & Summary */}
          <div className="space-y-8">
            {/* Customer Information */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Customer Information</h2>
              </div>
              <div className="p-6">
                {order.user ? (
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <FiUser className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Name</p>
                        <p className="text-sm font-medium">{order.user.name}</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <FiMail className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="text-sm font-medium">{order.user.email}</p>
                      </div>
                    </div>
                    {order.user.phone && (
                      <div className="flex items-start gap-3">
                        <FiPhone className="text-gray-400 mt-0.5" />
                        <div>
                          <p className="text-sm text-gray-500">Phone</p>
                          <p className="text-sm font-medium">{order.user.phone}</p>
                        </div>
                      </div>
                    )}
                    <div className="flex items-start gap-3">
                      <FiMapPin className="text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm text-gray-500">Shipping Address</p>
                        <p className="text-sm font-medium whitespace-pre-line">{order.shippingAddress}</p>
                      </div>
                    </div>
                    <div className="pt-4 border-t border-gray-200">
                      <Link
                        href={`/admin/users/${order.user._id}`}
                        className="text-sm text-black hover:underline flex items-center"
                      >
                        View Customer Profile
                        <FiArrowLeft className="ml-1 rotate-180" />
                      </Link>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">Customer information not available</p>
                )}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white border border-gray-200 rounded-sm shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-sm font-medium text-gray-900">Order Summary</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Subtotal</span>
                    <span>₹{formatPrice(order.subtotal)}</span>
                  </div>

                  {order.couponDiscount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Coupon Discount</span>
                      <span className="text-green-600">-₹{formatPrice(order.couponDiscount)}</span>
                    </div>
                  )}

                  {order.giftCardAmount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Gift Card</span>
                      <span className="text-green-600">-₹{formatPrice(order.giftCardAmount)}</span>
                    </div>
                  )}

                  {order.shippingCost > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Shipping</span>
                      <span>₹{formatPrice(order.shippingCost)}</span>
                    </div>
                  )}

                  <div className="pt-3 border-t border-gray-200 flex justify-between text-sm font-medium">
                    <span>Total</span>
                    <span>₹{formatPrice(order.total)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
