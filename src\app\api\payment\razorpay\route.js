import { NextResponse } from 'next/server';
import Razorpay from 'razorpay';
import { getSessionUser } from '@/lib/auth';
import { connectToDB } from '@/lib/mongodb';
import crypto from 'crypto';

// Initialize Razorpay with your key_id and key_secret
// Log the environment variables to debug (redacted for security)
console.log('Razorpay API Keys:', {
  key_id_exists: !!process.env.RAZORPAY_KEY_ID,
  key_secret_exists: !!process.env.RAZORPAY_KEY_SECRET,
  key_id_prefix: process.env.RAZORPAY_KEY_ID ? process.env.RAZORPAY_KEY_ID.substring(0, 8) + '...' : 'undefined',
});

// Initialize Razorpay instance only if keys are available
let razorpay;

// Function to get or create Razorpay instance
const getRazorpayInstance = () => {
  if (!razorpay && process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET) {
    razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    });
  }
  return razorpay;
};

export async function POST(request) {
  try {
    console.log('Razorpay API: POST request received');

    // Check authentication
    const user = await getSessionUser();
    if (!user) {
      console.log('Razorpay API: Authentication failed');
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDB();
    console.log('Razorpay API: Connected to database');

    // Parse request body
    const body = await request.json();
    console.log('Razorpay API: Request body:', body);
    const { amount, currency = 'INR', receipt, notes = {} } = body;

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      console.log('Razorpay API: Invalid amount:', amount);
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Create Razorpay order
    const options = {
      amount: Math.round(amount * 100), // Razorpay expects amount in paise (1 INR = 100 paise)
      currency,
      receipt,
      notes: {
        ...notes,
        userId: user._id.toString(),
      },
    };

    console.log('Razorpay API: Creating order with options:', options);

    try {
      // Get Razorpay instance
      const razorpayInstance = getRazorpayInstance();

      // Verify Razorpay instance is properly initialized
      if (!razorpayInstance) {
        console.error('Razorpay API: Missing API keys');
        return NextResponse.json(
          { error: 'Payment provider configuration error. Please contact support.' },
          { status: 500 }
        );
      }

      console.log('Razorpay API: Creating order with options:', {
        ...options,
        amount: options.amount,
        currency: options.currency,
        receipt: options.receipt
      });

      // Create Razorpay order
      const order = await razorpayInstance.orders.create(options);
      console.log('Razorpay API: Order created successfully:', {
        id: order.id,
        amount: order.amount,
        currency: order.currency
      });

      // Return order details
      return NextResponse.json({
        id: order.id,
        amount: order.amount,
        currency: order.currency,
        key: razorpayInstance.key_id,
      });
    } catch (razorpayError) {
      console.error('Razorpay API: Error creating order:', {
        name: razorpayError.name,
        message: razorpayError.message,
        stack: razorpayError.stack
      });

      // Check for specific error types
      if (razorpayError.message && razorpayError.message.includes('authentication')) {
        return NextResponse.json(
          { error: 'Payment provider authentication failed. Please contact support.' },
          { status: 500 }
        );
      }

      // Check for missing API keys
      if (razorpayError.message && razorpayError.message.includes('key_id')) {
        return NextResponse.json(
          { error: 'Payment provider not configured. Please contact support.' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: `Razorpay error: ${razorpayError.message}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Razorpay API: Unexpected error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create payment order' },
      { status: 500 }
    );
  }
}

// Verify Razorpay payment signature
export async function PUT(request) {
  try {
    // Parse request body
    const body = await request.json();
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return NextResponse.json(
        { error: 'Missing required payment verification parameters' },
        { status: 400 }
      );
    }

    // Get Razorpay instance
    const razorpayInstance = getRazorpayInstance();

    // Check if Razorpay is properly initialized
    if (!razorpayInstance) {
      console.error('Razorpay API: Missing API keys during verification');
      return NextResponse.json(
        { error: 'Payment provider configuration error. Please contact support.' },
        { status: 500 }
      );
    }

    // Verify signature
    const generated_signature = crypto
      .createHmac('sha256', razorpayInstance.key_secret)
      .update(`${razorpay_order_id}|${razorpay_payment_id}`)
      .digest('hex');

    if (generated_signature !== razorpay_signature) {
      return NextResponse.json(
        { error: 'Invalid payment signature' },
        { status: 400 }
      );
    }

    // Payment is valid
    return NextResponse.json({
      success: true,
      paymentId: razorpay_payment_id,
    });
  } catch (error) {
    console.error('Razorpay verification error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to verify payment' },
      { status: 500 }
    );
  }
}
