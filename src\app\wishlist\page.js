'use client';

import { useCart } from '@/context/CartContext';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiX, FiShoppingBag } from 'react-icons/fi';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from 'react-hot-toast';

function WishlistPage() {
  const router = useRouter();
  const { wishlist, removeFromWishlist, addToCart, isLoggedIn } = useCart();
  const [addingToCart, setAddingToCart] = useState({});
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
    setLoading(false);
  }, []);

  const handleAddToCart = async (product) => {
    // Prevent adding out-of-stock items
    if (product.stock === 0) {
      toast.error('This product is out of stock');
      return;
    }

    setAddingToCart(prev => ({ ...prev, [product._id]: true }));
    try {
      const success = await addToCart(product);
      if (success) {
        toast.success('Added to cart');
      } else {
        toast.error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error(error.message || 'Failed to add to cart');
    } finally {
      setAddingToCart(prev => ({ ...prev, [product._id]: false }));
    }
  };

  const handleRemoveFromWishlist = async (productId) => {
    try {
      const success = await removeFromWishlist(productId);
      if (success) {
        toast.success('Removed from wishlist');
      } else {
        toast.error('Failed to remove from wishlist');
      }
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast.error('Failed to remove from wishlist');
    }
  };

  if (!mounted || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-sm tracking-[0.2em]">LOADING...</div>
      </div>
    );
  }

  if (wishlist.length === 0) {
    return (
      <div className="min-h-screen bg-white pt-20">
        <div className="max-w-6xl mx-auto px-4 xl:px-0 py-16">
          <div className="text-center">
            <h2 className="text-xl font-light mb-6">YOUR WISHLIST IS EMPTY</h2>
            <p className="text-sm text-gray-600 mb-10 max-w-lg mx-auto">
              Save your favorite items to create your personal selection.
            </p>
            <Link
              href="/products"
              className="inline-block px-8 py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200"
            >
              Shop New Collection
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="max-w-6xl mx-auto px-4 xl:px-0 py-12">
        <h1 className="text-2xl font-light mb-2 text-center">WISHLIST</h1>
        <p className="text-sm text-center text-gray-500 mb-10">
          {wishlist.length} {wishlist.length === 1 ? 'item' : 'items'}
        </p>

        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-x-2 gap-y-4 sm:gap-x-6 sm:gap-y-16">
          {wishlist.map((product) => (
            <div key={product._id} className="group">
              {/* Image Container */}
              <div
                className="relative aspect-square mb-2 sm:mb-4 cursor-pointer overflow-hidden"
                onClick={() => router.push(`/products/${product._id}`)}
              >
                <Image
                  src={product.images[0]}
                  alt={product.name}
                  fill
                  className="object-cover object-center transition-transform duration-700 group-hover:scale-105"
                  sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                />

                {/* Discount Tag - Top Left Corner */}
                {product.discount > 0 && (
                  <div className="absolute top-2 left-2 bg-black text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    {product.discount}% Off
                  </div>
                )}

                {/* Stock Status Tags - Top Right Corner */}
                {product.stock === 0 ? (
                  <div className="absolute top-2 right-2 bg-red-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    Out of Stock
                  </div>
                ) : product.stock <= 5 && (
                  <div className="absolute top-2 right-2 bg-amber-500 text-white text-[10px] sm:text-xs px-2 sm:px-3 py-1 uppercase tracking-wider">
                    Low Stock
                  </div>
                )}

                {/* Quick Add Button - Hidden on Mobile */}
                <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 py-2 sm:py-3 px-2 sm:px-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 hidden sm:flex justify-between items-center">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFromWishlist(product._id);
                    }}
                    className="text-xs uppercase tracking-wider flex items-center gap-2 hover:opacity-70"
                  >
                    <FiX className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>Remove</span>
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(product);
                    }}
                    disabled={addingToCart[product._id] || product.stock === 0}
                    className="text-xs uppercase tracking-wider flex items-center gap-2 hover:opacity-70 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiShoppingBag className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span>{product.stock === 0 ? 'Out of Stock' : 'Add'}</span>
                  </button>
                </div>
              </div>

              {/* Product Info - Compact for Mobile */}
              <div className="space-y-0.5 sm:space-y-1 px-0.5">
                <h3 className="text-xs sm:text-sm font-light line-clamp-1 sm:line-clamp-2">
                  {product.name}
                </h3>
                <div className="flex justify-between items-center">
                  <p className="text-[10px] sm:text-xs text-gray-500 line-clamp-1">
                    {product.category}
                  </p>
                  {product.stock === 0 ? (
                    <p className="text-[10px] sm:text-xs font-medium text-red-500 bg-red-50 px-1.5 py-0.5 rounded">
                      Out of Stock
                    </p>
                  ) : product.stock <= 5 && (
                    <p className="text-[10px] sm:text-xs font-medium text-amber-500 bg-amber-50 px-1.5 py-0.5 rounded">
                      Low Stock: {product.stock}
                    </p>
                  )}
                </div>
                <div className="flex items-baseline gap-1.5 sm:gap-2 mt-0.5 sm:mt-1">
                  {product.discount > 0 ? (
                    <>
                      <p className="text-xs sm:text-sm font-light">
                        ₹{((product.price * (100 - product.discount)) / 100).toLocaleString('en-IN')}
                      </p>
                      <p className="text-[10px] sm:text-xs text-gray-500 line-through">
                        ₹{product.price.toLocaleString('en-IN')}
                      </p>
                    </>
                  ) : (
                    <p className="text-xs sm:text-sm font-light">
                      ₹{product.price.toLocaleString('en-IN')}
                    </p>
                  )}
                </div>
              </div>

              {/* Mobile Quick Actions */}
              <div className="flex justify-between items-center mt-2 sm:hidden border-t border-gray-100 pt-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFromWishlist(product._id);
                  }}
                  className="flex items-center gap-1.5 px-3 py-2 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-colors"
                >
                  <FiX className="w-4 h-4" />
                  <span className="text-[10px] uppercase tracking-wider">Remove</span>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToCart(product);
                  }}
                  disabled={addingToCart[product._id] || product.stock === 0}
                  className="flex items-center gap-1.5 px-3 py-2 rounded-full hover:bg-gray-50 active:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiShoppingBag className="w-4 h-4" />
                  <span className="text-[10px] uppercase tracking-wider">
                    {addingToCart[product._id] ? 'Adding...' : (product.stock === 0 ? 'Out of Stock' : 'Add')}
                  </span>
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Link
            href="/products"
            className="inline-block px-8 py-3 border border-black text-black uppercase text-xs tracking-wider hover:bg-black hover:text-white transition-colors duration-200"
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
}

export default WishlistPage;